{"version": 3, "file": "file-selector.umd.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../src/file.ts", "../../src/file-selector.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "export const COMMON_MIME_TYPES = new Map([\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types\n    ['aac', 'audio/aac'],\n    ['abw', 'application/x-abiword'],\n    ['arc', 'application/x-freearc'],\n    ['avif', 'image/avif'],\n    ['avi', 'video/x-msvideo'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['bin', 'application/octet-stream'],\n    ['bmp', 'image/bmp'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['cda', 'application/x-cdf'],\n    ['csh', 'application/x-csh'],\n    ['css', 'text/css'],\n    ['csv', 'text/csv'],\n    ['doc', 'application/msword'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['epub', 'application/epub+zip'],\n    ['gz', 'application/gzip'],\n    ['gif', 'image/gif'],\n    ['heic', 'image/heic'],\n    ['heif', 'image/heif'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['ico', 'image/vnd.microsoft.icon'],\n    ['ics', 'text/calendar'],\n    ['jar', 'application/java-archive'],\n    ['jpeg', 'image/jpeg'],\n    ['jpg', 'image/jpeg'],\n    ['js', 'text/javascript'],\n    ['json', 'application/json'],\n    ['jsonld', 'application/ld+json'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mjs', 'text/javascript'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mpeg', 'video/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['opus', 'audio/opus'],\n    ['otf', 'font/otf'],\n    ['png', 'image/png'],\n    ['pdf', 'application/pdf'],\n    ['php', 'application/x-httpd-php'],\n    ['ppt', 'application/vnd.ms-powerpoint'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['rar', 'application/vnd.rar'],\n    ['rtf', 'application/rtf'],\n    ['sh', 'application/x-sh'],\n    ['svg', 'image/svg+xml'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['tar', 'application/x-tar'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['ts', 'video/mp2t'],\n    ['ttf', 'font/ttf'],\n    ['txt', 'text/plain'],\n    ['vsd', 'application/vnd.visio'],\n    ['wav', 'audio/wav'],\n    ['weba', 'audio/webm'],\n    ['webm', 'video/webm'],\n    ['webp', 'image/webp'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xml', 'application/xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['zip', 'application/zip'],\n    ['7z', 'application/x-7z-compressed'],\n\n    // Others\n    ['mkv', 'video/x-matroska'],\n    ['mov', 'video/quicktime'],\n    ['msg', 'application/vnd.ms-outlook']\n]);\n\n\nexport function toFileWithPath(file: FileWithPath, path?: string): FileWithPath {\n    const f = withMimeType(file);\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        const {webkitRelativePath} = file as FileWithWebkitPath;\n        Object.defineProperty(f, 'path', {\n            value: typeof path === 'string'\n                ? path\n                // If <input webkitdirectory> is set,\n                // the File will have a {webkitRelativePath} property\n                // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n                : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n                    ? webkitRelativePath\n                    : file.name,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n\n    return f;\n}\n\ninterface DOMFile extends Blob {\n    readonly lastModified: number;\n    readonly name: string;\n}\n\nexport interface FileWithPath extends DOMFile {\n    readonly path?: string;\n}\n\ninterface FileWithWebkitPath extends File {\n    readonly webkitRelativePath?: string;\n}\n\nfunction withMimeType(file: FileWithPath) {\n    const {name} = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop()!.toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n\n    return file;\n}\n", "import {FileWithPath, toFileWithPath} from './file';\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db'  // Windows\n];\n\n\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport async function fromEvent(evt: Event | any): Promise<(FileWithPath | DataTransferItem)[]> {\n    if (isObject<DragEvent>(evt) && isDataTransfer(evt)) {\n        return getDataTransferFiles(evt.dataTransfer, evt.type);\n    } else if (isChangeEvt(evt)) {\n        return getInputFiles(evt);\n    } else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n        return getFsHandleFiles(evt)\n    }\n    return [];\n}\n\nfunction isDataTransfer(value: any): value is DataTransfer {\n    return isObject(value.dataTransfer);\n}\n\nfunction isChangeEvt(value: any): value is Event {\n    return isObject<Event>(value) && isObject(value.target);\n}\n\nfunction isObject<T>(v: any): v is T {\n    return typeof v === 'object' && v !== null\n}\n\nfunction getInputFiles(evt: Event) {\n    return fromList<FileWithPath>((evt.target as HTMLInputElement).files).map(file => toFileWithPath(file));\n}\n\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nasync function getFsHandleFiles(handles: any[]) {\n    const files = await Promise.all(handles.map(h => h.getFile()));\n    return files.map(file => toFileWithPath(file));\n}\n\n\nasync function getDataTransferFiles(dt: DataTransfer | null, type: string) {\n    if (dt === null) {\n        return [];\n    }\n\n    // IE11 does not support dataTransfer.items\n    // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n    if (dt.items) {\n        const items = fromList<DataTransferItem>(dt.items)\n            .filter(item => item.kind === 'file');\n        // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n        // only 'dragstart' and 'drop' has access to the data (source node)\n        if (type !== 'drop') {\n            return items;\n        }\n        const files = await Promise.all(items.map(toFilePromises));\n        return noIgnoredFiles(flatten<FileWithPath>(files));\n    }\n\n    return noIgnoredFiles(fromList<FileWithPath>(dt.files)\n        .map(file => toFileWithPath(file)));\n}\n\nfunction noIgnoredFiles(files: FileWithPath[]) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList<T>(items: DataTransferItemList | FileList | null): T[] {\n    if (items === null) {\n        return [];\n    }\n\n    const files = [];\n\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n\n    return files as any;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item: DataTransferItem) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n\n    const entry = item.webkitGetAsEntry();\n\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry) as any;\n    }\n\n    return fromDataTransferItem(item);\n}\n\nfunction flatten<T>(items: any[]): T[] {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\n\nfunction fromDataTransferItem(item: DataTransferItem) {\n    const file = item.getAsFile();\n    if (!file) {\n        return Promise.reject(`${item} is not a File`);\n    }\n    const fwp = toFileWithPath(file);\n    return Promise.resolve(fwp);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nasync function fromEntry(entry: any) {\n    return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry: any) {\n    const reader = entry.createReader();\n\n    return new Promise<FileArray[]>((resolve, reject) => {\n        const entries: Promise<FileValue[]>[] = [];\n\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries(async (batch: any[]) => {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = await Promise.all(entries);\n                        resolve(files);\n                    } catch (err) {\n                        reject(err);\n                    }\n                } else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n\n                    // Continue reading\n                    readEntries();\n                }\n            }, (err: any) => {\n                reject(err);\n            });\n        }\n\n        readEntries();\n    });\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nasync function fromFileEntry(entry: any) {\n    return new Promise<FileWithPath>((resolve, reject) => {\n        entry.file((file: FileWithPath) => {\n            const fwp = toFileWithPath(file, entry.fullPath);\n            resolve(fwp);\n        }, (err: any) => {\n            reject(err);\n        });\n    });\n}\n\n// Infinite type recursion\n// https://github.com/Microsoft/TypeScript/issues/3496#issuecomment-128553540\ninterface FileArray extends Array<FileValue> {}\ntype FileValue = FileWithPath\n    | FileArray[];\n"], "names": [], "mappings": ";;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AAqDA;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;IACtE,QAAQ,OAAO,CAAC,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AAyBD;IACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;AACD;IACO,SAAS,QAAQ,GAAG;IAC3B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd;;ICtJO,IAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC;;QAErC,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,KAAK,EAAE,uBAAuB,CAAC;QAChC,CAAC,KAAK,EAAE,uBAAuB,CAAC;QAChC,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,KAAK,EAAE,8BAA8B,CAAC;QACvC,CAAC,KAAK,EAAE,0BAA0B,CAAC;QACnC,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,IAAI,EAAE,oBAAoB,CAAC;QAC5B,CAAC,KAAK,EAAE,qBAAqB,CAAC;QAC9B,CAAC,KAAK,EAAE,mBAAmB,CAAC;QAC5B,CAAC,KAAK,EAAE,mBAAmB,CAAC;QAC5B,CAAC,KAAK,EAAE,UAAU,CAAC;QACnB,CAAC,KAAK,EAAE,UAAU,CAAC;QACnB,CAAC,KAAK,EAAE,oBAAoB,CAAC;QAC7B,CAAC,MAAM,EAAE,yEAAyE,CAAC;QACnF,CAAC,KAAK,EAAE,+BAA+B,CAAC;QACxC,CAAC,MAAM,EAAE,sBAAsB,CAAC;QAChC,CAAC,IAAI,EAAE,kBAAkB,CAAC;QAC1B,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,MAAM,EAAE,WAAW,CAAC;QACrB,CAAC,KAAK,EAAE,0BAA0B,CAAC;QACnC,CAAC,KAAK,EAAE,eAAe,CAAC;QACxB,CAAC,KAAK,EAAE,0BAA0B,CAAC;QACnC,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,KAAK,EAAE,YAAY,CAAC;QACrB,CAAC,IAAI,EAAE,iBAAiB,CAAC;QACzB,CAAC,MAAM,EAAE,kBAAkB,CAAC;QAC5B,CAAC,QAAQ,EAAE,qBAAqB,CAAC;QACjC,CAAC,KAAK,EAAE,YAAY,CAAC;QACrB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,KAAK,EAAE,YAAY,CAAC;QACrB,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,MAAM,EAAE,qCAAqC,CAAC;QAC/C,CAAC,KAAK,EAAE,iDAAiD,CAAC;QAC1D,CAAC,KAAK,EAAE,gDAAgD,CAAC;QACzD,CAAC,KAAK,EAAE,yCAAyC,CAAC;QAClD,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,KAAK,EAAE,UAAU,CAAC;QACnB,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,KAAK,EAAE,yBAAyB,CAAC;QAClC,CAAC,KAAK,EAAE,+BAA+B,CAAC;QACxC,CAAC,MAAM,EAAE,2EAA2E,CAAC;QACrF,CAAC,KAAK,EAAE,qBAAqB,CAAC;QAC9B,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,IAAI,EAAE,kBAAkB,CAAC;QAC1B,CAAC,KAAK,EAAE,eAAe,CAAC;QACxB,CAAC,KAAK,EAAE,+BAA+B,CAAC;QACxC,CAAC,KAAK,EAAE,mBAAmB,CAAC;QAC5B,CAAC,KAAK,EAAE,YAAY,CAAC;QACrB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,IAAI,EAAE,YAAY,CAAC;QACpB,CAAC,KAAK,EAAE,UAAU,CAAC;QACnB,CAAC,KAAK,EAAE,YAAY,CAAC;QACrB,CAAC,KAAK,EAAE,uBAAuB,CAAC;QAChC,CAAC,KAAK,EAAE,WAAW,CAAC;QACpB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,MAAM,EAAE,YAAY,CAAC;QACtB,CAAC,MAAM,EAAE,WAAW,CAAC;QACrB,CAAC,OAAO,EAAE,YAAY,CAAC;QACvB,CAAC,OAAO,EAAE,uBAAuB,CAAC;QAClC,CAAC,KAAK,EAAE,0BAA0B,CAAC;QACnC,CAAC,MAAM,EAAE,mEAAmE,CAAC;QAC7E,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,KAAK,EAAE,iCAAiC,CAAC;QAC1C,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,IAAI,EAAE,6BAA6B,CAAC;;QAGrC,CAAC,KAAK,EAAE,kBAAkB,CAAC;QAC3B,CAAC,KAAK,EAAE,iBAAiB,CAAC;QAC1B,CAAC,KAAK,EAAE,4BAA4B,CAAC;KACxC,CAAC,CAAC;aAGa,cAAc,CAAC,IAAkB,EAAE,IAAa;QAC5D,IAAM,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YACrB,IAAA,kBAAkB,GAAI,IAA0B,mBAA9B,CAA+B;YACxD,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE;gBAC7B,KAAK,EAAE,OAAO,IAAI,KAAK,QAAQ;sBACzB,IAAI;;;;sBAIJ,OAAO,kBAAkB,KAAK,QAAQ,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC;0BACnE,kBAAkB;0BAClB,IAAI,CAAC,IAAI;gBACnB,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,KAAK;gBACnB,UAAU,EAAE,IAAI;aACnB,CAAC,CAAC;SACN;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAeD,SAAS,YAAY,CAAC,IAAkB;QAC7B,IAAA,IAAI,GAAI,IAAI,KAAR,CAAS;QACpB,IAAM,YAAY,GAAG,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAE1D,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;iBACtB,GAAG,EAAG,CAAC,WAAW,EAAE,CAAC;YAC1B,IAAM,IAAI,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACxC,IAAI,IAAI,EAAE;gBACN,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;oBAChC,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,KAAK;oBACf,YAAY,EAAE,KAAK;oBACnB,UAAU,EAAE,IAAI;iBACnB,CAAC,CAAC;aACN;SACJ;QAED,OAAO,IAAI,CAAC;IAChB;;IC1IA,IAAM,eAAe,GAAG;;QAEpB,WAAW;QACX,WAAW;KACd,CAAC;IAGF;;;;;;;;;;aAUsB,SAAS,CAAC,GAAgB;;;gBAC5C,IAAI,QAAQ,CAAY,GAAG,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,EAAE;oBACjD,sBAAO,oBAAoB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,EAAC;iBAC3D;qBAAM,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;oBACzB,sBAAO,aAAa,CAAC,GAAG,CAAC,EAAC;iBAC7B;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,SAAS,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,GAAA,CAAC,EAAE;oBACzG,sBAAO,gBAAgB,CAAC,GAAG,CAAC,EAAA;iBAC/B;gBACD,sBAAO,EAAE,EAAC;;;KACb;IAED,SAAS,cAAc,CAAC,KAAU;QAC9B,OAAO,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,WAAW,CAAC,KAAU;QAC3B,OAAO,QAAQ,CAAQ,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED,SAAS,QAAQ,CAAI,CAAM;QACvB,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAA;IAC9C,CAAC;IAED,SAAS,aAAa,CAAC,GAAU;QAC7B,OAAO,QAAQ,CAAgB,GAAG,CAAC,MAA2B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,cAAc,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;IAC5G,CAAC;IAED;IACA,SAAe,gBAAgB,CAAC,OAAc;;;;;4BAC5B,qBAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,EAAE,GAAA,CAAC,CAAC,EAAA;;wBAAxD,KAAK,GAAG,SAAgD;wBAC9D,sBAAO,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,cAAc,CAAC,IAAI,CAAC,GAAA,CAAC,EAAC;;;;KAClD;IAGD,SAAe,oBAAoB,CAAC,EAAuB,EAAE,IAAY;;;;;;wBACrE,IAAI,EAAE,KAAK,IAAI,EAAE;4BACb,sBAAO,EAAE,EAAC;yBACb;6BAIG,EAAE,CAAC,KAAK,EAAR,wBAAQ;wBACF,KAAK,GAAG,QAAQ,CAAmB,EAAE,CAAC,KAAK,CAAC;6BAC7C,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,KAAK,MAAM,GAAA,CAAC,CAAC;;;wBAG1C,IAAI,IAAI,KAAK,MAAM,EAAE;4BACjB,sBAAO,KAAK,EAAC;yBAChB;wBACa,qBAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,EAAA;;wBAApD,KAAK,GAAG,SAA4C;wBAC1D,sBAAO,cAAc,CAAC,OAAO,CAAe,KAAK,CAAC,CAAC,EAAC;4BAGxD,sBAAO,cAAc,CAAC,QAAQ,CAAe,EAAE,CAAC,KAAK,CAAC;6BACjD,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,cAAc,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC,EAAC;;;;KAC3C;IAED,SAAS,cAAc,CAAC,KAAqB;QACzC,OAAO,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAA,CAAC,CAAC;IAC3E,CAAC;IAED;IACA;IACA;IACA;IACA,SAAS,QAAQ,CAAI,KAA6C;QAC9D,IAAI,KAAK,KAAK,IAAI,EAAE;YAChB,OAAO,EAAE,CAAC;SACb;QAED,IAAM,KAAK,GAAG,EAAE,CAAC;;QAGjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpB;QAED,OAAO,KAAY,CAAC;IACxB,CAAC;IAED;IACA,SAAS,cAAc,CAAC,IAAsB;QAC1C,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;YAC7C,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;SACrC;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;;QAKtC,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE;YAC5B,OAAO,YAAY,CAAC,KAAK,CAAQ,CAAC;SACrC;QAED,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,SAAS,OAAO,CAAI,KAAY;QAC5B,OAAO,KAAK,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,gBAC7B,GAAG,GACF,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,KACtD,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;IAED,SAAS,oBAAoB,CAAC,IAAsB;QAChD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,OAAO,CAAC,MAAM,CAAI,IAAI,mBAAgB,CAAC,CAAC;SAClD;QACD,IAAM,GAAG,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;IACA,SAAe,SAAS,CAAC,KAAU;;;gBAC/B,sBAAO,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,EAAC;;;KACzE;IAED;IACA,SAAS,YAAY,CAAC,KAAU;QAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;QAEpC,OAAO,IAAI,OAAO,CAAc,UAAC,OAAO,EAAE,MAAM;YAC5C,IAAM,OAAO,GAA2B,EAAE,CAAC;YAE3C,SAAS,WAAW;gBAApB,iBAsBC;;;gBAnBG,MAAM,CAAC,WAAW,CAAC,UAAO,KAAY;;;;;qCAC9B,CAAC,KAAK,CAAC,MAAM,EAAb,wBAAa;;;;gCAGK,qBAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAA;;gCAAlC,KAAK,GAAG,SAA0B;gCACxC,OAAO,CAAC,KAAK,CAAC,CAAC;;;;gCAEf,MAAM,CAAC,KAAG,CAAC,CAAC;;;;gCAGV,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;gCAChD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;gCAGpB,WAAW,EAAE,CAAC;;;;;qBAErB,EAAE,UAAC,GAAQ;oBACR,MAAM,CAAC,GAAG,CAAC,CAAC;iBACf,CAAC,CAAC;aACN;YAED,WAAW,EAAE,CAAC;SACjB,CAAC,CAAC;IACP,CAAC;IAED;IACA,SAAe,aAAa,CAAC,KAAU;;;gBACnC,sBAAO,IAAI,OAAO,CAAe,UAAC,OAAO,EAAE,MAAM;wBAC7C,KAAK,CAAC,IAAI,CAAC,UAAC,IAAkB;4BAC1B,IAAM,GAAG,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;4BACjD,OAAO,CAAC,GAAG,CAAC,CAAC;yBAChB,EAAE,UAAC,GAAQ;4BACR,MAAM,CAAC,GAAG,CAAC,CAAC;yBACf,CAAC,CAAC;qBACN,CAAC,EAAC;;;;;;;;;;;;;"}