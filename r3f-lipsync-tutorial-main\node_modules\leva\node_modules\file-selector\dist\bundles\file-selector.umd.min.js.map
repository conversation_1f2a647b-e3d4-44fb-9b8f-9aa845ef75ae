{"version": 3, "file": "file-selector.umd.min.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../src/file.ts", "../../src/file-selector.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "export const COMMON_MIME_TYPES = new Map([\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types\n    ['aac', 'audio/aac'],\n    ['abw', 'application/x-abiword'],\n    ['arc', 'application/x-freearc'],\n    ['avif', 'image/avif'],\n    ['avi', 'video/x-msvideo'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['bin', 'application/octet-stream'],\n    ['bmp', 'image/bmp'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['cda', 'application/x-cdf'],\n    ['csh', 'application/x-csh'],\n    ['css', 'text/css'],\n    ['csv', 'text/csv'],\n    ['doc', 'application/msword'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['epub', 'application/epub+zip'],\n    ['gz', 'application/gzip'],\n    ['gif', 'image/gif'],\n    ['heic', 'image/heic'],\n    ['heif', 'image/heif'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['ico', 'image/vnd.microsoft.icon'],\n    ['ics', 'text/calendar'],\n    ['jar', 'application/java-archive'],\n    ['jpeg', 'image/jpeg'],\n    ['jpg', 'image/jpeg'],\n    ['js', 'text/javascript'],\n    ['json', 'application/json'],\n    ['jsonld', 'application/ld+json'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mjs', 'text/javascript'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mpeg', 'video/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['opus', 'audio/opus'],\n    ['otf', 'font/otf'],\n    ['png', 'image/png'],\n    ['pdf', 'application/pdf'],\n    ['php', 'application/x-httpd-php'],\n    ['ppt', 'application/vnd.ms-powerpoint'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['rar', 'application/vnd.rar'],\n    ['rtf', 'application/rtf'],\n    ['sh', 'application/x-sh'],\n    ['svg', 'image/svg+xml'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['tar', 'application/x-tar'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['ts', 'video/mp2t'],\n    ['ttf', 'font/ttf'],\n    ['txt', 'text/plain'],\n    ['vsd', 'application/vnd.visio'],\n    ['wav', 'audio/wav'],\n    ['weba', 'audio/webm'],\n    ['webm', 'video/webm'],\n    ['webp', 'image/webp'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xml', 'application/xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['zip', 'application/zip'],\n    ['7z', 'application/x-7z-compressed'],\n\n    // Others\n    ['mkv', 'video/x-matroska'],\n    ['mov', 'video/quicktime'],\n    ['msg', 'application/vnd.ms-outlook']\n]);\n\n\nexport function toFileWithPath(file: FileWithPath, path?: string): FileWithPath {\n    const f = withMimeType(file);\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        const {webkitRelativePath} = file as FileWithWebkitPath;\n        Object.defineProperty(f, 'path', {\n            value: typeof path === 'string'\n                ? path\n                // If <input webkitdirectory> is set,\n                // the File will have a {webkitRelativePath} property\n                // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n                : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n                    ? webkitRelativePath\n                    : file.name,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n\n    return f;\n}\n\ninterface DOMFile extends Blob {\n    readonly lastModified: number;\n    readonly name: string;\n}\n\nexport interface FileWithPath extends DOMFile {\n    readonly path?: string;\n}\n\ninterface FileWithWebkitPath extends File {\n    readonly webkitRelativePath?: string;\n}\n\nfunction withMimeType(file: FileWithPath) {\n    const {name} = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop()!.toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n\n    return file;\n}\n", "import {FileWithPath, toFileWithPath} from './file';\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db'  // Windows\n];\n\n\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport async function fromEvent(evt: Event | any): Promise<(FileWithPath | DataTransferItem)[]> {\n    if (isObject<DragEvent>(evt) && isDataTransfer(evt)) {\n        return getDataTransferFiles(evt.dataTransfer, evt.type);\n    } else if (isChangeEvt(evt)) {\n        return getInputFiles(evt);\n    } else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n        return getFsHandleFiles(evt)\n    }\n    return [];\n}\n\nfunction isDataTransfer(value: any): value is DataTransfer {\n    return isObject(value.dataTransfer);\n}\n\nfunction isChangeEvt(value: any): value is Event {\n    return isObject<Event>(value) && isObject(value.target);\n}\n\nfunction isObject<T>(v: any): v is T {\n    return typeof v === 'object' && v !== null\n}\n\nfunction getInputFiles(evt: Event) {\n    return fromList<FileWithPath>((evt.target as HTMLInputElement).files).map(file => toFileWithPath(file));\n}\n\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nasync function getFsHandleFiles(handles: any[]) {\n    const files = await Promise.all(handles.map(h => h.getFile()));\n    return files.map(file => toFileWithPath(file));\n}\n\n\nasync function getDataTransferFiles(dt: DataTransfer | null, type: string) {\n    if (dt === null) {\n        return [];\n    }\n\n    // IE11 does not support dataTransfer.items\n    // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n    if (dt.items) {\n        const items = fromList<DataTransferItem>(dt.items)\n            .filter(item => item.kind === 'file');\n        // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n        // only 'dragstart' and 'drop' has access to the data (source node)\n        if (type !== 'drop') {\n            return items;\n        }\n        const files = await Promise.all(items.map(toFilePromises));\n        return noIgnoredFiles(flatten<FileWithPath>(files));\n    }\n\n    return noIgnoredFiles(fromList<FileWithPath>(dt.files)\n        .map(file => toFileWithPath(file)));\n}\n\nfunction noIgnoredFiles(files: FileWithPath[]) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList<T>(items: DataTransferItemList | FileList | null): T[] {\n    if (items === null) {\n        return [];\n    }\n\n    const files = [];\n\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n\n    return files as any;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item: DataTransferItem) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n\n    const entry = item.webkitGetAsEntry();\n\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry) as any;\n    }\n\n    return fromDataTransferItem(item);\n}\n\nfunction flatten<T>(items: any[]): T[] {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\n\nfunction fromDataTransferItem(item: DataTransferItem) {\n    const file = item.getAsFile();\n    if (!file) {\n        return Promise.reject(`${item} is not a File`);\n    }\n    const fwp = toFileWithPath(file);\n    return Promise.resolve(fwp);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nasync function fromEntry(entry: any) {\n    return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry: any) {\n    const reader = entry.createReader();\n\n    return new Promise<FileArray[]>((resolve, reject) => {\n        const entries: Promise<FileValue[]>[] = [];\n\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries(async (batch: any[]) => {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = await Promise.all(entries);\n                        resolve(files);\n                    } catch (err) {\n                        reject(err);\n                    }\n                } else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n\n                    // Continue reading\n                    readEntries();\n                }\n            }, (err: any) => {\n                reject(err);\n            });\n        }\n\n        readEntries();\n    });\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nasync function fromFileEntry(entry: any) {\n    return new Promise<FileWithPath>((resolve, reject) => {\n        entry.file((file: FileWithPath) => {\n            const fwp = toFileWithPath(file, entry.fullPath);\n            resolve(fwp);\n        }, (err: any) => {\n            reject(err);\n        });\n    });\n}\n\n// Infinite type recursion\n// https://github.com/Microsoft/TypeScript/issues/3496#issuecomment-128553540\ninterface FileArray extends Array<FileValue> {}\ntype FileValue = FileWithPath\n    | FileArray[];\n"], "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__generator", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "this", "n", "v", "op", "TypeError", "call", "pop", "length", "push", "__read", "o", "m", "r", "i", "ar", "error", "COMMON_MIME_TYPES", "Map", "toFileWithPath", "file", "path", "name", "lastIndexOf", "type", "ext", "split", "toLowerCase", "get", "Object", "defineProperty", "writable", "configurable", "enumerable", "withMimeType", "webkitRelativePath", "FILES_TO_IGNORE", "isObject", "getInputFiles", "evt", "fromList", "target", "files", "map", "getFsHandleFiles", "handles", "all", "h", "getFile", "_a", "getDataTransferFiles", "dt", "items", "filter", "item", "kind", "toFilePromises", "noIgnoredFiles", "flatten", "indexOf", "webkitGetAsEntry", "fromDataTransferItem", "entry", "isDirectory", "fromDirEntry", "reduce", "acc", "arguments", "concat", "Array", "isArray", "getAsFile", "fwp", "fromEntry", "fromFileEntry", "reader", "createReader", "entries", "readEntries", "batch", "err_1", "err", "fullPath", "dataTransfer", "isChangeEvt", "every"], "mappings": ";;;;;;;;;;;;;;oFAmEO,SAASA,EAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,IAAW,MAAOG,GAAKL,EAAOK,IACpF,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiB,MAAEK,IAAW,MAAOG,GAAKL,EAAOK,IACvF,SAASF,EAAKI,GAJlB,IAAeL,EAIaK,EAAOC,KAAOT,EAAQQ,EAAOL,QAJ1CA,EAIyDK,EAAOL,MAJhDA,aAAiBN,EAAIM,EAAQ,IAAIN,GAAE,SAAUG,GAAWA,EAAQG,OAITO,KAAKR,EAAWK,GAClGH,GAAMN,EAAYA,EAAUa,MAAMhB,EAASC,GAAc,KAAKS,WAI/D,SAASO,EAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGC,EAAGC,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPJ,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,IAAOK,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEZ,KAAMkB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BT,EAAES,OAAOC,UAAY,WAAa,OAAOC,OAAUX,EACvJ,SAASM,EAAKM,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIjB,EAAG,MAAM,IAAIkB,UAAU,mCAC3B,KAAOd,OACH,GAAIJ,EAAI,EAAGC,IAAMC,EAAY,EAARe,EAAG,GAAShB,EAAU,OAAIgB,EAAG,GAAKhB,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAEiB,KAAKlB,GAAI,GAAKA,EAAEV,SAAWW,EAAIA,EAAEiB,KAAKlB,EAAGgB,EAAG,KAAKtB,KAAM,OAAOO,EAE3J,OADID,EAAI,EAAGC,IAAGe,EAAK,CAAS,EAARA,EAAG,GAAQf,EAAEb,QACzB4B,EAAG,IACP,KAAK,EAAG,KAAK,EAAGf,EAAIe,EAAI,MACxB,KAAK,EAAc,OAAXb,EAAEC,QAAgB,CAAEhB,MAAO4B,EAAG,GAAItB,MAAM,GAChD,KAAK,EAAGS,EAAEC,QAASJ,EAAIgB,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKb,EAAEI,IAAIY,MAAOhB,EAAEG,KAAKa,MAAO,SACxC,QACI,KAAMlB,EAAIE,EAAEG,MAAML,EAAIA,EAAEmB,OAAS,GAAKnB,EAAEA,EAAEmB,OAAS,KAAkB,IAAVJ,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEb,EAAI,EAAG,SACjG,GAAc,IAAVa,EAAG,MAAcf,GAAMe,EAAG,GAAKf,EAAE,IAAMe,EAAG,GAAKf,EAAE,IAAM,CAAEE,EAAEC,MAAQY,EAAG,GAAI,MAC9E,GAAc,IAAVA,EAAG,IAAYb,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIA,EAAIe,EAAI,MAC7D,GAAIf,GAAKE,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIE,EAAEI,IAAIc,KAAKL,GAAK,MACvDf,EAAE,IAAIE,EAAEI,IAAIY,MAChBhB,EAAEG,KAAKa,MAAO,SAEtBH,EAAKlB,EAAKoB,KAAKtC,EAASuB,GAC1B,MAAOZ,GAAKyB,EAAK,CAAC,EAAGzB,GAAIS,EAAI,UAAeD,EAAIE,EAAI,EACtD,GAAY,EAARe,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE5B,MAAO4B,EAAG,GAAKA,EAAG,QAAK,EAAQtB,MAAM,GArB9BL,CAAK,CAACyB,EAAGC,MAiDtD,SAASO,EAAOC,EAAGT,GACtB,IAAIU,EAAsB,mBAAXb,QAAyBY,EAAEZ,OAAOC,UACjD,IAAKY,EAAG,OAAOD,EACf,IAAmBE,EAAYlC,EAA3BmC,EAAIF,EAAEN,KAAKK,GAAOI,EAAK,GAC3B,IACI,WAAc,IAANb,GAAgBA,KAAM,MAAQW,EAAIC,EAAEpC,QAAQI,MAAMiC,EAAGN,KAAKI,EAAErC,OAExE,MAAOwC,GAASrC,EAAI,CAAEqC,MAAOA,WAEzB,IACQH,IAAMA,EAAE/B,OAAS8B,EAAIE,EAAU,SAAIF,EAAEN,KAAKQ,WAExC,GAAInC,EAAG,MAAMA,EAAEqC,OAE7B,OAAOD,EC/IJ,IAAME,EAAoB,IAAIC,IAAI,CAErC,CAAC,MAAO,aACR,CAAC,MAAO,yBACR,CAAC,MAAO,yBACR,CAAC,OAAQ,cACT,CAAC,MAAO,mBACR,CAAC,MAAO,gCACR,CAAC,MAAO,4BACR,CAAC,MAAO,aACR,CAAC,KAAM,sBACP,CAAC,MAAO,uBACR,CAAC,MAAO,qBACR,CAAC,MAAO,qBACR,CAAC,MAAO,YACR,CAAC,MAAO,YACR,CAAC,MAAO,sBACR,CAAC,OAAQ,2EACT,CAAC,MAAO,iCACR,CAAC,OAAQ,wBACT,CAAC,KAAM,oBACP,CAAC,MAAO,aACR,CAAC,OAAQ,cACT,CAAC,OAAQ,cACT,CAAC,MAAO,aACR,CAAC,OAAQ,aACT,CAAC,MAAO,4BACR,CAAC,MAAO,iBACR,CAAC,MAAO,4BACR,CAAC,OAAQ,cACT,CAAC,MAAO,cACR,CAAC,KAAM,mBACP,CAAC,OAAQ,oBACT,CAAC,SAAU,uBACX,CAAC,MAAO,cACR,CAAC,OAAQ,cACT,CAAC,MAAO,mBACR,CAAC,MAAO,cACR,CAAC,MAAO,aACR,CAAC,OAAQ,cACT,CAAC,OAAQ,uCACT,CAAC,MAAO,mDACR,CAAC,MAAO,kDACR,CAAC,MAAO,2CACR,CAAC,MAAO,aACR,CAAC,MAAO,aACR,CAAC,MAAO,mBACR,CAAC,OAAQ,cACT,CAAC,MAAO,YACR,CAAC,MAAO,aACR,CAAC,MAAO,mBACR,CAAC,MAAO,2BACR,CAAC,MAAO,iCACR,CAAC,OAAQ,6EACT,CAAC,MAAO,uBACR,CAAC,MAAO,mBACR,CAAC,KAAM,oBACP,CAAC,MAAO,iBACR,CAAC,MAAO,iCACR,CAAC,MAAO,qBACR,CAAC,MAAO,cACR,CAAC,OAAQ,cACT,CAAC,KAAM,cACP,CAAC,MAAO,YACR,CAAC,MAAO,cACR,CAAC,MAAO,yBACR,CAAC,MAAO,aACR,CAAC,OAAQ,cACT,CAAC,OAAQ,cACT,CAAC,OAAQ,cACT,CAAC,OAAQ,aACT,CAAC,QAAS,cACV,CAAC,QAAS,yBACV,CAAC,MAAO,4BACR,CAAC,OAAQ,qEACT,CAAC,MAAO,mBACR,CAAC,MAAO,mCACR,CAAC,MAAO,mBACR,CAAC,KAAM,+BAGP,CAAC,MAAO,oBACR,CAAC,MAAO,mBACR,CAAC,MAAO,yCAIIC,EAAeC,EAAoBC,GAC/C,IAAMlC,EAkCV,SAAsBiC,GACX,IAAAE,EAAQF,OAGf,GAFqBE,IAAmC,IAA3BA,EAAKC,YAAY,OAEzBH,EAAKI,KAAM,CAC5B,IAAMC,EAAMH,EAAKI,MAAM,KAClBnB,MAAOoB,cACNH,EAAOP,EAAkBW,IAAIH,GAC/BD,GACAK,OAAOC,eAAeV,EAAM,OAAQ,CAChC5C,MAAOgD,EACPO,UAAU,EACVC,cAAc,EACdC,YAAY,IAKxB,OAAOb,EApDGc,CAAad,GACvB,GAAsB,iBAAXjC,EAAEkC,KAAmB,CACrB,IAAAc,EAAsBf,qBAC7BS,OAAOC,eAAe3C,EAAG,OAAQ,CAC7BX,MAAuB,iBAAT6C,EACRA,EAI8B,iBAAvBc,GAAmCA,EAAmB3B,OAAS,EAClE2B,EACAf,EAAKE,KACfS,UAAU,EACVC,cAAc,EACdC,YAAY,IAIpB,OAAO9C,ECvGX,IAAMiD,EAAkB,CAEpB,YACA,aAiCJ,SAASC,EAAYlC,GACjB,MAAoB,iBAANA,GAAwB,OAANA,EAGpC,SAASmC,EAAcC,GACnB,OAAOC,EAAwBD,EAAIE,OAA4BC,OAAOC,KAAI,SAAAvB,GAAQ,OAAAD,EAAeC,MAIrG,SAAewB,EAAiBC,6FACd,SAAMzE,QAAQ0E,IAAID,EAAQF,KAAI,SAAAI,GAAK,OAAAA,EAAEC,sBACnD,SADcC,SACDN,KAAI,SAAAvB,GAAQ,OAAAD,EAAeC,cAI5C,SAAe8B,EAAqBC,EAAyB3B,mGACzD,OAAW,OAAP2B,KACO,IAKPA,EAAGC,OACGA,EAAQZ,EAA2BW,EAAGC,OACvCC,QAAO,SAAAC,GAAQ,MAAc,SAAdA,EAAKC,QAGZ,SAAT/B,KACO4B,MAEShF,QAAQ0E,IAAIM,EAAMT,IAAIa,mBAC1C,SAAOC,EAAeC,EADRT,mBAIlB,SAAOQ,EAAejB,EAAuBW,EAAGT,OAC3CC,KAAI,SAAAvB,GAAQ,OAAAD,EAAeC,eAGpC,SAASqC,EAAef,GACpB,OAAOA,EAAMW,QAAO,SAAAjC,GAAQ,OAAwC,IAAxCgB,EAAgBuB,QAAQvC,EAAKE,SAO7D,SAASkB,EAAYY,GACjB,GAAc,OAAVA,EACA,MAAO,GAMX,IAHA,IAAMV,EAAQ,GAGL5B,EAAI,EAAGA,EAAIsC,EAAM5C,OAAQM,IAAK,CACnC,IAAMM,EAAOgC,EAAMtC,GACnB4B,EAAMjC,KAAKW,GAGf,OAAOsB,EAIX,SAASc,EAAeF,GACpB,GAAqC,mBAA1BA,EAAKM,iBACZ,OAAOC,EAAqBP,GAGhC,IAAMQ,EAAQR,EAAKM,mBAKnB,OAAIE,GAASA,EAAMC,YACRC,EAAaF,GAGjBD,EAAqBP,GAGhC,SAASI,EAAWN,GAChB,OAAOA,EAAMa,QAAO,SAACC,EAAKxB,GAAU,OF0BjC,WACH,IAAK,IAAI3B,EAAK,GAAID,EAAI,EAAGA,EAAIqD,UAAU3D,OAAQM,IAC3CC,EAAKA,EAAGqD,OAAO1D,EAAOyD,UAAUrD,KACpC,OAAOC,GE5BAmD,EACCG,MAAMC,QAAQ5B,GAASgB,EAAQhB,GAAS,CAACA,MAC9C,IAGP,SAASmB,EAAqBP,GAC1B,IAAMlC,EAAOkC,EAAKiB,YAClB,IAAKnD,EACD,OAAOhD,QAAQE,OAAUgF,oBAE7B,IAAMkB,EAAMrD,EAAeC,GAC3B,OAAOhD,QAAQC,QAAQmG,GAI3B,SAAeC,EAAUX,sEACrB,SAAOA,EAAMC,YAAcC,EAAaF,GAASY,EAAcZ,UAInE,SAASE,EAAaF,GAClB,IAAMa,EAASb,EAAMc,eAErB,OAAO,IAAIxG,SAAqB,SAACC,EAASC,GACtC,IAAMuG,EAAkC,IAExC,SAASC,IAAT,WAGIH,EAAOG,aAAY,SAAOC,uGACjBA,EAAMvE,OAAP,6BAGkB,gCAAMpC,QAAQ0E,IAAI+B,kBAA1BnC,EAAQO,SACd5E,EAAQqE,kCAERpE,EAAO0G,mCAGL5B,EAAQhF,QAAQ0E,IAAIiC,EAAMpC,IAAI8B,IACpCI,EAAQpE,KAAK2C,GAGb0B,yCAEL,SAACG,GACA3G,EAAO2G,MAIfH,MAKR,SAAeJ,EAAcZ,sEACzB,SAAO,IAAI1F,SAAsB,SAACC,EAASC,GACvCwF,EAAM1C,MAAK,SAACA,GACR,IAAMoD,EAAMrD,EAAeC,EAAM0C,EAAMoB,UACvC7G,EAAQmG,MACT,SAACS,GACA3G,EAAO2G,qCAlKa1C,sEAC5B,OAAIF,EAAoBE,IAWjBF,EAXwCE,EAWzB4C,iBAVXjC,EAAqBX,EAAI4C,aAAc5C,EAAIf,OAa1D,SAAqBhD,GACjB,OAAO6D,EAAgB7D,IAAU6D,EAAS7D,EAAMiE,QAbrC2C,CAAY7C,MACZD,EAAcC,IACd8B,MAAMC,QAAQ/B,IAAQA,EAAI8C,OAAM,SAAA/B,GAAQ,MAAA,YAAaA,GAAgC,mBAAjBA,EAAKN,cACzEJ,EAAiBL,OAErB"}