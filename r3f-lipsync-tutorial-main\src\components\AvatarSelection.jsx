import React, { useState } from 'react';
import ImageUpload from './ImageUpload';

const AvatarSelection = ({ onAvatarChange, currentAvatar, customAvatars = [] }) => {
  const [selectedType, setSelectedType] = useState(currentAvatar?.type || 'default');
  const [isUploading, setIsUploading] = useState(false);

  const handleAvatarTypeChange = (type, avatarData = null) => {
    setSelectedType(type);
    
    const newAvatar = {
      type,
      data: avatarData,
      timestamp: Date.now()
    };

    if (onAvatarChange) {
      onAvatarChange(newAvatar);
    }
  };

  const handleCustomAvatarCreated = (avatarData) => {
    handleAvatarTypeChange('custom', avatarData);
  };

  const handleProcessingStart = () => {
    setIsUploading(true);
  };

  const handleProcessingEnd = () => {
    setIsUploading(false);
  };

  return (
    <div className="avatar-selection">
      <h3 style={{ margin: '0 0 15px 0', color: '#333', fontSize: '16px' }}>
        Choose Avatar
      </h3>
      
      <div className="avatar-options">
        <div
          className={`avatar-option ${selectedType === 'default' ? 'active' : ''}`}
          onClick={() => handleAvatarTypeChange('default')}
        >
          <div className="avatar-option-icon">🤖</div>
          <div className="avatar-option-text">Default</div>
        </div>
        
        <div
          className={`avatar-option ${selectedType === 'custom' ? 'active' : ''}`}
          onClick={() => {
            if (currentAvatar?.type === 'custom') {
              handleAvatarTypeChange('custom', currentAvatar.data);
            }
          }}
        >
          {currentAvatar?.type === 'custom' && currentAvatar?.data?.avatar ? (
            <>
              <img 
                src={currentAvatar.data.avatar} 
                alt="Custom avatar" 
                className="custom-avatar-preview"
              />
              <div className="avatar-option-text">Custom</div>
            </>
          ) : (
            <>
              <div className="avatar-option-icon">📷</div>
              <div className="avatar-option-text">Upload Photo</div>
            </>
          )}
        </div>
      </div>

      {(selectedType === 'custom' && !currentAvatar?.data) || isUploading ? (
        <ImageUpload
          onAvatarCreated={handleCustomAvatarCreated}
          onProcessingStart={handleProcessingStart}
          onProcessingEnd={handleProcessingEnd}
        />
      ) : null}

      {selectedType === 'custom' && currentAvatar?.data && !isUploading && (
        <div className="current-custom-avatar">
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '10px'
          }}>
            <span style={{ fontSize: '14px', color: '#333' }}>Current Custom Avatar</span>
            <button
              onClick={() => setSelectedType('upload-new')}
              style={{
                background: 'none',
                border: '1px solid #007bff',
                color: '#007bff',
                padding: '5px 10px',
                borderRadius: '5px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Upload New
            </button>
          </div>
          
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <img 
              src={currentAvatar.data.avatar} 
              alt="Current custom avatar" 
              style={{
                width: '80px',
                height: '80px',
                borderRadius: '8px',
                objectFit: 'cover',
                border: '2px solid #007bff'
              }}
            />
            <div style={{ fontSize: '12px', color: '#666' }}>
              <div>✅ Avatar ready</div>
              <div>🎭 Lip-sync enabled</div>
              <div>🎬 Animations active</div>
            </div>
          </div>
        </div>
      )}

      {selectedType === 'upload-new' && (
        <ImageUpload
          onAvatarCreated={handleCustomAvatarCreated}
          onProcessingStart={handleProcessingStart}
          onProcessingEnd={handleProcessingEnd}
        />
      )}
    </div>
  );
};

export default AvatarSelection;
