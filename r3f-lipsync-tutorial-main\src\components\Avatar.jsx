/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.2.3 public/models/646d9dcdc8a5f5bddbfac913.glb -o src/components/Avatar.jsx -r public
*/

import { useAnimations, useFBX, useGLTF, useTexture } from "@react-three/drei";
import { useFrame, useLoader } from "@react-three/fiber";
import { useControls } from "leva";
import React, { useEffect, useMemo, useRef, useState } from "react";
import * as THREE from "three";
import CONFIG from "../config";

const corresponding = {
  A: "viseme_PP",
  B: "viseme_kk",
  C: "viseme_I",
  D: "viseme_AA",
  E: "viseme_O",
  F: "viseme_U",
  G: "viseme_FF",
  H: "viseme_TH",
  X: "viseme_PP",
};

export function Avatar(props) {
  const { customAvatar } = props;

  const {
    playAudio,
    script,
    headFollow,
    smoothMorphTarget,
    morphTargetSmoothing,
    enableChatbot,
    straightHandsPosition,
  } = useControls({
    playAudio: false,
    headFollow: true,
    smoothMorphTarget: true,
    morphTargetSmoothing: 0.5,
    enableChatbot: true,
    straightHandsPosition: true, // Control for keeping hands in a straight position
    script: {
      value: "welcome",
      options: ["welcome", "pizzas"],
    },
  });

  // State for chatbot integration
  const [isTalking, setIsTalking] = useState(false);
  const [currentMessage, setCurrentMessage] = useState("");
  const [isThinking, setIsThinking] = useState(false);
  const [chatbotLipsync, setChatbotLipsync] = useState({ mouthCues: [] });
  const [speechStartTime, setSpeechStartTime] = useState(0);

  // Create a speech synthesis instance
  const speechSynthesis = useMemo(() => window.speechSynthesis, []);

  // Audio for predefined scripts
  const audio = useMemo(() => new Audio(`/audios/${script}.mp3`), [script]);
  const jsonFile = useLoader(THREE.FileLoader, `audios/${script}.json`);
  const lipsync = JSON.parse(jsonFile);

  // Function to handle new messages from the chatbot
  const handleNewMessage = (message) => {
    setCurrentMessage(message);
    if (CONFIG.VOICE_ENABLED) {
      speakResponse(message);
    }

    // Check if message starts with "Hey there" for greeting animation
    if (message.startsWith("Hey there")) {
      setAnimation("Greeting");
      return;
    }

    // Determine emotion based on message content
    const emotion = analyzeSentiment(message);
    if (emotion === "happy" || emotion === "excited") {
      setAnimation("Greeting");
    } else if (emotion === "sad" || emotion === "angry") {
      setAnimation("Angry");
    } else {
      setAnimation("Idle");
    }
  };

  // Function to generate lipsync data for text
  const generateLipsyncData = (text) => {
    // Estimate speech duration based on text length and speaking rate
    // Average speaking rate is about 150 words per minute, or 2.5 words per second
    // Average word length is about 5 characters
    const estimatedDuration = (text.length / 5) / 2.5;

    // Create a lipsync data structure similar to the JSON files
    const lipsyncData = {
      metadata: {
        duration: estimatedDuration
      },
      mouthCues: []
    };

    // Start with mouth closed
    lipsyncData.mouthCues.push({ start: 0, end: 0.04, value: "X" });

    // Split text into words
    const words = text.split(/\s+/);
    let currentTime = 0.04;

    words.forEach((word, wordIndex) => {
      // Average time per word
      const wordDuration = word.length * 0.07;

      // Process each character in the word
      for (let i = 0; i < word.length; i++) {
        const char = word[i].toLowerCase();
        const charDuration = 0.07; // Average duration per character
        let visemeValue = "B"; // Default viseme

        // Map characters to visemes (using the same mapping as in the JSON files)
        if ("aeiou".includes(char)) {
          // Vowels
          if ("ae".includes(char)) visemeValue = "D";
          else if ("i".includes(char)) visemeValue = "C";
          else if ("o".includes(char)) visemeValue = "E";
          else if ("u".includes(char)) visemeValue = "F";
        } else if ("bmp".includes(char)) {
          // Bilabial consonants
          visemeValue = "A";
        } else if ("fv".includes(char)) {
          // Labiodental consonants
          visemeValue = "G";
        } else if ("th".includes(char)) {
          // Dental consonants
          visemeValue = "H";
        } else {
          // Other consonants
          visemeValue = "B";
        }

        // Add mouth cue
        lipsyncData.mouthCues.push({
          start: currentTime,
          end: currentTime + charDuration,
          value: visemeValue
        });

        currentTime += charDuration;
      }

      // Add a pause between words (except for the last word)
      if (wordIndex < words.length - 1) {
        lipsyncData.mouthCues.push({
          start: currentTime,
          end: currentTime + 0.1,
          value: "X"
        });
        currentTime += 0.1;
      }
    });

    // End with mouth closed
    lipsyncData.mouthCues.push({
      start: currentTime,
      end: currentTime + 0.1,
      value: "X"
    });

    // Update the total duration
    lipsyncData.metadata.duration = currentTime + 0.1;

    return lipsyncData;
  };

  // Function to speak the response
  const speakResponse = (text) => {
    if (speechSynthesis.speaking) {
      speechSynthesis.cancel();
    }

    // Generate lipsync data for the text
    const lipsyncData = generateLipsyncData(text);
    setChatbotLipsync(lipsyncData);

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'en-US';
    utterance.rate = 1.0;
    utterance.pitch = 1.0;

    setIsTalking(true);

    // Handle speech events
    utterance.onstart = () => {
      setSpeechStartTime(Date.now());
    };

    utterance.onend = () => {
      setIsTalking(false);
      setAnimation("Idle");
    };

    speechSynthesis.speak(utterance);
  };

  // Function to analyze sentiment of text
  const analyzeSentiment = (text) => {
    const positive = /great|happy|excellent|glad|good|wonderful|fantastic/i;
    const negative = /sorry|sad|unfortunate|bad|wrong|error/i;
    const thinking = /think|consider|perhaps|maybe|might|could/i;
    const excited = /excited|amazing|awesome|wow|incredible|love|best/i;
    const surprised = /surprised|unexpected|whoa|really|oh my|no way/i;

    if (excited.test(text)) return "excited";
    if (surprised.test(text)) return "surprised";
    if (positive.test(text)) return "happy";
    if (negative.test(text)) return "sad";
    if (thinking.test(text)) return "thinking";
    return "neutral";
  };

  // Functions to handle thinking state
  const handleThinking = () => {
    setIsThinking(true);
    setAnimation("Idle");
  };

  const handleStopThinking = () => {
    setIsThinking(false);
  };

  useFrame(() => {
    // Reset all morph targets
    Object.values(corresponding).forEach((value) => {
      if (!smoothMorphTarget) {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary[value]
        ] = 0;
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary[value]
        ] = 0;
      } else {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary[value]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[value]
          ],
          0,
          morphTargetSmoothing
        );

        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary[value]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[value]
          ],
          0,
          morphTargetSmoothing
        );
      }
    });

    // Adjust hand position if straightHandsPosition is enabled
    if (straightHandsPosition) {
      // Adjust arms to be straight at the sides
      // The exact bone names depend on the model's skeleton structure

      // Common bone naming patterns to try
      const leftArmBones = [
        'mixamorigLeftArm', 'LeftArm', 'Left_arm', 'left_arm',
        'mixamorigLeftShoulder', 'LeftShoulder'
      ];

      const rightArmBones = [
        'mixamorigRightArm', 'RightArm', 'Right_arm', 'right_arm',
        'mixamorigRightShoulder', 'RightShoulder'
      ];

      const leftForearmBones = [
        'mixamorigLeftForeArm', 'LeftForeArm', 'Left_forearm', 'left_forearm'
      ];

      const rightForearmBones = [
        'mixamorigRightForeArm', 'RightForeArm', 'Right_forearm', 'right_forearm'
      ];

      // Try to find and adjust left arm
      for (const boneName of leftArmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0.1, // Slightly angled for natural position
            0.1  // Smoothing factor
          );
          nodes[boneName].rotation.x = THREE.MathUtils.lerp(
            nodes[boneName].rotation.x,
            0,   // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust right arm
      for (const boneName of rightArmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            -0.1, // Slightly angled for natural position (mirrored from left)
            0.1
          );
          nodes[boneName].rotation.x = THREE.MathUtils.lerp(
            nodes[boneName].rotation.x,
            0,    // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust left forearm
      for (const boneName of leftForearmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0,    // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust right forearm
      for (const boneName of rightForearmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0,    // Straight position
            0.1
          );
          break;
        }
      }
    }

    // Handle predefined audio playback
    if (playAudio && !audio.paused && !audio.ended) {
      const currentAudioTime = audio.currentTime;

      for (let i = 0; i < lipsync.mouthCues.length; i++) {
        const mouthCue = lipsync.mouthCues[i];
        if (
          currentAudioTime >= mouthCue.start &&
          currentAudioTime <= mouthCue.end
        ) {
          if (!smoothMorphTarget) {
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[
                corresponding[mouthCue.value]
              ]
            ] = 1;
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[
                corresponding[mouthCue.value]
              ]
            ] = 1;
          } else {
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[
                corresponding[mouthCue.value]
              ]
            ] = THREE.MathUtils.lerp(
              nodes.Wolf3D_Head.morphTargetInfluences[
                nodes.Wolf3D_Head.morphTargetDictionary[
                  corresponding[mouthCue.value]
                ]
              ],
              1,
              morphTargetSmoothing
            );
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[
                corresponding[mouthCue.value]
              ]
            ] = THREE.MathUtils.lerp(
              nodes.Wolf3D_Teeth.morphTargetInfluences[
                nodes.Wolf3D_Teeth.morphTargetDictionary[
                  corresponding[mouthCue.value]
                ]
              ],
              1,
              morphTargetSmoothing
            );
          }
          break;
        }
      }
    }
    // Handle chatbot talking
    else if (isTalking && enableChatbot) {
      // Use the same approach as the predefined audio playback
      const currentTime = (Date.now() - speechStartTime) / 1000;

      // Find the current mouth cue based on the elapsed time
      let currentMouthCue = null;

      for (let i = 0; i < chatbotLipsync.mouthCues.length; i++) {
        const mouthCue = chatbotLipsync.mouthCues[i];
        if (
          currentTime >= mouthCue.start &&
          currentTime <= mouthCue.end
        ) {
          currentMouthCue = mouthCue;
          break;
        }
      }

      // If we found a mouth cue, apply it
      if (currentMouthCue) {
        const visemeShape = corresponding[currentMouthCue.value];

        if (!smoothMorphTarget) {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = 1;
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = 1;
        } else {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
        }
      } else {
        // If we're past the end of the lipsync data, use a closed mouth
        const visemeShape = corresponding["X"];

        if (!smoothMorphTarget) {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = 1;
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = 1;
        } else {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
        }
      }
    }
    // If thinking, show a slight mouth movement
    else if (isThinking && enableChatbot) {
      if (!smoothMorphTarget) {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
        ] = 0.3;
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
        ] = 0.3;
      } else {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
          ],
          0.3,
          morphTargetSmoothing
        );
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
          ],
          0.3,
          morphTargetSmoothing
        );
      }
    }
  });

  useEffect(() => {
    nodes.Wolf3D_Head.morphTargetInfluences[
      nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
    ] = 1;
    nodes.Wolf3D_Teeth.morphTargetInfluences[
      nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
    ] = 1;
    if (playAudio) {
      audio.play();
      if (script === "welcome") {
        setAnimation("Greeting");
      } else {
        setAnimation("Angry");
      }
    } else {
      setAnimation("Idle");
      audio.pause();
    }
  }, [playAudio, script]);

  const { nodes, materials } = useGLTF("/models/646d9dcdc8a5f5bddbfac913.glb");

  // Custom avatar texture handling
  const [customTexture, setCustomTexture] = useState(null);
  const [customMaterial, setCustomMaterial] = useState(null);

  useEffect(() => {
    if (customAvatar?.type === 'custom' && customAvatar?.data?.avatar) {
      // Load custom texture from the processed image
      const loader = new THREE.TextureLoader();
      loader.load(
        customAvatar.data.avatar,
        (texture) => {
          // Configure texture for better mapping
          texture.flipY = false;
          texture.wrapS = THREE.ClampToEdgeWrapping;
          texture.wrapT = THREE.ClampToEdgeWrapping;
          texture.minFilter = THREE.LinearFilter;
          texture.magFilter = THREE.LinearFilter;
          setCustomTexture(texture);

          // Create a simple material that replaces the head texture
          // but with better blending properties
          const material = new THREE.MeshStandardMaterial({
            map: texture,
            transparent: true,
            opacity: 0.95,
            roughness: 0.7,
            metalness: 0.1,
            // Use the same normal and other maps from original material if available
            normalMap: materials.Wolf3D_Skin.normalMap,
            roughnessMap: materials.Wolf3D_Skin.roughnessMap,
            metalnessMap: materials.Wolf3D_Skin.metalnessMap,
          });

          setCustomMaterial(material);
        },
        undefined,
        (error) => {
          console.error('Error loading custom avatar texture:', error);
        }
      );
    } else {
      setCustomTexture(null);
      setCustomMaterial(null);
    }
  }, [customAvatar, materials]);

  // Load animations with error handling
  const loadFBX = (path, animName) => {
    try {
      const { animations } = useFBX(path);
      if (animations && animations[0]) {
        animations[0].name = animName;
        return animations[0];
      }
      console.warn(`Failed to load animation: ${path}`);
      return null;
    } catch (error) {
      console.error(`Error loading animation ${path}:`, error);
      return null;
    }
  };

  // Load animations
  const idleAnimation = loadFBX("/animations/Idle.fbx", "Idle");
  const angryAnimation = loadFBX("/animations/Angry Gesture.fbx", "Angry");
  const greetingAnimation = loadFBX("/animations/Standing Greeting.fbx", "Greeting");

  // Filter out any null animations
  const validAnimations = [idleAnimation, angryAnimation, greetingAnimation].filter(Boolean);

  const [animation, setAnimation] = useState("Idle");
  const group = useRef();

  // Only create animations if we have valid ones
  const { actions } = useAnimations(
    validAnimations.length > 0 ? validAnimations : [],
    group
  );

  useEffect(() => {
    // Check if the animation exists before playing it
    if (actions && actions[animation]) {
      try {
        actions[animation].reset().fadeIn(0.5).play();
        return () => {
          if (actions[animation]) {
            actions[animation].fadeOut(0.5);
          }
        };
      } catch (error) {
        console.warn(`Error playing animation "${animation}":`, error.message);
      }
    } else {
      console.warn(`Animation "${animation}" not found in actions:`, Object.keys(actions || {}));
    }
  }, [animation, actions]);

  // Head tracking with error handling
  useFrame((state) => {
    if (headFollow && group.current) {
      const head = group.current.getObjectByName("Head");
      if (head) {
        head.lookAt(state.camera.position);
      }
    }
  });

  // Expose chatbot handler functions through a ref
  React.useImperativeHandle(props.avatarRef, () => ({
    handleNewMessage,
    handleThinking,
    handleStopThinking,
    isTalking,
    isThinking
  }), [isTalking, isThinking]);

  return (
    <group {...props} dispose={null} ref={group}>
      <primitive object={nodes.Hips} />
      <skinnedMesh
        geometry={nodes.Wolf3D_Body.geometry}
        material={materials.Wolf3D_Body}
        skeleton={nodes.Wolf3D_Body.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Bottom.geometry}
        material={materials.Wolf3D_Outfit_Bottom}
        skeleton={nodes.Wolf3D_Outfit_Bottom.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Footwear.geometry}
        material={materials.Wolf3D_Outfit_Footwear}
        skeleton={nodes.Wolf3D_Outfit_Footwear.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Top.geometry}
        material={materials.Wolf3D_Outfit_Top}
        skeleton={nodes.Wolf3D_Outfit_Top.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Hair.geometry}
        material={materials.Wolf3D_Hair}
        skeleton={nodes.Wolf3D_Hair.skeleton}
      />
      <skinnedMesh
        name="EyeLeft"
        geometry={nodes.EyeLeft.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeLeft.skeleton}
        morphTargetDictionary={nodes.EyeLeft.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeLeft.morphTargetInfluences}
      />
      <skinnedMesh
        name="EyeRight"
        geometry={nodes.EyeRight.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeRight.skeleton}
        morphTargetDictionary={nodes.EyeRight.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeRight.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Head"
        geometry={nodes.Wolf3D_Head.geometry}
        material={materials.Wolf3D_Skin}
        skeleton={nodes.Wolf3D_Head.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Head.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Head.morphTargetInfluences}
      />
      {/* Face overlay for custom avatar */}
      {customTexture && (
        <mesh position={[0, 1.65, 0.08]} rotation={[0, 0, 0]}>
          <planeGeometry args={[0.35, 0.4]} />
          <meshBasicMaterial
            map={customTexture}
            transparent={true}
            opacity={0.9}
            alphaTest={0.1}
          />
        </mesh>
      )}
      <skinnedMesh
        name="Wolf3D_Teeth"
        geometry={nodes.Wolf3D_Teeth.geometry}
        material={materials.Wolf3D_Teeth}
        skeleton={nodes.Wolf3D_Teeth.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Teeth.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Teeth.morphTargetInfluences}
      />
    </group>
  );
}

useGLTF.preload("/models/646d9dcdc8a5f5bddbfac913.glb");
