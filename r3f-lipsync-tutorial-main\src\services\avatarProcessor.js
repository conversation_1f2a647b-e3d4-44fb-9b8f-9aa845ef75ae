import { PoseLandmarker, FilesetResolver } from '@mediapipe/tasks-vision';

class AvatarProcessor {
  constructor() {
    this.poseLandmarker = null;
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      const vision = await FilesetResolver.forVisionTasks(
        "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.8/wasm"
      );

      this.poseLandmarker = await PoseLandmarker.createFromOptions(vision, {
        baseOptions: {
          modelAssetPath: "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task",
          delegate: "GPU"
        },
        runningMode: "IMAGE",
        numPoses: 1
      });

      this.initialized = true;
      console.log('Avatar processor initialized successfully');
    } catch (error) {
      console.warn('MediaPipe initialization failed, using fallback mode:', error);
      // Set initialized to true to use fallback processing
      this.initialized = true;
      this.poseLandmarker = null;
    }
  }

  async processImage(imageFile) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = async () => {
        try {
          // Set canvas size to image size
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);

          let landmarks = null;
          let cropData = null;

          if (this.poseLandmarker) {
            // Use MediaPipe for pose detection
            const results = this.poseLandmarker.detect(img);

            if (!results.landmarks || results.landmarks.length === 0) {
              reject(new Error('No human detected in the image'));
              return;
            }

            landmarks = results.landmarks[0];
            cropData = this.calculateCropArea(landmarks, img.width, img.height);
          } else {
            // Fallback: use simple center cropping
            console.log('Using fallback processing (center crop)');
            cropData = this.calculateFallbackCropArea(img.width, img.height);
          }

          // Create cropped image
          const croppedCanvas = this.cropImage(img, cropData);

          // Create masked image
          const maskedCanvas = await this.createMask(croppedCanvas, landmarks, cropData);

          // Convert to textures
          const avatarTexture = croppedCanvas.toDataURL('image/jpeg', 0.9);
          const maskedTexture = maskedCanvas.toDataURL('image/png');

          resolve({
            original: img.src,
            avatar: avatarTexture,
            masked: maskedTexture,
            cropData: cropData
          });
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(imageFile);
    });
  }

  calculateCropArea(landmarks, width, height) {
    // Get key landmarks for shoulder width calculation
    const leftShoulder = landmarks[11];  // Left shoulder
    const rightShoulder = landmarks[12]; // Right shoulder
    const nose = landmarks[0];           // Nose
    const leftHip = landmarks[23];       // Left hip
    const rightHip = landmarks[24];      // Right hip

    // Calculate shoulder width
    const shoulderWidth = Math.abs(rightShoulder.x - leftShoulder.x) * width;

    // Calculate center point (neck area)
    const centerX = ((leftShoulder.x + rightShoulder.x) / 2) * width;
    const centerY = ((leftShoulder.y + rightShoulder.y) / 2) * height;

    // Calculate crop dimensions (2.5x shoulder width as per the reference)
    const cropWidth = shoulderWidth * 2.5;
    const cropHeight = cropWidth * 1.2; // Slightly taller aspect ratio

    // Calculate crop position
    const cropX = Math.max(0, centerX - cropWidth / 2);
    const cropY = Math.max(0, centerY - cropHeight * 0.3); // Position higher to include head

    // Ensure crop doesn't exceed image boundaries
    const finalCropWidth = Math.min(cropWidth, width - cropX);
    const finalCropHeight = Math.min(cropHeight, height - cropY);

    return {
      x: cropX,
      y: cropY,
      width: finalCropWidth,
      height: finalCropHeight,
      centerX: centerX - cropX,
      centerY: centerY - cropY,
      shoulderWidth: shoulderWidth
    };
  }

  calculateFallbackCropArea(width, height) {
    // Simple center crop for fallback mode
    const size = Math.min(width, height);
    const cropX = (width - size) / 2;
    const cropY = (height - size) / 2;

    return {
      x: cropX,
      y: cropY,
      width: size,
      height: size,
      centerX: size / 2,
      centerY: size / 2,
      shoulderWidth: size * 0.3
    };
  }

  cropImage(img, cropData) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = cropData.width;
    canvas.height = cropData.height;

    ctx.drawImage(
      img,
      cropData.x, cropData.y, cropData.width, cropData.height,
      0, 0, cropData.width, cropData.height
    );

    return canvas;
  }

  async createMask(croppedCanvas, landmarks, cropData) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = croppedCanvas.width;
    canvas.height = croppedCanvas.height;

    // Draw the cropped image
    ctx.drawImage(croppedCanvas, 0, 0);

    // Create a simple mask based on pose landmarks
    // This is a simplified version - in a production app, you'd want more sophisticated masking
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Simple background removal based on edge detection and color similarity
    // This is a basic implementation - you might want to use more advanced techniques
    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % canvas.width;
      const y = Math.floor((i / 4) / canvas.width);

      // Simple edge-based masking
      if (this.isBackground(x, y, canvas.width, canvas.height, cropData)) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }

    ctx.putImageData(imageData, 0, 0);
    return canvas;
  }

  isBackground(x, y, width, height, cropData) {
    // Simple background detection based on distance from center
    const centerX = cropData.centerX;
    const centerY = cropData.centerY;
    const maxDistance = Math.min(width, height) * 0.4;

    const distance = Math.sqrt(
      Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
    );

    // Consider pixels far from center as background
    return distance > maxDistance;
  }

  // Utility method to resize image while maintaining aspect ratio
  resizeImage(canvas, maxWidth = 512, maxHeight = 512) {
    const aspectRatio = canvas.width / canvas.height;
    let newWidth = maxWidth;
    let newHeight = maxHeight;

    if (aspectRatio > 1) {
      newHeight = maxWidth / aspectRatio;
    } else {
      newWidth = maxHeight * aspectRatio;
    }

    const resizedCanvas = document.createElement('canvas');
    const ctx = resizedCanvas.getContext('2d');

    resizedCanvas.width = newWidth;
    resizedCanvas.height = newHeight;

    ctx.drawImage(canvas, 0, 0, newWidth, newHeight);
    return resizedCanvas;
  }
}

// Export singleton instance
export const avatarProcessor = new AvatarProcessor();
