// MediaPipe imports with fallback
let <PERSON>seLandmarker, FilesetResolver;

// Try to import MediaPipe, but don't fail if it's not available
const loadMediaPipe = async () => {
  try {
    const mediapipe = await import('@mediapipe/tasks-vision');
    PoseLandmarker = mediapipe.PoseLandmarker;
    FilesetResolver = mediapipe.FilesetResolver;
    return true;
  } catch (error) {
    console.warn('MediaPipe not available, using fallback mode:', error);
    return false;
  }
};

class AvatarProcessor {
  constructor() {
    this.poseLandmarker = null;
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    // Try to load MediaPipe first
    const mediaPipeLoaded = await loadMediaPipe();

    if (mediaPipeLoaded && PoseLandmarker && FilesetResolver) {
      try {
        const vision = await FilesetResolver.forVisionTasks(
          "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.8/wasm"
        );

        this.poseLandmarker = await PoseLandmarker.createFromOptions(vision, {
          baseOptions: {
            modelAssetPath: "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task",
            delegate: "GPU"
          },
          runningMode: "IMAGE",
          numPoses: 1
        });

        console.log('Avatar processor initialized with MediaPipe');
      } catch (error) {
        console.warn('MediaPipe initialization failed, using fallback mode:', error);
        this.poseLandmarker = null;
      }
    } else {
      console.log('MediaPipe not available, using fallback mode');
      this.poseLandmarker = null;
    }

    this.initialized = true;
  }

  async processImage(imageFile) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = async () => {
        try {
          // Set canvas size to image size
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);

          let landmarks = null;
          let cropData = null;

          if (this.poseLandmarker) {
            // Use MediaPipe for pose detection
            const results = this.poseLandmarker.detect(img);

            if (!results.landmarks || results.landmarks.length === 0) {
              reject(new Error('No human detected in the image'));
              return;
            }

            landmarks = results.landmarks[0];
            cropData = this.calculateCropArea(landmarks, img.width, img.height);
          } else {
            // Fallback: use simple center cropping
            console.log('Using fallback processing (center crop)');
            cropData = this.calculateFallbackCropArea(img.width, img.height);
          }

          // Create cropped image
          const croppedCanvas = this.cropImage(img, cropData);

          // Create masked image
          const maskedCanvas = await this.createMask(croppedCanvas, landmarks, cropData);

          // Convert to textures
          const avatarTexture = croppedCanvas.toDataURL('image/jpeg', 0.9);
          const maskedTexture = maskedCanvas.toDataURL('image/png');

          resolve({
            original: img.src,
            avatar: avatarTexture,
            masked: maskedTexture,
            cropData: cropData
          });
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(imageFile);
    });
  }

  calculateCropArea(landmarks, width, height) {
    // Get key landmarks for shoulder width calculation
    const leftShoulder = landmarks[11];  // Left shoulder
    const rightShoulder = landmarks[12]; // Right shoulder
    const nose = landmarks[0];           // Nose
    const leftHip = landmarks[23];       // Left hip
    const rightHip = landmarks[24];      // Right hip

    // Calculate shoulder width
    const shoulderWidth = Math.abs(rightShoulder.x - leftShoulder.x) * width;

    // Calculate center point (neck area)
    const centerX = ((leftShoulder.x + rightShoulder.x) / 2) * width;
    const centerY = ((leftShoulder.y + rightShoulder.y) / 2) * height;

    // Calculate crop dimensions (2.5x shoulder width as per the reference)
    const cropWidth = shoulderWidth * 2.5;
    const cropHeight = cropWidth * 1.2; // Slightly taller aspect ratio

    // Calculate crop position
    const cropX = Math.max(0, centerX - cropWidth / 2);
    const cropY = Math.max(0, centerY - cropHeight * 0.3); // Position higher to include head

    // Ensure crop doesn't exceed image boundaries
    const finalCropWidth = Math.min(cropWidth, width - cropX);
    const finalCropHeight = Math.min(cropHeight, height - cropY);

    return {
      x: cropX,
      y: cropY,
      width: finalCropWidth,
      height: finalCropHeight,
      centerX: centerX - cropX,
      centerY: centerY - cropY,
      shoulderWidth: shoulderWidth
    };
  }

  calculateFallbackCropArea(width, height) {
    // Better fallback crop focusing on face area
    const aspectRatio = width / height;
    let cropWidth, cropHeight, cropX, cropY;

    if (aspectRatio > 1) {
      // Landscape image - crop to portrait
      cropHeight = height;
      cropWidth = height * 0.75; // 3:4 aspect ratio
      cropX = (width - cropWidth) / 2;
      cropY = 0;
    } else {
      // Portrait or square image
      cropWidth = width;
      cropHeight = width * 1.33; // 3:4 aspect ratio
      cropX = 0;
      cropY = Math.max(0, (height - cropHeight) / 4); // Crop from upper portion
    }

    return {
      x: cropX,
      y: cropY,
      width: cropWidth,
      height: Math.min(cropHeight, height - cropY),
      centerX: cropWidth / 2,
      centerY: cropHeight / 2,
      shoulderWidth: cropWidth * 0.3
    };
  }

  cropImage(img, cropData) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Set canvas size to 1024x1024 for better quality
    canvas.width = 1024;
    canvas.height = 1024;

    // Fill with a neutral background color
    ctx.fillStyle = '#f0e6d2'; // Skin tone background
    ctx.fillRect(0, 0, 1024, 1024);

    // Enable image smoothing for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Create a face-focused crop that works better with 3D mapping
    const faceSize = Math.min(cropData.width, cropData.height) * 0.8;
    const faceX = cropData.x + (cropData.width - faceSize) / 2;
    const faceY = cropData.y + (cropData.height - faceSize) / 4; // Position face in upper portion

    // Draw the face area centered in the canvas
    const targetSize = 800; // Leave some padding
    const targetX = (1024 - targetSize) / 2;
    const targetY = (1024 - targetSize) / 2;

    ctx.drawImage(
      img,
      faceX, faceY, faceSize, faceSize,
      targetX, targetY, targetSize, targetSize
    );

    // Apply a circular mask to blend better with 3D head
    this.applyCircularMask(ctx, 1024, 1024);

    return canvas;
  }

  applyCircularMask(ctx, width, height) {
    // Create a circular mask for better 3D blending
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.4;

    // Create a radial gradient mask
    const gradient = ctx.createRadialGradient(
      centerX, centerY, radius * 0.7,
      centerX, centerY, radius
    );
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
    gradient.addColorStop(1, 'rgba(240, 230, 210, 1)');

    // Apply the mask
    ctx.globalCompositeOperation = 'destination-out';
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    ctx.globalCompositeOperation = 'source-over';
  }

  blendEdges(ctx, width, height, padding) {
    // Create a radial gradient to soften the edges
    const gradient = ctx.createRadialGradient(
      width / 2, height / 2, width / 2 - padding * 2,
      width / 2, height / 2, width / 2
    );
    gradient.addColorStop(0, 'rgba(240, 230, 210, 0)');
    gradient.addColorStop(1, 'rgba(240, 230, 210, 0.3)');

    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    ctx.globalCompositeOperation = 'source-over';
  }

  async createMask(croppedCanvas, landmarks, cropData) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = croppedCanvas.width;
    canvas.height = croppedCanvas.height;

    // Draw the cropped image
    ctx.drawImage(croppedCanvas, 0, 0);

    // Create a simple mask based on pose landmarks
    // This is a simplified version - in a production app, you'd want more sophisticated masking
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Simple background removal based on edge detection and color similarity
    // This is a basic implementation - you might want to use more advanced techniques
    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % canvas.width;
      const y = Math.floor((i / 4) / canvas.width);

      // Simple edge-based masking
      if (this.isBackground(x, y, canvas.width, canvas.height, cropData)) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }

    ctx.putImageData(imageData, 0, 0);
    return canvas;
  }

  isBackground(x, y, width, height, cropData) {
    // Simple background detection based on distance from center
    const centerX = cropData.centerX;
    const centerY = cropData.centerY;
    const maxDistance = Math.min(width, height) * 0.4;

    const distance = Math.sqrt(
      Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
    );

    // Consider pixels far from center as background
    return distance > maxDistance;
  }

  // Utility method to resize image while maintaining aspect ratio
  resizeImage(canvas, maxWidth = 512, maxHeight = 512) {
    const aspectRatio = canvas.width / canvas.height;
    let newWidth = maxWidth;
    let newHeight = maxHeight;

    if (aspectRatio > 1) {
      newHeight = maxWidth / aspectRatio;
    } else {
      newWidth = maxHeight * aspectRatio;
    }

    const resizedCanvas = document.createElement('canvas');
    const ctx = resizedCanvas.getContext('2d');

    resizedCanvas.width = newWidth;
    resizedCanvas.height = newHeight;

    ctx.drawImage(canvas, 0, 0, newWidth, newHeight);
    return resizedCanvas;
  }
}

// Export singleton instance
export const avatarProcessor = new AvatarProcessor();
