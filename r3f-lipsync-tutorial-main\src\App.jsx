import { Canvas } from "@react-three/fiber";
import { Experience } from "./components/Experience";
import Chatbot from "./components/Chatbot";
import AvatarSelection from "./components/AvatarSelection";
import AvatarStudio from "./components/AvatarStudio";
import React, { useRef, useState, useEffect } from "react";
import CONFIG from "./config";
import "./chatbot.css";

function App() {
  const avatarRef = useRef();
  const [avatarName, setAvatarName] = useState(CONFIG.DEFAULT_AVATAR_NAME);
  const [showNameInput, setShowNameInput] = useState(false);
  const [nameInputValue, setNameInputValue] = useState("");
  const [customAvatar, setCustomAvatar] = useState(null);
  const [useStudio, setUseStudio] = useState(false);

  // Load saved data from localStorage if available
  useEffect(() => {
    const savedName = localStorage.getItem("avatarName");
    if (savedName) {
      setAvatarName(savedName);
    }

    const savedAvatar = localStorage.getItem("customAvatar");
    if (savedAvatar) {
      try {
        setCustomAvatar(JSON.parse(savedAvatar));
      } catch (error) {
        console.error("Error loading saved avatar:", error);
      }
    }
  }, []);

  // Save name to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("avatarName", avatarName);
  }, [avatarName]);

  // Save custom avatar to localStorage when it changes
  useEffect(() => {
    if (customAvatar) {
      localStorage.setItem("customAvatar", JSON.stringify(customAvatar));
    }
  }, [customAvatar]);

  // Handle name change
  const handleNameChange = (newName) => {
    if (newName && newName.trim() !== "") {
      setAvatarName(newName.trim());
      setShowNameInput(false);
    }
  };

  // Toggle name input visibility
  const toggleNameInput = () => {
    setNameInputValue(avatarName);
    setShowNameInput(!showNameInput);
  };

  // Handle messages from the chatbot
  const handleMessageReceived = (message) => {
    if (avatarRef.current) {
      avatarRef.current.handleNewMessage(message);
    }
  };

  // Handle thinking state
  const handleThinking = () => {
    if (avatarRef.current) {
      avatarRef.current.handleThinking();
    }
  };

  // Handle stop thinking state
  const handleStopThinking = () => {
    if (avatarRef.current) {
      avatarRef.current.handleStopThinking();
    }
  };

  // Handle avatar change
  const handleAvatarChange = (newAvatar) => {
    setCustomAvatar(newAvatar);
    console.log("Avatar changed:", newAvatar);
  };

  // If studio mode is enabled, show the Avatar Studio
  if (useStudio) {
    return (
      <div>
        <AvatarStudio />
        <button
          onClick={() => setUseStudio(false)}
          style={{
            position: 'fixed',
            top: '20px',
            left: '20px',
            padding: '12px 24px',
            background: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            cursor: 'pointer',
            fontWeight: 'bold',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
            zIndex: 10000
          }}
        >
          ← Back to Chat
        </button>
      </div>
    );
  }

  return (
    <div className="app-container">
      {/* 3D Canvas */}
      <Canvas shadows camera={{ position: [0, 0, 8], fov: 42 }}>
        <color attach="background" args={["#ececec"]} />
        <Experience avatarRef={avatarRef} customAvatar={customAvatar} />
      </Canvas>

      {/* Avatar Name Display */}
      <div className="avatar-name-container">
        <div className="avatar-name" onClick={toggleNameInput}>
          {showNameInput ? (
            <div className="name-input-container">
              <input
                type="text"
                value={nameInputValue}
                onChange={(e) => setNameInputValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleNameChange(nameInputValue);
                  }
                }}
                autoFocus
                placeholder="Enter avatar name"
                maxLength={20}
              />
              <button onClick={() => handleNameChange(nameInputValue)}>✓</button>
              <button onClick={() => setShowNameInput(false)}>✕</button>
            </div>
          ) : (
            <>
              <span>{avatarName}</span>
              <button className="edit-name-btn" title="Edit name">✎</button>
            </>
          )}
        </div>
      </div>

      {/* Avatar Studio Button */}
      <div style={{
        position: 'absolute',
        top: '20px',
        right: '20px',
        zIndex: 1002
      }}>
        <button
          onClick={() => setUseStudio(true)}
          style={{
            padding: '12px 24px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            cursor: 'pointer',
            fontWeight: 'bold',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.3)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
          }}
        >
          🎭 Avatar Studio
        </button>
      </div>

      {/* Avatar Selection Panel */}
      <div className="avatar-selection-container" style={{
        position: 'absolute',
        top: '70px',
        right: '20px',
        width: '350px',
        background: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '10px',
        padding: '20px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
        zIndex: 1001,
        maxHeight: '60vh',
        overflowY: 'auto'
      }}>
        <AvatarSelection
          onAvatarChange={handleAvatarChange}
          currentAvatar={customAvatar}
        />
      </div>

      {/* HTML UI overlay */}
      <div className="ui-overlay">
        <Chatbot
          avatarName={avatarName}
          onMessageReceived={handleMessageReceived}
          onThinking={handleThinking}
          onStopThinking={handleStopThinking}
          onNameChange={handleNameChange}
        />
      </div>
    </div>
  );
}

export default App;
