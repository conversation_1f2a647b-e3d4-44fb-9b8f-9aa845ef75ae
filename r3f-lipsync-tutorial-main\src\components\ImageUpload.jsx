import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { avatarProcessor } from '../services/avatarProcessor';

const ImageUpload = ({ onAvatarCreated, onProcessingStart, onProcessingEnd }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [preview, setPreview] = useState(null);

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please upload an image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('Image file is too large. Please upload an image smaller than 10MB');
      return;
    }

    setError(null);
    setIsProcessing(true);
    
    if (onProcessingStart) {
      onProcessingStart();
    }

    try {
      // Create preview
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);

      // Process the image
      const result = await avatarProcessor.processImage(file);
      
      if (onAvatarCreated) {
        onAvatarCreated(result);
      }

      console.log('Avatar created successfully:', result);
    } catch (error) {
      console.error('Error processing image:', error);
      setError(error.message || 'Failed to process image. Please try another image.');
    } finally {
      setIsProcessing(false);
      if (onProcessingEnd) {
        onProcessingEnd();
      }
    }
  }, [onAvatarCreated, onProcessingStart, onProcessingEnd]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    multiple: false,
    disabled: isProcessing
  });

  const clearPreview = () => {
    if (preview) {
      URL.revokeObjectURL(preview);
      setPreview(null);
    }
    setError(null);
  };

  return (
    <div className="image-upload-container">
      <div className="upload-section">
        <div
          {...getRootProps()}
          className={`dropzone ${isDragActive ? 'active' : ''} ${isProcessing ? 'processing' : ''}`}
        >
          <input {...getInputProps()} />
          
          {isProcessing ? (
            <div className="processing-indicator">
              <div className="spinner"></div>
              <p>Processing your image...</p>
              <small>Detecting human and creating avatar</small>
            </div>
          ) : (
            <div className="upload-content">
              <div className="upload-icon">📷</div>
              <p className="upload-text">
                {isDragActive
                  ? 'Drop your image here'
                  : 'Drag & drop an image or click to select'
                }
              </p>
              <small className="upload-hint">
                Upload a photo of a person to create a custom avatar
              </small>
            </div>
          )}
        </div>

        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span>{error}</span>
            <button onClick={clearPreview} className="error-close">×</button>
          </div>
        )}

        {preview && !isProcessing && (
          <div className="preview-section">
            <div className="preview-header">
              <h4>Preview</h4>
              <button onClick={clearPreview} className="clear-preview">×</button>
            </div>
            <img src={preview} alt="Preview" className="preview-image" />
          </div>
        )}
      </div>

      <div className="upload-tips">
        <h4>Tips for best results:</h4>
        <ul>
          <li>Use a clear photo with good lighting</li>
          <li>Person should be facing the camera</li>
          <li>Arms should be at the sides</li>
          <li>Full upper body should be visible</li>
          <li>Avoid busy backgrounds when possible</li>
        </ul>
      </div>
    </div>
  );
};

export default ImageUpload;
