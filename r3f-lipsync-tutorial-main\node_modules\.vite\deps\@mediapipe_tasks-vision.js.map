{"version": 3, "sources": ["../../@mediapipe/tasks-vision/vision_bundle.mjs"], "sourcesContent": ["var t=\"undefined\"!=typeof self?self:{};function e(){throw Error(\"Invalid UTF8\")}function n(t,e){return e=String.fromCharCode.apply(null,e),null==t?e:t+e}let r,i;const s=\"undefined\"!=typeof TextDecoder;let o;const a=\"undefined\"!=typeof TextEncoder;function c(t){if(a)t=(o||=new TextEncoder).encode(t);else{let n=0;const r=new Uint8Array(3*t.length);for(let i=0;i<t.length;i++){var e=t.charCodeAt(i);if(e<128)r[n++]=e;else{if(e<2048)r[n++]=e>>6|192;else{if(e>=55296&&e<=57343){if(e<=56319&&i<t.length){const s=t.charCodeAt(++i);if(s>=56320&&s<=57343){e=1024*(e-55296)+s-56320+65536,r[n++]=e>>18|240,r[n++]=e>>12&63|128,r[n++]=e>>6&63|128,r[n++]=63&e|128;continue}i--}e=65533}r[n++]=e>>12|224,r[n++]=e>>6&63|128}r[n++]=63&e|128}}t=n===r.length?r:r.subarray(0,n)}return t}var h,u;t:{for(var l=[\"CLOSURE_FLAGS\"],d=t,f=0;f<l.length;f++)if(null==(d=d[l[f]])){u=null;break t}u=d}var p,g=u&&u[610401301];h=null!=g&&g;const m=t.navigator;function y(t){return!!h&&(!!p&&p.brands.some((({brand:e})=>e&&-1!=e.indexOf(t))))}function _(e){var n;return(n=t.navigator)&&(n=n.userAgent)||(n=\"\"),-1!=n.indexOf(e)}function v(){return!!h&&(!!p&&p.brands.length>0)}function E(){return v()?y(\"Chromium\"):(_(\"Chrome\")||_(\"CriOS\"))&&!(!v()&&_(\"Edge\"))||_(\"Silk\")}function w(t){return w[\" \"](t),t}p=m&&m.userAgentData||null,w[\" \"]=function(){};var T=!v()&&(_(\"Trident\")||_(\"MSIE\"));!_(\"Android\")||E(),E(),_(\"Safari\")&&(E()||!v()&&_(\"Coast\")||!v()&&_(\"Opera\")||!v()&&_(\"Edge\")||(v()?y(\"Microsoft Edge\"):_(\"Edg/\"))||v()&&y(\"Opera\"));var A={},b=null;function k(t){const e=t.length;let n=3*e/4;n%3?n=Math.floor(n):-1!=\"=.\".indexOf(t[e-1])&&(n=-1!=\"=.\".indexOf(t[e-2])?n-2:n-1);const r=new Uint8Array(n);let i=0;return function(t,e){function n(e){for(;r<t.length;){const e=t.charAt(r++),n=b[e];if(null!=n)return n;if(!/^[\\s\\xa0]*$/.test(e))throw Error(\"Unknown base64 encoding at char: \"+e)}return e}S();let r=0;for(;;){const t=n(-1),r=n(0),i=n(64),s=n(64);if(64===s&&-1===t)break;e(t<<2|r>>4),64!=i&&(e(r<<4&240|i>>2),64!=s&&e(i<<6&192|s))}}(t,(function(t){r[i++]=t})),i!==n?r.subarray(0,i):r}function S(){if(!b){b={};var t=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\"),e=[\"+/=\",\"+/\",\"-_=\",\"-_.\",\"-_\"];for(let n=0;n<5;n++){const r=t.concat(e[n].split(\"\"));A[n]=r;for(let t=0;t<r.length;t++){const e=r[t];void 0===b[e]&&(b[e]=t)}}}}var x=\"undefined\"!=typeof Uint8Array,L=!T&&\"function\"==typeof btoa;function R(t){if(!L){var e;void 0===e&&(e=0),S(),e=A[e];var n=Array(Math.floor(t.length/3)),r=e[64]||\"\";let c=0,h=0;for(;c<t.length-2;c+=3){var i=t[c],s=t[c+1],o=t[c+2],a=e[i>>2];i=e[(3&i)<<4|s>>4],s=e[(15&s)<<2|o>>6],o=e[63&o],n[h++]=a+i+s+o}switch(a=0,o=r,t.length-c){case 2:o=e[(15&(a=t[c+1]))<<2]||r;case 1:t=t[c],n[h]=e[t>>2]+e[(3&t)<<4|a>>4]+o+r}return n.join(\"\")}for(e=\"\",n=0,r=t.length-10240;n<r;)e+=String.fromCharCode.apply(null,t.subarray(n,n+=10240));return e+=String.fromCharCode.apply(null,n?t.subarray(n):t),btoa(e)}const F=/[-_.]/g,I={\"-\":\"+\",_:\"/\",\".\":\"=\"};function M(t){return I[t]||\"\"}function P(t){if(!L)return k(t);F.test(t)&&(t=t.replace(F,M)),t=atob(t);const e=new Uint8Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}function O(t){return x&&null!=t&&t instanceof Uint8Array}var C={};function U(){return B||=new N(null,C)}function D(t){j(C);var e=t.g;return null==(e=null==e||O(e)?e:\"string\"==typeof e?P(e):null)?e:t.g=e}var N=class{h(){return new Uint8Array(D(this)||0)}constructor(t,e){if(j(e),this.g=t,null!=t&&0===t.length)throw Error(\"ByteString should be constructed with non-empty values\")}};let B,G;function j(t){if(t!==C)throw Error(\"illegal external caller\")}function V(t,e){t.__closure__error__context__984382||(t.__closure__error__context__984382={}),t.__closure__error__context__984382.severity=e}function X(t){return V(t=Error(t),\"warning\"),t}var H=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol(),W=new Set;function z(t,e,n=!1,r=!1){return t=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol()?r&&Symbol.for&&t?Symbol.for(t):null!=t?Symbol(t):Symbol():e,n&&W.add(t),t}var K=z(\"jas\",void 0,!0,!0),Y=z(void 0,\"0di\"),$=z(void 0,\"2ex\"),q=z(void 0,\"1oa\",!0),J=z(void 0,Symbol(),!0);const Z=H?K:\"Ga\",Q={Ga:{value:0,configurable:!0,writable:!0,enumerable:!1}},tt=Object.defineProperties;function et(t,e){H||Z in t||tt(t,Q),t[Z]|=e}function nt(t,e){H||Z in t||tt(t,Q),t[Z]=e}function rt(t){return et(t,34),t}function it(t,e){nt(e,-30975&(0|t))}function st(t,e){nt(e,-30941&(34|t))}function ot(){return\"function\"==typeof BigInt}function at(t){return Array.prototype.slice.call(t)}var ct,ht={},ut={};function lt(t){return!(!t||\"object\"!=typeof t||t.Ia!==ut)}function dt(t){return null!==t&&\"object\"==typeof t&&!Array.isArray(t)&&t.constructor===Object}function ft(t,e){if(null!=t)if(\"string\"==typeof t)t=t?new N(t,C):U();else if(t.constructor!==N)if(O(t))t=t.length?new N(new Uint8Array(t),C):U();else{if(!e)throw Error();t=void 0}return t}function pt(t){return!(!Array.isArray(t)||t.length)&&!!(1&(0|t[Z]))}const gt=[];function mt(t){if(2&t)throw Error()}nt(gt,55),ct=Object.freeze(gt);class yt{constructor(t,e,n){this.l=0,this.g=t,this.h=e,this.m=n}next(){if(this.l<this.g.length){const t=this.g[this.l++];return{done:!1,value:this.h?this.h.call(this.m,t):t}}return{done:!0,value:void 0}}[Symbol.iterator](){return new yt(this.g,this.h,this.m)}}function _t(t){return J?t[J]:void 0}var vt=Object.freeze({});function Et(t){return t.Qa=!0,t}var wt=Et((t=>\"number\"==typeof t)),Tt=Et((t=>\"string\"==typeof t)),At=Et((t=>\"boolean\"==typeof t)),bt=\"function\"==typeof t.BigInt&&\"bigint\"==typeof t.BigInt(0),kt=Et((t=>bt?t>=xt&&t<=Rt:\"-\"===t[0]?Ft(t,St):Ft(t,Lt)));const St=Number.MIN_SAFE_INTEGER.toString(),xt=bt?BigInt(Number.MIN_SAFE_INTEGER):void 0,Lt=Number.MAX_SAFE_INTEGER.toString(),Rt=bt?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Ft(t,e){if(t.length>e.length)return!1;if(t.length<e.length||t===e)return!0;for(let n=0;n<t.length;n++){const r=t[n],i=e[n];if(r>i)return!1;if(r<i)return!0}}const It=\"function\"==typeof Uint8Array.prototype.slice;let Mt,Pt=0,Ot=0;function Ct(t){const e=t>>>0;Pt=e,Ot=(t-e)/4294967296>>>0}function Ut(t){if(t<0){Ct(-t);const[e,n]=Xt(Pt,Ot);Pt=e>>>0,Ot=n>>>0}else Ct(t)}function Dt(t){const e=Mt||=new DataView(new ArrayBuffer(8));e.setFloat32(0,+t,!0),Ot=0,Pt=e.getUint32(0,!0)}function Nt(t,e){const n=4294967296*e+(t>>>0);return Number.isSafeInteger(n)?n:Gt(t,e)}function Bt(t,e){const n=2147483648&e;return n&&(e=~e>>>0,0==(t=1+~t>>>0)&&(e=e+1>>>0)),\"number\"==typeof(t=Nt(t,e))?n?-t:t:n?\"-\"+t:t}function Gt(t,e){if(t>>>=0,(e>>>=0)<=2097151)var n=\"\"+(4294967296*e+t);else ot()?n=\"\"+(BigInt(e)<<BigInt(32)|BigInt(t)):(t=(16777215&t)+6777216*(n=16777215&(t>>>24|e<<8))+6710656*(e=e>>16&65535),n+=8147497*e,e*=2,t>=1e7&&(n+=t/1e7>>>0,t%=1e7),n>=1e7&&(e+=n/1e7>>>0,n%=1e7),n=e+jt(n)+jt(t));return n}function jt(t){return t=String(t),\"0000000\".slice(t.length)+t}function Vt(t){if(t.length<16)Ut(Number(t));else if(ot())t=BigInt(t),Pt=Number(t&BigInt(4294967295))>>>0,Ot=Number(t>>BigInt(32)&BigInt(4294967295));else{const e=+(\"-\"===t[0]);Ot=Pt=0;const n=t.length;for(let r=e,i=(n-e)%6+e;i<=n;r=i,i+=6){const e=Number(t.slice(r,i));Ot*=1e6,Pt=1e6*Pt+e,Pt>=4294967296&&(Ot+=Math.trunc(Pt/4294967296),Ot>>>=0,Pt>>>=0)}if(e){const[t,e]=Xt(Pt,Ot);Pt=t,Ot=e}}}function Xt(t,e){return e=~e,t?t=1+~t:e+=1,[t,e]}const Ht=\"function\"==typeof BigInt?BigInt.asIntN:void 0,Wt=\"function\"==typeof BigInt?BigInt.asUintN:void 0,zt=Number.isSafeInteger,Kt=Number.isFinite,Yt=Math.trunc;function $t(t){return null==t||\"number\"==typeof t?t:\"NaN\"===t||\"Infinity\"===t||\"-Infinity\"===t?Number(t):void 0}function qt(t){return null==t||\"boolean\"==typeof t?t:\"number\"==typeof t?!!t:void 0}const Jt=/^-?([1-9][0-9]*|0)(\\.[0-9]+)?$/;function Zt(t){switch(typeof t){case\"bigint\":return!0;case\"number\":return Kt(t);case\"string\":return Jt.test(t);default:return!1}}function Qt(t){if(null==t)return t;if(\"string\"==typeof t&&t)t=+t;else if(\"number\"!=typeof t)return;return Kt(t)?0|t:void 0}function te(t){if(null==t)return t;if(\"string\"==typeof t&&t)t=+t;else if(\"number\"!=typeof t)return;return Kt(t)?t>>>0:void 0}function ee(t){if(\"-\"===t[0])return!1;const e=t.length;return e<20||20===e&&Number(t.substring(0,6))<184467}function ne(t){return t=Yt(t),zt(t)||(Ut(t),t=Bt(Pt,Ot)),t}function re(t){var e=Yt(Number(t));if(zt(e))return String(e);if(-1!==(e=t.indexOf(\".\"))&&(t=t.substring(0,e)),e=t.length,!(\"-\"===t[0]?e<20||20===e&&Number(t.substring(0,7))>-922337:e<19||19===e&&Number(t.substring(0,6))<922337))if(Vt(t),t=Pt,2147483648&(e=Ot))if(ot())t=\"\"+(BigInt(0|e)<<BigInt(32)|BigInt(t>>>0));else{const[n,r]=Xt(t,e);t=\"-\"+Gt(n,r)}else t=Gt(t,e);return t}function ie(t){return null==t?t:\"bigint\"==typeof t?(kt(t)?t=Number(t):(t=Ht(64,t),t=kt(t)?Number(t):String(t)),t):Zt(t)?\"number\"==typeof t?ne(t):re(t):void 0}function se(t){if(null==t)return t;var e=typeof t;if(\"bigint\"===e)return String(Wt(64,t));if(Zt(t)){if(\"string\"===e)return e=Yt(Number(t)),zt(e)&&e>=0?t=String(e):(-1!==(e=t.indexOf(\".\"))&&(t=t.substring(0,e)),ee(t)||(Vt(t),t=Gt(Pt,Ot))),t;if(\"number\"===e)return(t=Yt(t))>=0&&zt(t)?t:function(t){if(t<0){Ut(t);var e=Gt(Pt,Ot);return t=Number(e),zt(t)?t:e}return ee(e=String(t))?e:(Ut(t),Nt(Pt,Ot))}(t)}}function oe(t){if(\"string\"!=typeof t)throw Error();return t}function ae(t){if(null!=t&&\"string\"!=typeof t)throw Error();return t}function ce(t){return null==t||\"string\"==typeof t?t:void 0}function he(t,e,n,r){if(null!=t&&\"object\"==typeof t&&t.W===ht)return t;if(!Array.isArray(t))return n?2&r?((t=e[Y])||(rt((t=new e).u),t=e[Y]=t),e=t):e=new e:e=void 0,e;let i=n=0|t[Z];return 0===i&&(i|=32&r),i|=2&r,i!==n&&nt(t,i),new e(t)}function ue(t,e,n){if(e)t:{if(!Zt(e=t))throw X(\"int64\");switch(typeof e){case\"string\":e=re(e);break t;case\"bigint\":if(t=e=Ht(64,e),Tt(t)){if(!/^\\s*(?:-?[1-9]\\d*|0)?\\s*$/.test(t))throw Error(String(t))}else if(wt(t)&&!Number.isSafeInteger(t))throw Error(String(t));e=bt?BigInt(e):At(e)?e?\"1\":\"0\":Tt(e)?e.trim()||\"0\":String(e);break t;default:e=ne(e)}}else e=ie(t);return\"string\"==typeof(n=null==(t=e)?n?0:void 0:t)&&zt(e=+n)?e:n}const le={};let de=function(){try{return w(new class extends Map{constructor(){super()}}),!1}catch{return!0}}();class fe{constructor(){this.g=new Map}get(t){return this.g.get(t)}set(t,e){return this.g.set(t,e),this.size=this.g.size,this}delete(t){return t=this.g.delete(t),this.size=this.g.size,t}clear(){this.g.clear(),this.size=this.g.size}has(t){return this.g.has(t)}entries(){return this.g.entries()}keys(){return this.g.keys()}values(){return this.g.values()}forEach(t,e){return this.g.forEach(t,e)}[Symbol.iterator](){return this.entries()}}const pe=de?(Object.setPrototypeOf(fe.prototype,Map.prototype),Object.defineProperties(fe.prototype,{size:{value:0,configurable:!0,enumerable:!0,writable:!0}}),fe):class extends Map{constructor(){super()}};function ge(t){return t}function me(t){if(2&t.L)throw Error(\"Cannot mutate an immutable Map\")}var ye=class extends pe{constructor(t,e,n=ge,r=ge){super();let i=0|t[Z];i|=64,nt(t,i),this.L=i,this.S=e,this.R=n,this.Y=this.S?_e:r;for(let s=0;s<t.length;s++){const o=t[s],a=n(o[0],!1,!0);let c=o[1];e?void 0===c&&(c=null):c=r(o[1],!1,!0,void 0,void 0,i),super.set(a,c)}}na(t=ve){if(0!==this.size)return this.X(t)}X(t=ve){const e=[],n=super.entries();for(var r;!(r=n.next()).done;)(r=r.value)[0]=t(r[0]),r[1]=t(r[1]),e.push(r);return e}clear(){me(this),super.clear()}delete(t){return me(this),super.delete(this.R(t,!0,!1))}entries(){var t=this.ma();return new yt(t,Ee,this)}keys(){return this.Ha()}values(){var t=this.ma();return new yt(t,ye.prototype.get,this)}forEach(t,e){super.forEach(((n,r)=>{t.call(e,this.get(r),r,this)}))}set(t,e){return me(this),null==(t=this.R(t,!0,!1))?this:null==e?(super.delete(t),this):super.set(t,this.Y(e,!0,!0,this.S,!1,this.L))}Na(t){const e=this.R(t[0],!1,!0);t=t[1],t=this.S?void 0===t?null:t:this.Y(t,!1,!0,void 0,!1,this.L),super.set(e,t)}has(t){return super.has(this.R(t,!1,!1))}get(t){t=this.R(t,!1,!1);const e=super.get(t);if(void 0!==e){var n=this.S;return n?((n=this.Y(e,!1,!0,n,this.ra,this.L))!==e&&super.set(t,n),n):e}}ma(){return Array.from(super.keys())}Ha(){return super.keys()}[Symbol.iterator](){return this.entries()}};function _e(t,e,n,r,i,s){return t=he(t,r,n,s),i&&(t=Oe(t)),t}function ve(t){return t}function Ee(t){return[t,this.get(t)]}let we,Te,Ae;function be(){return we||=new ye(rt([]),void 0,void 0,void 0,le)}function ke(t,e,n,r,i){if(null!=t){if(Array.isArray(t))t=pt(t)?void 0:i&&2&(0|t[Z])?t:Se(t,e,n,void 0!==r,i);else if(dt(t)){const s={};for(let o in t)s[o]=ke(t[o],e,n,r,i);t=s}else t=e(t,r);return t}}function Se(t,e,n,r,i){const s=r||n?0|t[Z]:0,o=r?!!(32&s):void 0;r=at(t);for(let t=0;t<r.length;t++)r[t]=ke(r[t],e,n,o,i);return n&&((t=_t(t))&&(r[J]=at(t)),n(s,r)),r}function xe(t){return ke(t,Le,void 0,void 0,!1)}function Le(t){return t.W===ht?t.toJSON():t instanceof ye?t.na(xe):function(t){switch(typeof t){case\"number\":return isFinite(t)?t:String(t);case\"bigint\":return kt(t)?Number(t):String(t);case\"boolean\":return t?1:0;case\"object\":if(t)if(Array.isArray(t)){if(pt(t))return}else{if(O(t))return R(t);if(t instanceof N){const e=t.g;return null==e?\"\":\"string\"==typeof e?e:t.g=R(e)}if(t instanceof ye)return t.na()}}return t}(t)}function Re(t){return Se(t,Le,void 0,void 0,!1)}function Fe(t,e,n){return t=Ie(t,e[0],e[1],n?1:2),e!==Te&&n&&et(t,16384),t}function Ie(t,e,n,r){if(null==t){var i=96;n?(t=[n],i|=512):t=[],e&&(i=-33521665&i|(1023&e)<<15)}else{if(!Array.isArray(t))throw Error(\"narr\");if(2048&(i=0|t[Z]))throw Error(\"farr\");if(64&i)return t;if(1===r||2===r||(i|=64),n&&(i|=512,n!==t[0]))throw Error(\"mid\");t:{if(r=(n=t).length){const t=r-1;if(dt(n[t])){if((e=t-(512&(i|=256)?0:-1))>=1024)throw Error(\"pvtlmt\");i=-33521665&i|(1023&e)<<15;break t}}if(e){if((e=Math.max(e,r-(512&i?0:-1)))>1024)throw Error(\"spvt\");i=-33521665&i|(1023&e)<<15}}}return nt(t,i),t}function Me(t,e,n=st){if(null!=t){if(x&&t instanceof Uint8Array)return e?t:new Uint8Array(t);if(Array.isArray(t)){var r=0|t[Z];return 2&r?t:(e&&=0===r||!!(32&r)&&!(64&r||!(16&r)),e?(nt(t,-12293&(34|r)),t):Se(t,Me,4&r?st:n,!0,!0))}return t.W===ht?t=2&(r=0|(n=t.u)[Z])?t:new t.constructor(Pe(n,r,!0)):t instanceof ye&&!(2&t.L)&&(n=rt(t.X(Me)),t=new ye(n,t.S,t.R,t.Y)),t}}function Pe(t,e,n){const r=n||2&e?st:it,i=!!(32&e);return t=function(t,e,n){const r=at(t);var i=r.length;const s=256&e?r[i-1]:void 0;for(i+=s?-1:0,e=512&e?1:0;e<i;e++)r[e]=n(r[e]);if(s){e=r[e]={};for(const t in s)e[t]=n(s[t])}return(t=_t(t))&&(r[J]=at(t)),r}(t,e,(t=>Me(t,i,r))),et(t,32|(n?2:0)),t}function Oe(t){const e=t.u,n=0|e[Z];return 2&n?new t.constructor(Pe(e,n,!1)):t}function Ce(t,e){return Ue(t=t.u,0|t[Z],e)}function Ue(e,n,r,i){if(-1===r)return null;var s=r+(512&n?0:-1);const o=e.length-1;return s>=o&&256&n?e[o][r]:i&&256&n&&null!=(n=e[o][r])?(null!=e[s]&&null!=$&&((s=(e=G??={})[$]||0)>=4||(e[$]=s+1,V(e=Error(),\"incident\"),function(e){t.setTimeout((()=>{throw e}),0)}(e))),n):s<=o?e[s]:void 0}function De(t,e,n){const r=t.u;let i=0|r[Z];return mt(i),Ne(r,i,e,n),t}function Ne(t,e,n,r){const i=512&e?0:-1,s=n+i;var o=t.length-1;return s>=o&&256&e?(t[o][n]=r,e):s<=o?(t[s]=r,256&e&&(n in(t=t[o])&&delete t[n]),e):(void 0!==r&&(n>=(o=e>>15&1023||536870912)?null!=r&&(t[o+i]={[n]:r},nt(t,e|=256)):t[s]=r),e)}function Be(t,e){let n=0|(t=t.u)[Z];const r=Ue(t,n,e),i=$t(r);return null!=i&&i!==r&&Ne(t,n,e,i),i}function Ge(t){let e=0|(t=t.u)[Z];const n=Ue(t,e,1),r=ft(n,!0);return null!=r&&r!==n&&Ne(t,e,1,r),r}function je(){return void 0===vt?2:4}function Ve(t,e,n,r,i){const s=t.u,o=2&(t=0|s[Z])?1:r;i=!!i;let a=0|(r=Xe(s,t,e))[Z];if(!(4&a)){4&a&&(r=at(r),a=an(a,t),t=Ne(s,t,e,r));let i=0,o=0;for(;i<r.length;i++){const t=n(r[i]);null!=t&&(r[o++]=t)}o<i&&(r.length=o),a=He(a,t),n=-4097&(20|a),a=n&=-8193,nt(r,a),2&a&&Object.freeze(r)}return 1===o||4===o&&32&a?We(a)||(i=a,a|=2,a!==i&&nt(r,a),Object.freeze(r)):(2===o&&We(a)&&(r=at(r),a=an(a,t),a=cn(a,t,i),nt(r,a),t=Ne(s,t,e,r)),We(a)||(e=a,a=cn(a,t,i),a!==e&&nt(r,a))),r}function Xe(t,e,n,r){return t=Ue(t,e,n,r),Array.isArray(t)?t:ct}function He(t,e){return 0===t&&(t=an(t,e)),1|t}function We(t){return!!(2&t)&&!!(4&t)||!!(2048&t)}function ze(t){t=at(t);for(let e=0;e<t.length;e++){const n=t[e]=at(t[e]);Array.isArray(n[1])&&(n[1]=rt(n[1]))}return t}function Ke(t,e,n,r){let i=0|(t=t.u)[Z];mt(i),Ne(t,i,e,(\"0\"===r?0===Number(n):n===r)?void 0:n)}function Ye(t,e,n,r,i){mt(e);var s=!(!(64&e)&&16384&e);const o=(i=Xe(t,e,n,i))!==ct;if(s||!o){let a=s=o?0|i[Z]:0;(!o||2&a||We(a)||4&a&&!(32&a))&&(i=at(i),a=an(a,e),e=Ne(t,e,n,i)),a=-13&He(a,e),a=cn(r?-17&a:16|a,e,!0),a!==s&&nt(i,a)}return i}function $e(t,e){var n=vs;return Ze(qe(t=t.u),t,0|t[Z],n)===e?e:-1}function qe(t){if(H)return t[q]??(t[q]=new Map);if(q in t)return t[q];const e=new Map;return Object.defineProperty(t,q,{value:e}),e}function Je(t,e,n,r){const i=qe(t),s=Ze(i,t,e,n);return s!==r&&(s&&(e=Ne(t,e,s)),i.set(n,r)),e}function Ze(t,e,n,r){let i=t.get(r);if(null!=i)return i;i=0;for(let t=0;t<r.length;t++){const s=r[t];null!=Ue(e,n,s)&&(0!==i&&(n=Ne(e,n,i)),i=s)}return t.set(r,i),i}function Qe(t,e,n,r){let i,s=0|t[Z];if(null!=(r=Ue(t,s,n,r))&&r.W===ht)return(e=Oe(r))!==r&&Ne(t,s,n,e),e.u;if(Array.isArray(r)){const t=0|r[Z];i=2&t?Fe(Pe(r,t,!1),e,!0):64&t?r:Fe(i,e,!0)}else i=Fe(void 0,e,!0);return i!==r&&Ne(t,s,n,i),i}function tn(t,e,n,r){let i=0|(t=t.u)[Z];return(e=he(r=Ue(t,i,n,r),e,!1,i))!==r&&null!=e&&Ne(t,i,n,e),e}function en(t,e,n,r=!1){if(null==(e=tn(t,e,n,r)))return e;if(!(2&(r=0|(t=t.u)[Z]))){const i=Oe(e);i!==e&&Ne(t,r,n,e=i)}return e}function nn(t,e,n,r,i,s,o){t=t.u;var a=!!(2&e);const c=a?1:i;s=!!s,o&&=!a;var h=0|(i=Xe(t,e,r))[Z];if(!(a=!!(4&h))){var u=i,l=e;const t=!!(2&(h=He(h,e)));t&&(l|=2);let r=!t,s=!0,o=0,a=0;for(;o<u.length;o++){const e=he(u[o],n,!1,l);if(e instanceof n){if(!t){const t=!!(2&(0|e.u[Z]));r&&=!t,s&&=t}u[a++]=e}}a<o&&(u.length=a),h|=4,h=s?16|h:-17&h,nt(u,h=r?8|h:-9&h),t&&Object.freeze(u)}if(o&&!(8&h||!i.length&&(1===c||4===c&&32&h))){for(We(h)&&(i=at(i),h=an(h,e),e=Ne(t,e,r,i)),n=i,o=h,u=0;u<n.length;u++)(h=n[u])!==(l=Oe(h))&&(n[u]=l);o|=8,nt(n,o=n.length?-17&o:16|o),h=o}return 1===c||4===c&&32&h?We(h)||(e=h,(h|=!i.length||16&h&&(!a||32&h)?2:2048)!==e&&nt(i,h),Object.freeze(i)):(2===c&&We(h)&&(nt(i=at(i),h=cn(h=an(h,e),e,s)),e=Ne(t,e,r,i)),We(h)||(r=h,(h=cn(h,e,s))!==r&&nt(i,h))),i}function rn(t,e,n){const r=0|t.u[Z];return nn(t,r,e,n,je(),!1,!(2&r))}function sn(t,e,n,r){return null==r&&(r=void 0),De(t,n,r)}function on(t,e,n,r){null==r&&(r=void 0);t:{let i=0|(t=t.u)[Z];if(mt(i),null==r){const r=qe(t);if(Ze(r,t,i,n)!==e)break t;r.set(n,0)}else i=Je(t,i,n,e);Ne(t,i,e,r)}}function an(t,e){return-2049&(t=32|(2&e?2|t:-3&t))}function cn(t,e,n){return 32&e&&n||(t&=-33),t}function hn(t,e,n,r){const i=0|t.u[Z];mt(i),t=nn(t,i,n,e,2,!0),r=null!=r?r:new n,t.push(r),t[Z]=2&(0|r.u[Z])?-9&t[Z]:-17&t[Z]}function un(t,e){return Qt(Ce(t,e))}function ln(t,e){return ce(Ce(t,e))}function dn(t,e){return Be(t,e)??0}function fn(t,e,n){if(null!=n&&\"boolean\"!=typeof n)throw t=typeof n,Error(`Expected boolean but got ${\"object\"!=t?t:n?Array.isArray(n)?\"array\":t:\"null\"}: ${n}`);De(t,e,n)}function pn(t,e,n){if(null!=n){if(\"number\"!=typeof n)throw X(\"int32\");if(!Kt(n))throw X(\"int32\");n|=0}De(t,e,n)}function gn(t,e,n){if(null!=n&&\"number\"!=typeof n)throw Error(`Value of float/double field must be a number, found ${typeof n}: ${n}`);De(t,e,n)}function mn(t,e,n){{const o=t.u;let a=0|o[Z];if(mt(a),null==n)Ne(o,a,e);else{var r=t=0|n[Z],i=We(t),s=i||Object.isFrozen(n);for(i||(t=0),s||(n=at(n),r=0,t=cn(t=an(t,a),a,!0),s=!1),t|=21,i=0;i<n.length;i++){const e=n[i],o=oe(e);Object.is(e,o)||(s&&(n=at(n),r=0,t=cn(t=an(t,a),a,!0),s=!1),n[i]=o)}t!==r&&(s&&(n=at(n),t=cn(t=an(t,a),a,!0)),nt(n,t)),Ne(o,a,e,n)}}}function yn(t,e,n){mt(0|t.u[Z]),Ve(t,e,ce,2,!0).push(oe(n))}function _n(t,e){return Error(`Invalid wire type: ${t} (at position ${e})`)}function vn(){return Error(\"Failed to read varint, encoding is invalid.\")}function En(t,e){return Error(`Tried to read past the end of the data ${e} > ${t}`)}function wn(t){if(\"string\"==typeof t)return{buffer:P(t),N:!1};if(Array.isArray(t))return{buffer:new Uint8Array(t),N:!1};if(t.constructor===Uint8Array)return{buffer:t,N:!1};if(t.constructor===ArrayBuffer)return{buffer:new Uint8Array(t),N:!1};if(t.constructor===N)return{buffer:D(t)||new Uint8Array(0),N:!0};if(t instanceof Uint8Array)return{buffer:new Uint8Array(t.buffer,t.byteOffset,t.byteLength),N:!1};throw Error(\"Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers\")}function Tn(t,e){let n,r=0,i=0,s=0;const o=t.h;let a=t.g;do{n=o[a++],r|=(127&n)<<s,s+=7}while(s<32&&128&n);for(s>32&&(i|=(127&n)>>4),s=3;s<32&&128&n;s+=7)n=o[a++],i|=(127&n)<<s;if(Fn(t,a),n<128)return e(r>>>0,i>>>0);throw vn()}function An(t){let e=0,n=t.g;const r=n+10,i=t.h;for(;n<r;){const r=i[n++];if(e|=r,0==(128&r))return Fn(t,n),!!(127&e)}throw vn()}function bn(t){const e=t.h;let n=t.g,r=e[n++],i=127&r;if(128&r&&(r=e[n++],i|=(127&r)<<7,128&r&&(r=e[n++],i|=(127&r)<<14,128&r&&(r=e[n++],i|=(127&r)<<21,128&r&&(r=e[n++],i|=r<<28,128&r&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++])))))throw vn();return Fn(t,n),i}function kn(t){return bn(t)>>>0}function Sn(t){var e=t.h;const n=t.g,r=e[n],i=e[n+1],s=e[n+2];return e=e[n+3],Fn(t,t.g+4),(r<<0|i<<8|s<<16|e<<24)>>>0}function xn(t){var e=Sn(t);t=2*(e>>31)+1;const n=e>>>23&255;return e&=8388607,255==n?e?NaN:t*(1/0):0==n?1401298464324817e-60*t*e:t*Math.pow(2,n-150)*(e+8388608)}function Ln(t){return bn(t)}function Rn(t,e,{ba:n=!1}={}){t.ba=n,e&&(e=wn(e),t.h=e.buffer,t.m=e.N,t.j=0,t.l=t.h.length,t.g=t.j)}function Fn(t,e){if(t.g=e,e>t.l)throw En(t.l,e)}function In(t,e){if(e<0)throw Error(`Tried to read a negative byte length: ${e}`);const n=t.g,r=n+e;if(r>t.l)throw En(e,t.l-n);return t.g=r,n}function Mn(t,e){if(0==e)return U();var n=In(t,e);return t.ba&&t.m?n=t.h.subarray(n,n+e):(t=t.h,n=n===(e=n+e)?new Uint8Array(0):It?t.slice(n,e):new Uint8Array(t.subarray(n,e))),0==n.length?U():new N(n,C)}ye.prototype.toJSON=void 0,ye.prototype.Ia=ut;var Pn=[];function On(t){var e=t.g;if(e.g==e.l)return!1;t.l=t.g.g;var n=kn(t.g);if(e=n>>>3,!((n&=7)>=0&&n<=5))throw _n(n,t.l);if(e<1)throw Error(`Invalid field number: ${e} (at position ${t.l})`);return t.m=e,t.h=n,!0}function Cn(t){switch(t.h){case 0:0!=t.h?Cn(t):An(t.g);break;case 1:Fn(t=t.g,t.g+8);break;case 2:if(2!=t.h)Cn(t);else{var e=kn(t.g);Fn(t=t.g,t.g+e)}break;case 5:Fn(t=t.g,t.g+4);break;case 3:for(e=t.m;;){if(!On(t))throw Error(\"Unmatched start-group tag: stream EOF\");if(4==t.h){if(t.m!=e)throw Error(\"Unmatched end-group tag\");break}Cn(t)}break;default:throw _n(t.h,t.l)}}function Un(t,e,n){const r=t.g.l,i=kn(t.g),s=t.g.g+i;let o=s-r;if(o<=0&&(t.g.l=s,n(e,t,void 0,void 0,void 0),o=s-t.g.g),o)throw Error(`Message parsing ended unexpectedly. Expected to read ${i} bytes, instead read ${i-o} bytes, either the data ended unexpectedly or the message misreported its own length`);return t.g.g=s,t.g.l=r,e}function Dn(t){var o=kn(t.g),a=In(t=t.g,o);if(t=t.h,s){var c,h=t;(c=i)||(c=i=new TextDecoder(\"utf-8\",{fatal:!0})),o=a+o,h=0===a&&o===h.length?h:h.subarray(a,o);try{var u=c.decode(h)}catch(t){if(void 0===r){try{c.decode(new Uint8Array([128]))}catch(t){}try{c.decode(new Uint8Array([97])),r=!0}catch(t){r=!1}}throw!r&&(i=void 0),t}}else{o=(u=a)+o,a=[];let r,i=null;for(;u<o;){var l=t[u++];l<128?a.push(l):l<224?u>=o?e():(r=t[u++],l<194||128!=(192&r)?(u--,e()):a.push((31&l)<<6|63&r)):l<240?u>=o-1?e():(r=t[u++],128!=(192&r)||224===l&&r<160||237===l&&r>=160||128!=(192&(c=t[u++]))?(u--,e()):a.push((15&l)<<12|(63&r)<<6|63&c)):l<=244?u>=o-2?e():(r=t[u++],128!=(192&r)||r-144+(l<<28)>>30!=0||128!=(192&(c=t[u++]))||128!=(192&(h=t[u++]))?(u--,e()):(l=(7&l)<<18|(63&r)<<12|(63&c)<<6|63&h,l-=65536,a.push(55296+(l>>10&1023),56320+(1023&l)))):e(),a.length>=8192&&(i=n(i,a),a.length=0)}u=n(i,a)}return u}function Nn(t){const e=kn(t.g);return Mn(t.g,e)}function Bn(t,e,n){var r=kn(t.g);for(r=t.g.g+r;t.g.g<r;)n.push(e(t.g))}var Gn=[];function jn(t){return t}let Vn;function Xn(t,e,n){e.g?e.m(t,e.g,e.h,n):e.m(t,e.h,n)}var Hn=class{constructor(t,e){this.u=Ie(t,e)}toJSON(){const t=!Vn;try{return t&&(Vn=Re),Wn(this)}finally{t&&(Vn=void 0)}}l(){var t=go;return t.g?t.l(this,t.g,t.h,!0):t.l(this,t.h,t.defaultValue,!0)}clone(){const t=this.u;return new this.constructor(Pe(t,0|t[Z],!1))}N(){return!!(2&(0|this.u[Z]))}};function Wn(t){var e=t.u;{e=(t=Vn(e))!==e;let h=t.length;if(h){var n=t[h-1],r=dt(n);r?h--:n=void 0;var i=t;if(r){t:{var s,o=n,a=!1;if(o)for(let t in o)isNaN(+t)?(s??={})[t]=o[t]:(r=o[t],Array.isArray(r)&&(pt(r)||lt(r)&&0===r.size)&&(r=null),null==r&&(a=!0),null!=r&&((s??={})[t]=r));if(a||(s=o),s)for(let t in s){a=s;break t}a=null}o=null==a?null!=n:a!==n}for(;h>0&&(null==(s=i[h-1])||pt(s)||lt(s)&&0===s.size);h--)var c=!0;(i!==t||o||c)&&(e?(c||o||a)&&(i.length=h):i=Array.prototype.slice.call(i,0,h),a&&i.push(a)),c=i}else c=t}return c}function zn(t){return t?/^\\d+$/.test(t)?(Vt(t),new Kn(Pt,Ot)):null:Yn||=new Kn(0,0)}Hn.prototype.W=ht,Hn.prototype.toString=function(){try{return Vn=jn,Wn(this).toString()}finally{Vn=void 0}};var Kn=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}};let Yn;function $n(t){return t?/^-?\\d+$/.test(t)?(Vt(t),new qn(Pt,Ot)):null:Jn||=new qn(0,0)}var qn=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}};let Jn;function Zn(t,e,n){for(;n>0||e>127;)t.g.push(127&e|128),e=(e>>>7|n<<25)>>>0,n>>>=7;t.g.push(e)}function Qn(t,e){for(;e>127;)t.g.push(127&e|128),e>>>=7;t.g.push(e)}function tr(t,e){if(e>=0)Qn(t,e);else{for(let n=0;n<9;n++)t.g.push(127&e|128),e>>=7;t.g.push(1)}}function er(t,e){t.g.push(e>>>0&255),t.g.push(e>>>8&255),t.g.push(e>>>16&255),t.g.push(e>>>24&255)}function nr(t,e){0!==e.length&&(t.l.push(e),t.h+=e.length)}function rr(t,e,n){Qn(t.g,8*e+n)}function ir(t,e){return rr(t,e,2),e=t.g.end(),nr(t,e),e.push(t.h),e}function sr(t,e){var n=e.pop();for(n=t.h+t.g.length()-n;n>127;)e.push(127&n|128),n>>>=7,t.h++;e.push(n),t.h++}function or(t,e,n){rr(t,e,2),Qn(t.g,n.length),nr(t,t.g.end()),nr(t,n)}function ar(t,e,n,r){null!=n&&(e=ir(t,e),r(n,t),sr(t,e))}function cr(){const t=class{constructor(){throw Error()}};return Object.setPrototypeOf(t,t.prototype),t}var hr=cr(),ur=cr(),lr=cr(),dr=cr(),fr=cr(),pr=cr(),gr=cr(),mr=cr(),yr=cr(),_r=class{constructor(t,e,n){this.g=t,this.h=e,t=hr,this.l=!!t&&n===t||!1}};function vr(t,e){return new _r(t,e,hr)}function Er(t,e,n,r,i){ar(t,n,Ir(e,r),i)}const wr=vr((function(t,e,n,r,i){return 2===t.h&&(Un(t,Qe(e,r,n),i),!0)}),Er),Tr=vr((function(t,e,n,r,i){return 2===t.h&&(Un(t,Qe(e,r,n,!0),i),!0)}),Er);var Ar=Symbol(),br=Symbol(),kr=Symbol(),Sr=Symbol();let xr,Lr;function Rr(t,e,n,r){var i=r[t];if(i)return i;(i={}).Pa=r,i.V=function(t){switch(typeof t){case\"boolean\":return Te||=[0,void 0,!0];case\"number\":return t>0?void 0:0===t?Ae||=[0,void 0]:[-t,void 0];case\"string\":return[0,t];case\"object\":return t}}(r[0]);var s=r[1];let o=1;s&&s.constructor===Object&&(i.ga=s,\"function\"==typeof(s=r[++o])&&(i.la=!0,xr??=s,Lr??=r[o+1],s=r[o+=2]));const a={};for(;s&&Array.isArray(s)&&s.length&&\"number\"==typeof s[0]&&s[0]>0;){for(var c=0;c<s.length;c++)a[s[c]]=s;s=r[++o]}for(c=1;void 0!==s;){let t;\"number\"==typeof s&&(c+=s,s=r[++o]);var h=void 0;if(s instanceof _r?t=s:(t=wr,o--),t?.l){s=r[++o],h=r;var u=o;\"function\"==typeof s&&(s=s(),h[u]=s),h=s}for(u=c+1,\"number\"==typeof(s=r[++o])&&s<0&&(u-=s,s=r[++o]);c<u;c++){const r=a[c];h?n(i,c,t,h,r):e(i,c,t,r)}}return r[t]=i}function Fr(t){return Array.isArray(t)?t[0]instanceof _r?t:[Tr,t]:[t,void 0]}function Ir(t,e){return t instanceof Hn?t.u:Array.isArray(t)?Fe(t,e,!1):void 0}function Mr(t,e,n,r){const i=n.g;t[e]=r?(t,e,n)=>i(t,e,n,r):i}function Pr(t,e,n,r,i){const s=n.g;let o,a;t[e]=(t,e,n)=>s(t,e,n,a||=Rr(br,Mr,Pr,r).V,o||=Or(r),i)}function Or(t){let e=t[kr];if(null!=e)return e;const n=Rr(br,Mr,Pr,t);return e=n.la?(t,e)=>xr(t,e,n):(t,e)=>{const r=0|t[Z];for(;On(e)&&4!=e.h;){var i=e.m,s=n[i];if(null==s){var o=n.ga;o&&(o=o[i])&&(null!=(o=Cr(o))&&(s=n[i]=o))}null!=s&&s(e,t,i)||(i=(s=e).l,Cn(s),s.fa?s=void 0:(o=s.g.g-i,s.g.g=i,s=Mn(s.g,o)),i=t,s&&((o=i[J])?o.push(s):i[J]=[s]))}return 16384&r&&rt(t),!0},t[kr]=e}function Cr(t){const e=(t=Fr(t))[0].g;if(t=t[1]){const n=Or(t),r=Rr(br,Mr,Pr,t).V;return(t,i,s)=>e(t,i,s,r,n)}return e}function Ur(t,e,n){t[e]=n.h}function Dr(t,e,n,r){let i,s;const o=n.h;t[e]=(t,e,n)=>o(t,e,n,s||=Rr(Ar,Ur,Dr,r).V,i||=Nr(r))}function Nr(t){let e=t[Sr];if(!e){const n=Rr(Ar,Ur,Dr,t);e=(t,e)=>Br(t,e,n),t[Sr]=e}return e}function Br(t,e,n){for(var r=0|t[Z],i=512&r?0:-1,s=t.length,o=512&r?1:0,a=s+(256&r?-1:0);o<a;o++){const r=t[o];if(null==r)continue;const s=o-i,a=Gr(n,s);a&&a(e,r,s)}if(256&r){r=t[s-1];for(const t in r)i=+t,Number.isNaN(i)||null!=(s=r[i])&&(a=Gr(n,i))&&a(e,s,i)}if(t=_t(t))for(nr(e,e.g.end()),n=0;n<t.length;n++)nr(e,D(t[n])||new Uint8Array(0))}function Gr(t,e){var n=t[e];if(n)return n;if((n=t.ga)&&(n=n[e])){var r=(n=Fr(n))[0].h;if(n=n[1]){const e=Nr(n),i=Rr(Ar,Ur,Dr,n).V;n=t.la?Lr(i,e):(t,n,s)=>r(t,n,s,i,e)}else n=r;return t[e]=n}}function jr(t,e){if(Array.isArray(e)){var n=0|e[Z];if(4&n)return e;for(var r=0,i=0;r<e.length;r++){const n=t(e[r]);null!=n&&(e[i++]=n)}return i<r&&(e.length=i),nt(e,-12289&(5|n)),2&n&&Object.freeze(e),e}}function Vr(t,e,n){return new _r(t,e,n)}function Xr(t,e,n){return new _r(t,e,n)}function Hr(t,e,n){Ne(t,0|t[Z],e,n)}var Wr=vr((function(t,e,n,r,i){return 2===t.h&&(t=Un(t,Fe([void 0,void 0],r,!0),i),mt(r=0|e[Z]),(i=Ue(e,r,n))instanceof ye?0!=(2&i.L)?((i=i.X()).push(t),Ne(e,r,n,i)):i.Na(t):Array.isArray(i)?(2&(0|i[Z])&&Ne(e,r,n,i=ze(i)),i.push(t)):Ne(e,r,n,[t]),!0)}),(function(t,e,n,r,i){if(e instanceof ye)e.forEach(((e,s)=>{ar(t,n,Fe([s,e],r,!1),i)}));else if(Array.isArray(e))for(let s=0;s<e.length;s++){const o=e[s];Array.isArray(o)&&ar(t,n,Fe(o,r,!1),i)}}));function zr(t,e,n){if(e=function(t){if(null==t)return t;const e=typeof t;if(\"bigint\"===e)return String(Ht(64,t));if(Zt(t)){if(\"string\"===e)return re(t);if(\"number\"===e)return ne(t)}}(e),null!=e){if(\"string\"==typeof e)$n(e);if(null!=e)switch(rr(t,n,0),typeof e){case\"number\":t=t.g,Ut(e),Zn(t,Pt,Ot);break;case\"bigint\":n=BigInt.asUintN(64,e),n=new qn(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),Zn(t.g,n.h,n.g);break;default:n=$n(e),Zn(t.g,n.h,n.g)}}}function Kr(t,e,n){null!=(e=Qt(e))&&null!=e&&(rr(t,n,0),tr(t.g,e))}function Yr(t,e,n){null!=(e=qt(e))&&(rr(t,n,0),t.g.g.push(e?1:0))}function $r(t,e,n){null!=(e=ce(e))&&or(t,n,c(e))}function qr(t,e,n,r,i){ar(t,n,Ir(e,r),i)}function Jr(t,e,n){null!=(e=null==e||\"string\"==typeof e||O(e)||e instanceof N?e:void 0)&&or(t,n,wn(e).buffer)}function Zr(t,e,n){return(5===t.h||2===t.h)&&(e=Ye(e,0|e[Z],n,!1,!1),2==t.h?Bn(t,xn,e):e.push(xn(t.g)),!0)}var Qr=Vr((function(t,e,n){if(1!==t.h)return!1;var r=t.g;t=Sn(r);const i=Sn(r);r=2*(i>>31)+1;const s=i>>>20&2047;return t=4294967296*(1048575&i)+t,Hr(e,n,2047==s?t?NaN:r*(1/0):0==s?5e-324*r*t:r*Math.pow(2,s-1075)*(t+4503599627370496)),!0}),(function(t,e,n){null!=(e=$t(e))&&(rr(t,n,1),t=t.g,(n=Mt||=new DataView(new ArrayBuffer(8))).setFloat64(0,+e,!0),Pt=n.getUint32(0,!0),Ot=n.getUint32(4,!0),er(t,Pt),er(t,Ot))}),cr()),ti=Vr((function(t,e,n){return 5===t.h&&(Hr(e,n,xn(t.g)),!0)}),(function(t,e,n){null!=(e=$t(e))&&(rr(t,n,5),t=t.g,Dt(e),er(t,Pt))}),gr),ei=Xr(Zr,(function(t,e,n){if(null!=(e=jr($t,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&(rr(r,i,5),r=r.g,Dt(s),er(r,Pt))}}),gr),ni=Xr(Zr,(function(t,e,n){if(null!=(e=jr($t,e))&&e.length){rr(t,n,2),Qn(t.g,4*e.length);for(let r=0;r<e.length;r++)n=t.g,Dt(e[r]),er(n,Pt)}}),gr),ri=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,Tn(t.g,Bt)),!0)}),zr,pr),ii=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,0===(t=Tn(t.g,Bt))?void 0:t),!0)}),zr,pr),si=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,Tn(t.g,Nt)),!0)}),(function(t,e,n){if(null!=(e=se(e))){if(\"string\"==typeof e)zn(e);if(null!=e)switch(rr(t,n,0),typeof e){case\"number\":t=t.g,Ut(e),Zn(t,Pt,Ot);break;case\"bigint\":n=BigInt.asUintN(64,e),n=new Kn(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),Zn(t.g,n.h,n.g);break;default:n=zn(e),Zn(t.g,n.h,n.g)}}}),cr()),oi=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,bn(t.g)),!0)}),Kr,dr),ai=Xr((function(t,e,n){return(0===t.h||2===t.h)&&(e=Ye(e,0|e[Z],n,!1,!1),2==t.h?Bn(t,bn,e):e.push(bn(t.g)),!0)}),(function(t,e,n){if(null!=(e=jr(Qt,e))&&e.length){n=ir(t,n);for(let n=0;n<e.length;n++)tr(t.g,e[n]);sr(t,n)}}),dr),ci=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,0===(t=bn(t.g))?void 0:t),!0)}),Kr,dr),hi=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,An(t.g)),!0)}),Yr,ur),ui=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,!1===(t=An(t.g))?void 0:t),!0)}),Yr,ur),li=Xr((function(t,e,n){return 2===t.h&&(t=Dn(t),Ye(e,0|e[Z],n,!1).push(t),!0)}),(function(t,e,n){if(null!=(e=jr(ce,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&or(r,i,c(s))}}),lr),di=Vr((function(t,e,n){return 2===t.h&&(Hr(e,n,\"\"===(t=Dn(t))?void 0:t),!0)}),$r,lr),fi=Vr((function(t,e,n){return 2===t.h&&(Hr(e,n,Dn(t)),!0)}),$r,lr),pi=function(t,e,n=hr){return new _r(t,e,n)}((function(t,e,n,r,i){return 2===t.h&&(r=Fe(void 0,r,!0),Ye(e,0|e[Z],n,!0).push(r),Un(t,r,i),!0)}),(function(t,e,n,r,i){if(Array.isArray(e))for(let s=0;s<e.length;s++)qr(t,e[s],n,r,i)})),gi=vr((function(t,e,n,r,i,s){return 2===t.h&&(Je(e,0|e[Z],s,n),Un(t,e=Qe(e,r,n),i),!0)}),qr),mi=Vr((function(t,e,n){return 2===t.h&&(Hr(e,n,Nn(t)),!0)}),Jr,mr),yi=Xr((function(t,e,n){return(0===t.h||2===t.h)&&(e=Ye(e,0|e[Z],n,!1,!1),2==t.h?Bn(t,kn,e):e.push(kn(t.g)),!0)}),(function(t,e,n){if(null!=(e=jr(te,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&(rr(r,i,0),Qn(r.g,s))}}),fr),_i=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,0===(t=kn(t.g))?void 0:t),!0)}),(function(t,e,n){null!=(e=te(e))&&null!=e&&(rr(t,n,0),Qn(t.g,e))}),fr),vi=Vr((function(t,e,n){return 0===t.h&&(Hr(e,n,bn(t.g)),!0)}),(function(t,e,n){null!=(e=Qt(e))&&(e=parseInt(e,10),rr(t,n,0),tr(t.g,e))}),yr);class Ei{constructor(t,e){this.h=t,this.g=e,this.l=en,this.m=sn,this.defaultValue=void 0}}function wi(t,e){return new Ei(t,e)}function Ti(t,e){return(n,r)=>{if(Gn.length){const t=Gn.pop();t.o(r),Rn(t.g,n,r),n=t}else n=new class{constructor(t,e){if(Pn.length){const n=Pn.pop();Rn(n,t,e),t=n}else t=new class{constructor(t,e){this.h=null,this.m=!1,this.g=this.l=this.j=0,Rn(this,t,e)}clear(){this.h=null,this.m=!1,this.g=this.l=this.j=0,this.ba=!1}}(t,e);this.g=t,this.l=this.g.g,this.h=this.m=-1,this.o(e)}o({fa:t=!1}={}){this.fa=t}}(n,r);try{const r=new t,s=r.u;Or(e)(s,n);var i=r}finally{n.g.clear(),n.m=-1,n.h=-1,Gn.length<100&&Gn.push(n)}return i}}function Ai(t){return function(){const e=new class{constructor(){this.l=[],this.h=0,this.g=new class{constructor(){this.g=[]}length(){return this.g.length}end(){const t=this.g;return this.g=[],t}}}};Br(this.u,e,Rr(Ar,Ur,Dr,t)),nr(e,e.g.end());const n=new Uint8Array(e.h),r=e.l,i=r.length;let s=0;for(let t=0;t<i;t++){const e=r[t];n.set(e,s),s+=e.length}return e.l=[n],n}}var bi=class extends Hn{constructor(t){super(t)}},ki=[0,di,Vr((function(t,e,n){return 2===t.h&&(Hr(e,n,(t=Nn(t))===U()?void 0:t),!0)}),(function(t,e,n){if(null!=e){if(e instanceof Hn){const r=e.Ra;return void(r&&(e=r(e),null!=e&&or(t,n,wn(e).buffer)))}if(Array.isArray(e))return}Jr(t,e,n)}),mr)];let Si,xi=globalThis.trustedTypes;function Li(t){void 0===Si&&(Si=function(){let t=null;if(!xi)return t;try{const e=t=>t;t=xi.createPolicy(\"goog#html\",{createHTML:e,createScript:e,createScriptURL:e})}catch(t){}return t}());var e=Si;return new class{constructor(t){this.g=t}toString(){return this.g+\"\"}}(e?e.createScriptURL(t):t)}function Ri(t,...e){if(0===e.length)return Li(t[0]);let n=t[0];for(let r=0;r<e.length;r++)n+=encodeURIComponent(e[r])+t[r+1];return Li(n)}var Fi=[0,oi,vi,hi,-1,ai,vi,-1],Ii=class extends Hn{constructor(t){super(t)}},Mi=[0,hi,fi,hi,vi,-1,Xr((function(t,e,n){return(0===t.h||2===t.h)&&(e=Ye(e,0|e[Z],n,!1,!1),2==t.h?Bn(t,Ln,e):e.push(bn(t.g)),!0)}),(function(t,e,n){if(null!=(e=jr(Qt,e))&&e.length){n=ir(t,n);for(let n=0;n<e.length;n++)tr(t.g,e[n]);sr(t,n)}}),yr),fi,-1,[0,hi,-1],vi,hi,-1],Pi=[0,fi,-2],Oi=class extends Hn{constructor(t){super(t)}},Ci=[0],Ui=[0,oi,hi,1,hi,-3],Di=class extends Hn{constructor(t){super(t,2)}},Ni={};Ni[336783863]=[0,fi,hi,-1,oi,[0,[1,2,3,4,5,6,7,8],gi,Ci,gi,Mi,gi,Pi,gi,Ui,gi,Fi,gi,[0,fi,-2],gi,[0,fi,vi],gi,[0,vi,fi]],[0,fi],hi,[0,[1,3],[2,4],gi,[0,ai],-1,gi,[0,li],-1,pi,[0,fi,-1]],fi];var Bi=[0,ii,-1,ui,-3,ii,ai,di,ci,ii,-1,ui,ci,ui,-2,di];function Gi(t,e){Ke(t,2,ae(e),\"\")}function ji(t,e){yn(t,3,e)}function Vi(t,e){yn(t,4,e)}var Xi=class extends Hn{constructor(t){super(t,500)}o(t){return sn(this,0,7,t)}},Hi=[-1,{}],Wi=[0,fi,1,Hi],zi=[0,fi,li,Hi];function Ki(t,e){hn(t,1,Xi,e)}function Yi(t,e){yn(t,10,e)}function $i(t,e){yn(t,15,e)}var qi=class extends Hn{constructor(t){super(t,500)}o(t){return sn(this,0,1001,t)}},Ji=[-500,pi,[-500,di,-1,li,-3,[-2,Ni,hi],pi,ki,ci,-1,Wi,zi,pi,[0,di,ui],di,Bi,ci,li,987,li],4,pi,[-500,fi,-1,[-1,{}],998,fi],pi,[-500,fi,li,-1,[-2,{},hi],997,li,-1],ci,pi,[-500,fi,li,Hi,998,li],li,ci,Wi,zi,pi,[0,di,-1,Hi],li,-2,Bi,di,-1,ui,[0,ui,_i],978,Hi,pi,ki];qi.prototype.g=Ai(Ji);var Zi=Ti(qi,Ji),Qi=class extends Hn{constructor(t){super(t)}},ts=class extends Hn{constructor(t){super(t)}g(){return rn(this,Qi,1)}},es=[0,pi,[0,oi,ti,fi,-1]],ns=Ti(ts,es),rs=class extends Hn{constructor(t){super(t)}},is=class extends Hn{constructor(t){super(t)}},ss=class extends Hn{constructor(t){super(t)}h(){return en(this,rs,2)}g(){return rn(this,is,5)}},os=Ti(class extends Hn{constructor(t){super(t)}},[0,li,ai,ni,[0,vi,[0,oi,-3],[0,ti,-3],[0,oi,-1,[0,pi,[0,oi,-2]]],pi,[0,ti,-1,fi,ti]],fi,-1,ri,pi,[0,oi,ti],li,ri]),as=class extends Hn{constructor(t){super(t)}},cs=Ti(class extends Hn{constructor(t){super(t)}},[0,pi,[0,ti,-4]]),hs=class extends Hn{constructor(t){super(t)}},us=Ti(class extends Hn{constructor(t){super(t)}},[0,pi,[0,ti,-4]]),ls=class extends Hn{constructor(t){super(t)}},ds=[0,oi,-1,ni,vi],fs=class extends Hn{constructor(t){super(t)}};fs.prototype.g=Ai([0,ti,-4,ri]);var ps=class extends Hn{constructor(t){super(t)}},gs=Ti(class extends Hn{constructor(t){super(t)}},[0,pi,[0,1,oi,fi,es],ri]),ms=class extends Hn{constructor(t){super(t)}},ys=class extends Hn{constructor(t){super(t)}oa(){const t=Ge(this);return null==t?U():t}},_s=class extends Hn{constructor(t){super(t)}},vs=[1,2],Es=Ti(class extends Hn{constructor(t){super(t)}},[0,pi,[0,vs,gi,[0,ni],gi,[0,mi],oi,fi],ri]),ws=class extends Hn{constructor(t){super(t)}},Ts=[0,fi,oi,ti,li,-1],As=class extends Hn{constructor(t){super(t)}},bs=[0,hi,-1],ks=class extends Hn{constructor(t){super(t)}},Ss=[1,2,3,4,5],xs=class extends Hn{constructor(t){super(t)}g(){return null!=Ge(this)}h(){return null!=ln(this,2)}},Ls=class extends Hn{constructor(t){super(t)}g(){return qt(Ce(this,2))??!1}},Rs=[0,mi,fi,[0,oi,ri,-1],[0,si,ri]],Fs=[0,Rs,hi,[0,Ss,gi,Ui,gi,Mi,gi,Fi,gi,Ci,gi,Pi],vi],Is=class extends Hn{constructor(t){super(t)}},Ms=[0,Fs,ti,-1,oi],Ps=wi(502141897,Is);Ni[502141897]=Ms;var Os=Ti(class extends Hn{constructor(t){super(t)}},[0,[0,vi,-1,ei,yi],ds]),Cs=class extends Hn{constructor(t){super(t)}},Us=class extends Hn{constructor(t){super(t)}},Ds=[0,Fs,ti,[0,Fs],hi],Ns=[0,Fs,Ms,Ds,ti,[0,[0,Rs]]],Bs=wi(508968150,Us);Ni[508968150]=Ns,Ni[508968149]=Ds;var Gs=class extends Hn{constructor(t){super(t)}},js=wi(513916220,Gs);Ni[513916220]=[0,Fs,Ns,oi];var Vs=class extends Hn{constructor(t){super(t)}h(){return en(this,ws,2)}g(){De(this,2)}},Xs=[0,Fs,Ts];Ni[478825465]=Xs;var Hs=class extends Hn{constructor(t){super(t)}},Ws=class extends Hn{constructor(t){super(t)}},zs=class extends Hn{constructor(t){super(t)}},Ks=class extends Hn{constructor(t){super(t)}},Ys=class extends Hn{constructor(t){super(t)}},$s=[0,Fs,[0,Fs],Xs,-1],qs=[0,Fs,ti,oi],Js=[0,Fs,ti],Zs=[0,Fs,qs,Js,ti],Qs=wi(479097054,Ys);Ni[479097054]=[0,Fs,Zs,$s],Ni[463370452]=$s,Ni[464864288]=qs;var to=wi(462713202,Ks);Ni[462713202]=Zs,Ni[474472470]=Js;var eo=class extends Hn{constructor(t){super(t)}},no=class extends Hn{constructor(t){super(t)}},ro=class extends Hn{constructor(t){super(t)}},io=class extends Hn{constructor(t){super(t)}},so=[0,Fs,ti,-1,oi],oo=[0,Fs,ti,hi];io.prototype.g=Ai([0,Fs,Js,[0,Fs],Ms,Ds,so,oo]);var ao=class extends Hn{constructor(t){super(t)}},co=wi(456383383,ao);Ni[456383383]=[0,Fs,Ts];var ho=class extends Hn{constructor(t){super(t)}},uo=wi(476348187,ho);Ni[476348187]=[0,Fs,bs];var lo=class extends Hn{constructor(t){super(t)}},fo=class extends Hn{constructor(t){super(t)}},po=[0,vi,-1],go=wi(458105876,class extends Hn{constructor(t){super(t)}g(){var t=this.u;const e=0|t[Z];const n=2&e;return t=function(t,e,n){var r=fo;const i=2&e;let s=!1;if(null==n){if(i)return be();n=[]}else if(n.constructor===ye){if(0==(2&n.L)||i)return n;n=n.X()}else Array.isArray(n)?s=!!(2&(0|n[Z])):n=[];if(i){if(!n.length)return be();s||(s=!0,rt(n))}else s&&(s=!1,n=ze(n));return s||(64&(0|n[Z])?n[Z]&=-33:32&e&&et(n,32)),Ne(t,e,2,r=new ye(n,r,ue,void 0)),r}(t,e,Ue(t,e,2)),!n&&fo&&(t.ra=!0),t}});Ni[458105876]=[0,po,Wr,[!0,ri,[0,fi,-1,li]]];var mo=class extends Hn{constructor(t){super(t)}},yo=wi(458105758,mo);Ni[458105758]=[0,Fs,fi,po];var _o=class extends Hn{constructor(t){super(t)}},vo=wi(443442058,_o);Ni[443442058]=[0,Fs,fi,oi,ti,li,-1,hi,ti],Ni[514774813]=so;var Eo=class extends Hn{constructor(t){super(t)}},wo=wi(516587230,Eo);function To(t,e){return e=e?e.clone():new ws,void 0!==t.displayNamesLocale?De(e,1,ae(t.displayNamesLocale)):void 0===t.displayNamesLocale&&De(e,1),void 0!==t.maxResults?pn(e,2,t.maxResults):\"maxResults\"in t&&De(e,2),void 0!==t.scoreThreshold?gn(e,3,t.scoreThreshold):\"scoreThreshold\"in t&&De(e,3),void 0!==t.categoryAllowlist?mn(e,4,t.categoryAllowlist):\"categoryAllowlist\"in t&&De(e,4),void 0!==t.categoryDenylist?mn(e,5,t.categoryDenylist):\"categoryDenylist\"in t&&De(e,5),e}function Ao(t,e=-1,n=\"\"){return{categories:t.map((t=>({index:un(t,1)??0??-1,score:dn(t,2)??0,categoryName:ln(t,3)??\"\"??\"\",displayName:ln(t,4)??\"\"??\"\"}))),headIndex:e,headName:n}}function bo(t){var e=Ve(t,3,$t,je()),n=Ve(t,2,Qt,je()),r=Ve(t,1,ce,je()),i=Ve(t,9,ce,je());const s={categories:[],keypoints:[]};for(let t=0;t<e.length;t++)s.categories.push({score:e[t],index:n[t]??-1,categoryName:r[t]??\"\",displayName:i[t]??\"\"});if((e=en(t,ss,4)?.h())&&(s.boundingBox={originX:un(e,1)??0,originY:un(e,2)??0,width:un(e,3)??0,height:un(e,4)??0,angle:0}),en(t,ss,4)?.g().length)for(const e of en(t,ss,4).g())s.keypoints.push({x:Be(e,1)??0,y:Be(e,2)??0,score:Be(e,4)??0,label:ln(e,3)??\"\"});return s}function ko(t){const e=[];for(const n of rn(t,hs,1))e.push({x:dn(n,1)??0,y:dn(n,2)??0,z:dn(n,3)??0,visibility:dn(n,4)??0});return e}function So(t){const e=[];for(const n of rn(t,as,1))e.push({x:dn(n,1)??0,y:dn(n,2)??0,z:dn(n,3)??0,visibility:dn(n,4)??0});return e}function xo(t){return Array.from(t,(t=>t>127?t-256:t))}function Lo(t,e){if(t.length!==e.length)throw Error(`Cannot compute cosine similarity between embeddings of different sizes (${t.length} vs. ${e.length}).`);let n=0,r=0,i=0;for(let s=0;s<t.length;s++)n+=t[s]*e[s],r+=t[s]*t[s],i+=e[s]*e[s];if(r<=0||i<=0)throw Error(\"Cannot compute cosine similarity on embedding with 0 norm.\");return n/Math.sqrt(r*i)}let Ro;Ni[516587230]=[0,Fs,so,oo,ti],Ni[518928384]=oo;const Fo=new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11]);async function Io(){if(void 0===Ro)try{await WebAssembly.instantiate(Fo),Ro=!0}catch{Ro=!1}return Ro}async function Mo(t,e=Ri``){const n=await Io()?\"wasm_internal\":\"wasm_nosimd_internal\";return{wasmLoaderPath:`${e}/${t}_${n}.js`,wasmBinaryPath:`${e}/${t}_${n}.wasm`}}var Po=class{};function Oo(){var t=navigator;return\"undefined\"!=typeof OffscreenCanvas&&(!function(t=navigator){return(t=t.userAgent).includes(\"Safari\")&&!t.includes(\"Chrome\")}(t)||!!((t=t.userAgent.match(/Version\\/([\\d]+).*Safari/))&&t.length>=1&&Number(t[1])>=17))}async function Co(t){if(\"function\"!=typeof importScripts){const e=document.createElement(\"script\");return e.src=t.toString(),e.crossOrigin=\"anonymous\",new Promise(((t,n)=>{e.addEventListener(\"load\",(()=>{t()}),!1),e.addEventListener(\"error\",(t=>{n(t)}),!1),document.body.appendChild(e)}))}importScripts(t.toString())}function Uo(t){return void 0!==t.videoWidth?[t.videoWidth,t.videoHeight]:void 0!==t.naturalWidth?[t.naturalWidth,t.naturalHeight]:void 0!==t.displayWidth?[t.displayWidth,t.displayHeight]:[t.width,t.height]}function Do(t,e,n){t.m||console.error(\"No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target\"),n(e=t.i.stringToNewUTF8(e)),t.i._free(e)}function No(t,e,n){if(!t.i.canvas)throw Error(\"No OpenGL canvas configured.\");if(n?t.i._bindTextureToStream(n):t.i._bindTextureToCanvas(),!(n=t.i.canvas.getContext(\"webgl2\")||t.i.canvas.getContext(\"webgl\")))throw Error(\"Failed to obtain WebGL context from the provided canvas. `getContext()` should only be invoked with `webgl` or `webgl2`.\");t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!0),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,e),t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!1);const[r,i]=Uo(e);return!t.l||r===t.i.canvas.width&&i===t.i.canvas.height||(t.i.canvas.width=r,t.i.canvas.height=i),[r,i]}function Bo(t,e,n){t.m||console.error(\"No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target\");const r=new Uint32Array(e.length);for(let n=0;n<e.length;n++)r[n]=t.i.stringToNewUTF8(e[n]);e=t.i._malloc(4*r.length),t.i.HEAPU32.set(r,e>>2),n(e);for(const e of r)t.i._free(e);t.i._free(e)}function Go(t,e,n){t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=n}function jo(t,e,n){let r=[];t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=(t,e,i)=>{e?(n(r,i),r=[]):r.push(t)}}Po.forVisionTasks=function(t){return Mo(\"vision\",t)},Po.forTextTasks=function(t){return Mo(\"text\",t)},Po.forGenAiExperimentalTasks=function(t){return Mo(\"genai_experimental\",t)},Po.forGenAiTasks=function(t){return Mo(\"genai\",t)},Po.forAudioTasks=function(t){return Mo(\"audio\",t)},Po.isSimdSupported=function(){return Io()};async function Vo(t,e,n,r){return t=await(async(t,e,n,r,i)=>{if(e&&await Co(e),!self.ModuleFactory)throw Error(\"ModuleFactory not set.\");if(n&&(await Co(n),!self.ModuleFactory))throw Error(\"ModuleFactory not set.\");return self.Module&&i&&((e=self.Module).locateFile=i.locateFile,i.mainScriptUrlOrBlob&&(e.mainScriptUrlOrBlob=i.mainScriptUrlOrBlob)),i=await self.ModuleFactory(self.Module||i),self.ModuleFactory=self.Module=void 0,new t(i,r)})(t,n.wasmLoaderPath,n.assetLoaderPath,e,{locateFile:t=>t.endsWith(\".wasm\")?n.wasmBinaryPath.toString():n.assetBinaryPath&&t.endsWith(\".data\")?n.assetBinaryPath.toString():t}),await t.o(r),t}function Xo(t,e){const n=en(t.baseOptions,xs,1)||new xs;\"string\"==typeof e?(De(n,2,ae(e)),De(n,1)):e instanceof Uint8Array&&(De(n,1,ft(e,!1)),De(n,2)),sn(t.baseOptions,0,1,n)}function Ho(t){try{const e=t.G.length;if(1===e)throw Error(t.G[0].message);if(e>1)throw Error(\"Encountered multiple errors: \"+t.G.map((t=>t.message)).join(\", \"))}finally{t.G=[]}}function Wo(t,e){t.B=Math.max(t.B,e)}function zo(t,e){t.A=new Xi,Gi(t.A,\"PassThroughCalculator\"),ji(t.A,\"free_memory\"),Vi(t.A,\"free_memory_unused_out\"),Yi(e,\"free_memory\"),Ki(e,t.A)}function Ko(t,e){ji(t.A,e),Vi(t.A,e+\"_unused_out\")}function Yo(t){t.g.addBoolToStream(!0,\"free_memory\",t.B)}var $o=class{constructor(t){this.g=t,this.G=[],this.B=0,this.g.setAutoRenderToScreen(!1)}l(t,e=!0){if(e){const e=t.baseOptions||{};if(t.baseOptions?.modelAssetBuffer&&t.baseOptions?.modelAssetPath)throw Error(\"Cannot set both baseOptions.modelAssetPath and baseOptions.modelAssetBuffer\");if(!(en(this.baseOptions,xs,1)?.g()||en(this.baseOptions,xs,1)?.h()||t.baseOptions?.modelAssetBuffer||t.baseOptions?.modelAssetPath))throw Error(\"Either baseOptions.modelAssetPath or baseOptions.modelAssetBuffer must be set\");if(function(t,e){let n=en(t.baseOptions,ks,3);if(!n){var r=n=new ks,i=new Oi;on(r,4,Ss,i)}\"delegate\"in e&&(\"GPU\"===e.delegate?(e=n,r=new Ii,on(e,2,Ss,r)):(e=n,r=new Oi,on(e,4,Ss,r))),sn(t.baseOptions,0,3,n)}(this,e),e.modelAssetPath)return fetch(e.modelAssetPath.toString()).then((t=>{if(t.ok)return t.arrayBuffer();throw Error(`Failed to fetch model: ${e.modelAssetPath} (${t.status})`)})).then((t=>{try{this.g.i.FS_unlink(\"/model.dat\")}catch{}this.g.i.FS_createDataFile(\"/\",\"model.dat\",new Uint8Array(t),!0,!1,!1),Xo(this,\"/model.dat\"),this.m(),this.I()}));if(e.modelAssetBuffer instanceof Uint8Array)Xo(this,e.modelAssetBuffer);else if(e.modelAssetBuffer)return async function(t){const e=[];for(var n=0;;){const{done:r,value:i}=await t.read();if(r)break;e.push(i),n+=i.length}if(0===e.length)return new Uint8Array(0);if(1===e.length)return e[0];t=new Uint8Array(n),n=0;for(const r of e)t.set(r,n),n+=r.length;return t}(e.modelAssetBuffer).then((t=>{Xo(this,t),this.m(),this.I()}))}return this.m(),this.I(),Promise.resolve()}I(){}da(){let t;if(this.g.da((e=>{t=Zi(e)})),!t)throw Error(\"Failed to retrieve CalculatorGraphConfig\");return t}setGraph(t,e){this.g.attachErrorListener(((t,e)=>{this.G.push(Error(e))})),this.g.La(),this.g.setGraph(t,e),this.A=void 0,Ho(this)}finishProcessing(){this.g.finishProcessing(),Ho(this)}close(){this.A=void 0,this.g.closeGraph()}};function qo(t,e){if(!t)throw Error(`Unable to obtain required WebGL resource: ${e}`);return t}$o.prototype.close=$o.prototype.close,function(e,n){e=e.split(\".\");var r,i=t;e[0]in i||void 0===i.execScript||i.execScript(\"var \"+e[0]);for(;e.length&&(r=e.shift());)e.length||void 0===n?i=i[r]&&i[r]!==Object.prototype[r]?i[r]:i[r]={}:i[r]=n}(\"TaskRunner\",$o);class Jo{constructor(t,e,n,r){this.g=t,this.h=e,this.m=n,this.l=r}bind(){this.g.bindVertexArray(this.h)}close(){this.g.deleteVertexArray(this.h),this.g.deleteBuffer(this.m),this.g.deleteBuffer(this.l)}}function Zo(t,e,n){const r=t.g;if(n=qo(r.createShader(n),\"Failed to create WebGL shader\"),r.shaderSource(n,e),r.compileShader(n),!r.getShaderParameter(n,r.COMPILE_STATUS))throw Error(`Could not compile WebGL shader: ${r.getShaderInfoLog(n)}`);return r.attachShader(t.h,n),n}function Qo(t,e){const n=t.g,r=qo(n.createVertexArray(),\"Failed to create vertex array\");n.bindVertexArray(r);const i=qo(n.createBuffer(),\"Failed to create buffer\");n.bindBuffer(n.ARRAY_BUFFER,i),n.enableVertexAttribArray(t.O),n.vertexAttribPointer(t.O,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),n.STATIC_DRAW);const s=qo(n.createBuffer(),\"Failed to create buffer\");return n.bindBuffer(n.ARRAY_BUFFER,s),n.enableVertexAttribArray(t.I),n.vertexAttribPointer(t.I,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array(e?[0,1,0,0,1,0,1,1]:[0,0,0,1,1,1,1,0]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),n.bindVertexArray(null),new Jo(n,r,i,s)}function ta(t,e){if(t.g){if(e!==t.g)throw Error(\"Cannot change GL context once initialized\")}else t.g=e}function ea(t,e,n,r){return ta(t,e),t.h||(t.m(),t.C()),n?(t.s||(t.s=Qo(t,!0)),n=t.s):(t.v||(t.v=Qo(t,!1)),n=t.v),e.useProgram(t.h),n.bind(),t.l(),t=r(),n.g.bindVertexArray(null),t}function na(t,e,n){return ta(t,e),t=qo(e.createTexture(),\"Failed to create texture\"),e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,n??e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,n??e.LINEAR),e.bindTexture(e.TEXTURE_2D,null),t}function ra(t,e,n){ta(t,e),t.A||(t.A=qo(e.createFramebuffer(),\"Failed to create framebuffe.\")),e.bindFramebuffer(e.FRAMEBUFFER,t.A),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,n,0)}function ia(t){t.g?.bindFramebuffer(t.g.FRAMEBUFFER,null)}var sa=class{G(){return\"\\n  precision mediump float;\\n  varying vec2 vTex;\\n  uniform sampler2D inputTexture;\\n  void main() {\\n    gl_FragColor = texture2D(inputTexture, vTex);\\n  }\\n \"}m(){const t=this.g;if(this.h=qo(t.createProgram(),\"Failed to create WebGL program\"),this.aa=Zo(this,\"\\n  attribute vec2 aVertex;\\n  attribute vec2 aTex;\\n  varying vec2 vTex;\\n  void main(void) {\\n    gl_Position = vec4(aVertex, 0.0, 1.0);\\n    vTex = aTex;\\n  }\",t.VERTEX_SHADER),this.Z=Zo(this,this.G(),t.FRAGMENT_SHADER),t.linkProgram(this.h),!t.getProgramParameter(this.h,t.LINK_STATUS))throw Error(`Error during program linking: ${t.getProgramInfoLog(this.h)}`);this.O=t.getAttribLocation(this.h,\"aVertex\"),this.I=t.getAttribLocation(this.h,\"aTex\")}C(){}l(){}close(){if(this.h){const t=this.g;t.deleteProgram(this.h),t.deleteShader(this.aa),t.deleteShader(this.Z)}this.A&&this.g.deleteFramebuffer(this.A),this.v&&this.v.close(),this.s&&this.s.close()}};var oa=class extends sa{G(){return\"\\n  precision mediump float;\\n  uniform sampler2D backgroundTexture;\\n  uniform sampler2D maskTexture;\\n  uniform sampler2D colorMappingTexture;\\n  varying vec2 vTex;\\n  void main() {\\n    vec4 backgroundColor = texture2D(backgroundTexture, vTex);\\n    float category = texture2D(maskTexture, vTex).r;\\n    vec4 categoryColor = texture2D(colorMappingTexture, vec2(category, 0.0));\\n    gl_FragColor = mix(backgroundColor, categoryColor, categoryColor.a);\\n  }\\n \"}C(){const t=this.g;t.activeTexture(t.TEXTURE1),this.B=na(this,t,t.LINEAR),t.activeTexture(t.TEXTURE2),this.j=na(this,t,t.NEAREST)}m(){super.m();const t=this.g;this.K=qo(t.getUniformLocation(this.h,\"backgroundTexture\"),\"Uniform location\"),this.T=qo(t.getUniformLocation(this.h,\"colorMappingTexture\"),\"Uniform location\"),this.J=qo(t.getUniformLocation(this.h,\"maskTexture\"),\"Uniform location\")}l(){super.l();const t=this.g;t.uniform1i(this.J,0),t.uniform1i(this.K,1),t.uniform1i(this.T,2)}close(){this.B&&this.g.deleteTexture(this.B),this.j&&this.g.deleteTexture(this.j),super.close()}},aa=class extends sa{G(){return\"\\n  precision mediump float;\\n  uniform sampler2D maskTexture;\\n  uniform sampler2D defaultTexture;\\n  uniform sampler2D overlayTexture;\\n  varying vec2 vTex;\\n  void main() {\\n    float confidence = texture2D(maskTexture, vTex).r;\\n    vec4 defaultColor = texture2D(defaultTexture, vTex);\\n    vec4 overlayColor = texture2D(overlayTexture, vTex);\\n    // Apply the alpha from the overlay and merge in the default color\\n    overlayColor = mix(defaultColor, overlayColor, overlayColor.a);\\n    gl_FragColor = mix(defaultColor, overlayColor, confidence);\\n  }\\n \"}C(){const t=this.g;t.activeTexture(t.TEXTURE1),this.j=na(this,t),t.activeTexture(t.TEXTURE2),this.B=na(this,t)}m(){super.m();const t=this.g;this.J=qo(t.getUniformLocation(this.h,\"defaultTexture\"),\"Uniform location\"),this.K=qo(t.getUniformLocation(this.h,\"overlayTexture\"),\"Uniform location\"),this.H=qo(t.getUniformLocation(this.h,\"maskTexture\"),\"Uniform location\")}l(){super.l();const t=this.g;t.uniform1i(this.H,0),t.uniform1i(this.J,1),t.uniform1i(this.K,2)}close(){this.j&&this.g.deleteTexture(this.j),this.B&&this.g.deleteTexture(this.B),super.close()}};function ca(t,e){switch(e){case 0:return t.g.find((t=>t instanceof Uint8Array));case 1:return t.g.find((t=>t instanceof Float32Array));case 2:return t.g.find((t=>\"undefined\"!=typeof WebGLTexture&&t instanceof WebGLTexture));default:throw Error(`Type is not supported: ${e}`)}}function ha(t){var e=ca(t,1);if(!e){if(e=ca(t,0))e=new Float32Array(e).map((t=>t/255));else{e=new Float32Array(t.width*t.height);const r=la(t);var n=fa(t);if(ra(n,r,ua(t)),\"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod\".split(\";\").includes(navigator.platform)||navigator.userAgent.includes(\"Mac\")&&\"document\"in self&&\"ontouchend\"in self.document){n=new Float32Array(t.width*t.height*4),r.readPixels(0,0,t.width,t.height,r.RGBA,r.FLOAT,n);for(let t=0,r=0;t<e.length;++t,r+=4)e[t]=n[r]}else r.readPixels(0,0,t.width,t.height,r.RED,r.FLOAT,e)}t.g.push(e)}return e}function ua(t){let e=ca(t,2);if(!e){const n=la(t);e=pa(t);const r=ha(t),i=da(t);n.texImage2D(n.TEXTURE_2D,0,i,t.width,t.height,0,n.RED,n.FLOAT,r),ga(t)}return e}function la(t){if(!t.canvas)throw Error(\"Conversion to different image formats require that a canvas is passed when initializing the image.\");return t.h||(t.h=qo(t.canvas.getContext(\"webgl2\"),\"You cannot use a canvas that is already bound to a different type of rendering context.\")),t.h}function da(t){if(t=la(t),!ma)if(t.getExtension(\"EXT_color_buffer_float\")&&t.getExtension(\"OES_texture_float_linear\")&&t.getExtension(\"EXT_float_blend\"))ma=t.R32F;else{if(!t.getExtension(\"EXT_color_buffer_half_float\"))throw Error(\"GPU does not fully support 4-channel float32 or float16 formats\");ma=t.R16F}return ma}function fa(t){return t.l||(t.l=new sa),t.l}function pa(t){const e=la(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=ca(t,2);return n||(n=na(fa(t),e,t.m?e.LINEAR:e.NEAREST),t.g.push(n),t.j=!0),e.bindTexture(e.TEXTURE_2D,n),n}function ga(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}var ma,ya=class{constructor(t,e,n,r,i,s,o){this.g=t,this.m=e,this.j=n,this.canvas=r,this.l=i,this.width=s,this.height=o,this.j&&(0===--_a&&console.error(\"You seem to be creating MPMask instances without invoking .close(). This leaks resources.\"))}Fa(){return!!ca(this,0)}ja(){return!!ca(this,1)}P(){return!!ca(this,2)}ia(){return(e=ca(t=this,0))||(e=ha(t),e=new Uint8Array(e.map((t=>255*t))),t.g.push(e)),e;var t,e}ha(){return ha(this)}M(){return ua(this)}clone(){const t=[];for(const e of this.g){let n;if(e instanceof Uint8Array)n=new Uint8Array(e);else if(e instanceof Float32Array)n=new Float32Array(e);else{if(!(e instanceof WebGLTexture))throw Error(`Type is not supported: ${e}`);{const t=la(this),e=fa(this);t.activeTexture(t.TEXTURE1),n=na(e,t,this.m?t.LINEAR:t.NEAREST),t.bindTexture(t.TEXTURE_2D,n);const r=da(this);t.texImage2D(t.TEXTURE_2D,0,r,this.width,this.height,0,t.RED,t.FLOAT,null),t.bindTexture(t.TEXTURE_2D,null),ra(e,t,n),ea(e,t,!1,(()=>{pa(this),t.clearColor(0,0,0,0),t.clear(t.COLOR_BUFFER_BIT),t.drawArrays(t.TRIANGLE_FAN,0,4),ga(this)})),ia(e),ga(this)}}t.push(n)}return new ya(t,this.m,this.P(),this.canvas,this.l,this.width,this.height)}close(){this.j&&la(this).deleteTexture(ca(this,2)),_a=-1}};ya.prototype.close=ya.prototype.close,ya.prototype.clone=ya.prototype.clone,ya.prototype.getAsWebGLTexture=ya.prototype.M,ya.prototype.getAsFloat32Array=ya.prototype.ha,ya.prototype.getAsUint8Array=ya.prototype.ia,ya.prototype.hasWebGLTexture=ya.prototype.P,ya.prototype.hasFloat32Array=ya.prototype.ja,ya.prototype.hasUint8Array=ya.prototype.Fa;var _a=250;const va={color:\"white\",lineWidth:4,radius:6};function Ea(t){return{...va,fillColor:(t=t||{}).color,...t}}function wa(t,e){return t instanceof Function?t(e):t}function Ta(t,e,n){return Math.max(Math.min(e,n),Math.min(Math.max(e,n),t))}function Aa(t){if(!t.l)throw Error(\"CPU rendering requested but CanvasRenderingContext2D not provided.\");return t.l}function ba(t){if(!t.j)throw Error(\"GPU rendering requested but WebGL2RenderingContext not provided.\");return t.j}function ka(t,e,n){if(e.P())n(e.M());else{const r=e.ja()?e.ha():e.ia();t.m=t.m??new sa;const i=ba(t);n((t=new ya([r],e.m,!1,i.canvas,t.m,e.width,e.height)).M()),t.close()}}function Sa(t,e,n,r){const i=function(t){return t.g||(t.g=new oa),t.g}(t),s=ba(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n;ea(i,s,!0,(()=>{!function(t,e,n,r){const i=t.g;if(i.activeTexture(i.TEXTURE0),i.bindTexture(i.TEXTURE_2D,e),i.activeTexture(i.TEXTURE1),i.bindTexture(i.TEXTURE_2D,t.B),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,i.RGBA,i.UNSIGNED_BYTE,n),t.H&&function(t,e){if(t!==e)return!1;t=t.entries(),e=e.entries();for(const[r,i]of t){t=r;const s=i;var n=e.next();if(n.done)return!1;const[o,a]=n.value;if(n=a,t!==o||s[0]!==n[0]||s[1]!==n[1]||s[2]!==n[2]||s[3]!==n[3])return!1}return!!e.next().done}(t.H,r))i.activeTexture(i.TEXTURE2),i.bindTexture(i.TEXTURE_2D,t.j);else{t.H=r;const e=Array(1024).fill(0);r.forEach(((t,n)=>{if(4!==t.length)throw Error(`Color at index ${n} is not a four-channel value.`);e[4*n]=t[0],e[4*n+1]=t[1],e[4*n+2]=t[2],e[4*n+3]=t[3]})),i.activeTexture(i.TEXTURE2),i.bindTexture(i.TEXTURE_2D,t.j),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,256,1,0,i.RGBA,i.UNSIGNED_BYTE,new Uint8Array(e))}}(i,e,o,r),s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT),s.drawArrays(s.TRIANGLE_FAN,0,4);const t=i.g;t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,null)}))}function xa(t,e,n,r){const i=ba(t),s=function(t){return t.h||(t.h=new aa),t.h}(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n,a=Array.isArray(r)?new ImageData(new Uint8ClampedArray(r),1,1):r;ea(s,i,!0,(()=>{var t=s.g;t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,s.j),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,o),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,s.B),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,a),i.clearColor(0,0,0,0),i.clear(i.COLOR_BUFFER_BIT),i.drawArrays(i.TRIANGLE_FAN,0,4),i.bindTexture(i.TEXTURE_2D,null),(t=s.g).activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,null)}))}var La=class{constructor(t,e){t instanceof CanvasRenderingContext2D||t instanceof OffscreenCanvasRenderingContext2D?(this.l=t,this.j=e):this.j=t}ya(t,e){if(t){var n=Aa(this);e=Ea(e),n.save();var r=n.canvas,i=0;for(const s of t)n.fillStyle=wa(e.fillColor,{index:i,from:s}),n.strokeStyle=wa(e.color,{index:i,from:s}),n.lineWidth=wa(e.lineWidth,{index:i,from:s}),(t=new Path2D).arc(s.x*r.width,s.y*r.height,wa(e.radius,{index:i,from:s}),0,2*Math.PI),n.fill(t),n.stroke(t),++i;n.restore()}}xa(t,e,n){if(t&&e){var r=Aa(this);n=Ea(n),r.save();var i=r.canvas,s=0;for(const o of e){r.beginPath(),e=t[o.start];const a=t[o.end];e&&a&&(r.strokeStyle=wa(n.color,{index:s,from:e,to:a}),r.lineWidth=wa(n.lineWidth,{index:s,from:e,to:a}),r.moveTo(e.x*i.width,e.y*i.height),r.lineTo(a.x*i.width,a.y*i.height)),++s,r.stroke()}r.restore()}}ua(t,e){const n=Aa(this);e=Ea(e),n.save(),n.beginPath(),n.lineWidth=wa(e.lineWidth,{}),n.strokeStyle=wa(e.color,{}),n.fillStyle=wa(e.fillColor,{}),n.moveTo(t.originX,t.originY),n.lineTo(t.originX+t.width,t.originY),n.lineTo(t.originX+t.width,t.originY+t.height),n.lineTo(t.originX,t.originY+t.height),n.lineTo(t.originX,t.originY),n.stroke(),n.fill(),n.restore()}va(t,e,n=[0,0,0,255]){this.l?function(t,e,n,r){const i=ba(t);ka(t,e,(e=>{Sa(t,e,n,r),(e=Aa(t)).drawImage(i.canvas,0,0,e.canvas.width,e.canvas.height)}))}(this,t,n,e):Sa(this,t.M(),n,e)}wa(t,e,n){this.l?function(t,e,n,r){const i=ba(t);ka(t,e,(e=>{xa(t,e,n,r),(e=Aa(t)).drawImage(i.canvas,0,0,e.canvas.width,e.canvas.height)}))}(this,t,e,n):xa(this,t.M(),e,n)}close(){this.g?.close(),this.g=void 0,this.h?.close(),this.h=void 0,this.m?.close(),this.m=void 0}};function Ra(t,e){switch(e){case 0:return t.g.find((t=>t instanceof ImageData));case 1:return t.g.find((t=>\"undefined\"!=typeof ImageBitmap&&t instanceof ImageBitmap));case 2:return t.g.find((t=>\"undefined\"!=typeof WebGLTexture&&t instanceof WebGLTexture));default:throw Error(`Type is not supported: ${e}`)}}function Fa(t){var e=Ra(t,0);if(!e){e=Ma(t);const n=Pa(t),r=new Uint8Array(t.width*t.height*4);ra(n,e,Ia(t)),e.readPixels(0,0,t.width,t.height,e.RGBA,e.UNSIGNED_BYTE,r),ia(n),e=new ImageData(new Uint8ClampedArray(r.buffer),t.width,t.height),t.g.push(e)}return e}function Ia(t){let e=Ra(t,2);if(!e){const n=Ma(t);e=Oa(t);const r=Ra(t,1)||Fa(t);n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,r),Ca(t)}return e}function Ma(t){if(!t.canvas)throw Error(\"Conversion to different image formats require that a canvas is passed when initializing the image.\");return t.h||(t.h=qo(t.canvas.getContext(\"webgl2\"),\"You cannot use a canvas that is already bound to a different type of rendering context.\")),t.h}function Pa(t){return t.l||(t.l=new sa),t.l}function Oa(t){const e=Ma(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=Ra(t,2);return n||(n=na(Pa(t),e),t.g.push(n),t.m=!0),e.bindTexture(e.TEXTURE_2D,n),n}function Ca(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}function Ua(t){const e=Ma(t);return ea(Pa(t),e,!0,(()=>function(t,e){const n=t.canvas;if(n.width===t.width&&n.height===t.height)return e();const r=n.width,i=n.height;return n.width=t.width,n.height=t.height,t=e(),n.width=r,n.height=i,t}(t,(()=>{if(e.bindFramebuffer(e.FRAMEBUFFER,null),e.clearColor(0,0,0,0),e.clear(e.COLOR_BUFFER_BIT),e.drawArrays(e.TRIANGLE_FAN,0,4),!(t.canvas instanceof OffscreenCanvas))throw Error(\"Conversion to ImageBitmap requires that the MediaPipe Tasks is initialized with an OffscreenCanvas\");return t.canvas.transferToImageBitmap()}))))}La.prototype.close=La.prototype.close,La.prototype.drawConfidenceMask=La.prototype.wa,La.prototype.drawCategoryMask=La.prototype.va,La.prototype.drawBoundingBox=La.prototype.ua,La.prototype.drawConnectors=La.prototype.xa,La.prototype.drawLandmarks=La.prototype.ya,La.lerp=function(t,e,n,r,i){return Ta(r*(1-(t-e)/(n-e))+i*(1-(n-t)/(n-e)),r,i)},La.clamp=Ta;var Da=class{constructor(t,e,n,r,i,s,o){this.g=t,this.j=e,this.m=n,this.canvas=r,this.l=i,this.width=s,this.height=o,(this.j||this.m)&&(0===--Na&&console.error(\"You seem to be creating MPImage instances without invoking .close(). This leaks resources.\"))}Ea(){return!!Ra(this,0)}ka(){return!!Ra(this,1)}P(){return!!Ra(this,2)}Ca(){return Fa(this)}Ba(){var t=Ra(this,1);return t||(Ia(this),Oa(this),t=Ua(this),Ca(this),this.g.push(t),this.j=!0),t}M(){return Ia(this)}clone(){const t=[];for(const e of this.g){let n;if(e instanceof ImageData)n=new ImageData(e.data,this.width,this.height);else if(e instanceof WebGLTexture){const t=Ma(this),e=Pa(this);t.activeTexture(t.TEXTURE1),n=na(e,t),t.bindTexture(t.TEXTURE_2D,n),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,this.width,this.height,0,t.RGBA,t.UNSIGNED_BYTE,null),t.bindTexture(t.TEXTURE_2D,null),ra(e,t,n),ea(e,t,!1,(()=>{Oa(this),t.clearColor(0,0,0,0),t.clear(t.COLOR_BUFFER_BIT),t.drawArrays(t.TRIANGLE_FAN,0,4),Ca(this)})),ia(e),Ca(this)}else{if(!(e instanceof ImageBitmap))throw Error(`Type is not supported: ${e}`);Ia(this),Oa(this),n=Ua(this),Ca(this)}t.push(n)}return new Da(t,this.ka(),this.P(),this.canvas,this.l,this.width,this.height)}close(){this.j&&Ra(this,1).close(),this.m&&Ma(this).deleteTexture(Ra(this,2)),Na=-1}};Da.prototype.close=Da.prototype.close,Da.prototype.clone=Da.prototype.clone,Da.prototype.getAsWebGLTexture=Da.prototype.M,Da.prototype.getAsImageBitmap=Da.prototype.Ba,Da.prototype.getAsImageData=Da.prototype.Ca,Da.prototype.hasWebGLTexture=Da.prototype.P,Da.prototype.hasImageBitmap=Da.prototype.ka,Da.prototype.hasImageData=Da.prototype.Ea;var Na=250;function Ba(...t){return t.map((([t,e])=>({start:t,end:e})))}const Ga=function(t){return class extends t{La(){this.i._registerModelResourcesGraphService()}}}((ja=class{constructor(t,e){this.l=!0,this.i=t,this.g=null,this.h=0,this.m=\"function\"==typeof this.i._addIntToInputStream,void 0!==e?this.i.canvas=e:Oo()?this.i.canvas=new OffscreenCanvas(1,1):(console.warn(\"OffscreenCanvas not supported and GraphRunner constructor glCanvas parameter is undefined. Creating backup canvas.\"),this.i.canvas=document.createElement(\"canvas\"))}async initializeGraph(t){const e=await(await fetch(t)).arrayBuffer();t=!(t.endsWith(\".pbtxt\")||t.endsWith(\".textproto\")),this.setGraph(new Uint8Array(e),t)}setGraphFromString(t){this.setGraph((new TextEncoder).encode(t),!1)}setGraph(t,e){const n=t.length,r=this.i._malloc(n);this.i.HEAPU8.set(t,r),e?this.i._changeBinaryGraph(n,r):this.i._changeTextGraph(n,r),this.i._free(r)}configureAudio(t,e,n,r,i){this.i._configureAudio||console.warn('Attempting to use configureAudio without support for input audio. Is build dep \":gl_graph_runner_audio\" missing?'),Do(this,r||\"input_audio\",(r=>{Do(this,i=i||\"audio_header\",(i=>{this.i._configureAudio(r,i,t,e??0,n)}))}))}setAutoResizeCanvas(t){this.l=t}setAutoRenderToScreen(t){this.i._setAutoRenderToScreen(t)}setGpuBufferVerticalFlip(t){this.i.gpuOriginForWebTexturesIsBottomLeft=t}da(t){Go(this,\"__graph_config__\",(e=>{t(e)})),Do(this,\"__graph_config__\",(t=>{this.i._getGraphConfig(t,void 0)})),delete this.i.simpleListeners.__graph_config__}attachErrorListener(t){this.i.errorListener=t}attachEmptyPacketListener(t,e){this.i.emptyPacketListeners=this.i.emptyPacketListeners||{},this.i.emptyPacketListeners[t]=e}addAudioToStream(t,e,n){this.addAudioToStreamWithShape(t,0,0,e,n)}addAudioToStreamWithShape(t,e,n,r,i){const s=4*t.length;this.h!==s&&(this.g&&this.i._free(this.g),this.g=this.i._malloc(s),this.h=s),this.i.HEAPF32.set(t,this.g/4),Do(this,r,(t=>{this.i._addAudioToInputStream(this.g,e,n,t,i)}))}addGpuBufferToStream(t,e,n){Do(this,e,(e=>{const[r,i]=No(this,t,e);this.i._addBoundTextureToStream(e,r,i,n)}))}addBoolToStream(t,e,n){Do(this,e,(e=>{this.i._addBoolToInputStream(t,e,n)}))}addDoubleToStream(t,e,n){Do(this,e,(e=>{this.i._addDoubleToInputStream(t,e,n)}))}addFloatToStream(t,e,n){Do(this,e,(e=>{this.i._addFloatToInputStream(t,e,n)}))}addIntToStream(t,e,n){Do(this,e,(e=>{this.i._addIntToInputStream(t,e,n)}))}addUintToStream(t,e,n){Do(this,e,(e=>{this.i._addUintToInputStream(t,e,n)}))}addStringToStream(t,e,n){Do(this,e,(e=>{Do(this,t,(t=>{this.i._addStringToInputStream(t,e,n)}))}))}addStringRecordToStream(t,e,n){Do(this,e,(e=>{Bo(this,Object.keys(t),(r=>{Bo(this,Object.values(t),(i=>{this.i._addFlatHashMapToInputStream(r,i,Object.keys(t).length,e,n)}))}))}))}addProtoToStream(t,e,n,r){Do(this,n,(n=>{Do(this,e,(e=>{const i=this.i._malloc(t.length);this.i.HEAPU8.set(t,i),this.i._addProtoToInputStream(i,t.length,e,n,r),this.i._free(i)}))}))}addEmptyPacketToStream(t,e){Do(this,t,(t=>{this.i._addEmptyPacketToInputStream(t,e)}))}addBoolVectorToStream(t,e,n){Do(this,e,(e=>{const r=this.i._allocateBoolVector(t.length);if(!r)throw Error(\"Unable to allocate new bool vector on heap.\");for(const e of t)this.i._addBoolVectorEntry(r,e);this.i._addBoolVectorToInputStream(r,e,n)}))}addDoubleVectorToStream(t,e,n){Do(this,e,(e=>{const r=this.i._allocateDoubleVector(t.length);if(!r)throw Error(\"Unable to allocate new double vector on heap.\");for(const e of t)this.i._addDoubleVectorEntry(r,e);this.i._addDoubleVectorToInputStream(r,e,n)}))}addFloatVectorToStream(t,e,n){Do(this,e,(e=>{const r=this.i._allocateFloatVector(t.length);if(!r)throw Error(\"Unable to allocate new float vector on heap.\");for(const e of t)this.i._addFloatVectorEntry(r,e);this.i._addFloatVectorToInputStream(r,e,n)}))}addIntVectorToStream(t,e,n){Do(this,e,(e=>{const r=this.i._allocateIntVector(t.length);if(!r)throw Error(\"Unable to allocate new int vector on heap.\");for(const e of t)this.i._addIntVectorEntry(r,e);this.i._addIntVectorToInputStream(r,e,n)}))}addUintVectorToStream(t,e,n){Do(this,e,(e=>{const r=this.i._allocateUintVector(t.length);if(!r)throw Error(\"Unable to allocate new unsigned int vector on heap.\");for(const e of t)this.i._addUintVectorEntry(r,e);this.i._addUintVectorToInputStream(r,e,n)}))}addStringVectorToStream(t,e,n){Do(this,e,(e=>{const r=this.i._allocateStringVector(t.length);if(!r)throw Error(\"Unable to allocate new string vector on heap.\");for(const e of t)Do(this,e,(t=>{this.i._addStringVectorEntry(r,t)}));this.i._addStringVectorToInputStream(r,e,n)}))}addBoolToInputSidePacket(t,e){Do(this,e,(e=>{this.i._addBoolToInputSidePacket(t,e)}))}addDoubleToInputSidePacket(t,e){Do(this,e,(e=>{this.i._addDoubleToInputSidePacket(t,e)}))}addFloatToInputSidePacket(t,e){Do(this,e,(e=>{this.i._addFloatToInputSidePacket(t,e)}))}addIntToInputSidePacket(t,e){Do(this,e,(e=>{this.i._addIntToInputSidePacket(t,e)}))}addUintToInputSidePacket(t,e){Do(this,e,(e=>{this.i._addUintToInputSidePacket(t,e)}))}addStringToInputSidePacket(t,e){Do(this,e,(e=>{Do(this,t,(t=>{this.i._addStringToInputSidePacket(t,e)}))}))}addProtoToInputSidePacket(t,e,n){Do(this,n,(n=>{Do(this,e,(e=>{const r=this.i._malloc(t.length);this.i.HEAPU8.set(t,r),this.i._addProtoToInputSidePacket(r,t.length,e,n),this.i._free(r)}))}))}addBoolVectorToInputSidePacket(t,e){Do(this,e,(e=>{const n=this.i._allocateBoolVector(t.length);if(!n)throw Error(\"Unable to allocate new bool vector on heap.\");for(const e of t)this.i._addBoolVectorEntry(n,e);this.i._addBoolVectorToInputSidePacket(n,e)}))}addDoubleVectorToInputSidePacket(t,e){Do(this,e,(e=>{const n=this.i._allocateDoubleVector(t.length);if(!n)throw Error(\"Unable to allocate new double vector on heap.\");for(const e of t)this.i._addDoubleVectorEntry(n,e);this.i._addDoubleVectorToInputSidePacket(n,e)}))}addFloatVectorToInputSidePacket(t,e){Do(this,e,(e=>{const n=this.i._allocateFloatVector(t.length);if(!n)throw Error(\"Unable to allocate new float vector on heap.\");for(const e of t)this.i._addFloatVectorEntry(n,e);this.i._addFloatVectorToInputSidePacket(n,e)}))}addIntVectorToInputSidePacket(t,e){Do(this,e,(e=>{const n=this.i._allocateIntVector(t.length);if(!n)throw Error(\"Unable to allocate new int vector on heap.\");for(const e of t)this.i._addIntVectorEntry(n,e);this.i._addIntVectorToInputSidePacket(n,e)}))}addUintVectorToInputSidePacket(t,e){Do(this,e,(e=>{const n=this.i._allocateUintVector(t.length);if(!n)throw Error(\"Unable to allocate new unsigned int vector on heap.\");for(const e of t)this.i._addUintVectorEntry(n,e);this.i._addUintVectorToInputSidePacket(n,e)}))}addStringVectorToInputSidePacket(t,e){Do(this,e,(e=>{const n=this.i._allocateStringVector(t.length);if(!n)throw Error(\"Unable to allocate new string vector on heap.\");for(const e of t)Do(this,e,(t=>{this.i._addStringVectorEntry(n,t)}));this.i._addStringVectorToInputSidePacket(n,e)}))}attachBoolListener(t,e){Go(this,t,e),Do(this,t,(t=>{this.i._attachBoolListener(t)}))}attachBoolVectorListener(t,e){jo(this,t,e),Do(this,t,(t=>{this.i._attachBoolVectorListener(t)}))}attachIntListener(t,e){Go(this,t,e),Do(this,t,(t=>{this.i._attachIntListener(t)}))}attachIntVectorListener(t,e){jo(this,t,e),Do(this,t,(t=>{this.i._attachIntVectorListener(t)}))}attachUintListener(t,e){Go(this,t,e),Do(this,t,(t=>{this.i._attachUintListener(t)}))}attachUintVectorListener(t,e){jo(this,t,e),Do(this,t,(t=>{this.i._attachUintVectorListener(t)}))}attachDoubleListener(t,e){Go(this,t,e),Do(this,t,(t=>{this.i._attachDoubleListener(t)}))}attachDoubleVectorListener(t,e){jo(this,t,e),Do(this,t,(t=>{this.i._attachDoubleVectorListener(t)}))}attachFloatListener(t,e){Go(this,t,e),Do(this,t,(t=>{this.i._attachFloatListener(t)}))}attachFloatVectorListener(t,e){jo(this,t,e),Do(this,t,(t=>{this.i._attachFloatVectorListener(t)}))}attachStringListener(t,e){Go(this,t,e),Do(this,t,(t=>{this.i._attachStringListener(t)}))}attachStringVectorListener(t,e){jo(this,t,e),Do(this,t,(t=>{this.i._attachStringVectorListener(t)}))}attachProtoListener(t,e,n){Go(this,t,e),Do(this,t,(t=>{this.i._attachProtoListener(t,n||!1)}))}attachProtoVectorListener(t,e,n){jo(this,t,e),Do(this,t,(t=>{this.i._attachProtoVectorListener(t,n||!1)}))}attachAudioListener(t,e,n){this.i._attachAudioListener||console.warn('Attempting to use attachAudioListener without support for output audio. Is build dep \":gl_graph_runner_audio_out\" missing?'),Go(this,t,((t,n)=>{t=new Float32Array(t.buffer,t.byteOffset,t.length/4),e(t,n)})),Do(this,t,(t=>{this.i._attachAudioListener(t,n||!1)}))}finishProcessing(){this.i._waitUntilIdle()}closeGraph(){this.i._closeGraph(),this.i.simpleListeners=void 0,this.i.emptyPacketListeners=void 0}},class extends ja{get ea(){return this.i}qa(t,e,n){Do(this,e,(e=>{const[r,i]=No(this,t,e);this.ea._addBoundTextureAsImageToStream(e,r,i,n)}))}U(t,e){Go(this,t,e),Do(this,t,(t=>{this.ea._attachImageListener(t)}))}ca(t,e){jo(this,t,e),Do(this,t,(t=>{this.ea._attachImageVectorListener(t)}))}}));var ja,Va=class extends Ga{};async function Xa(t,e,n){return async function(t,e,n,r){return Vo(t,e,n,r)}(t,n.canvas??(Oo()?void 0:document.createElement(\"canvas\")),e,n)}function Ha(t,e,n,r){if(t.T){const s=new fs;if(n?.regionOfInterest){if(!t.pa)throw Error(\"This task doesn't support region-of-interest.\");var i=n.regionOfInterest;if(i.left>=i.right||i.top>=i.bottom)throw Error(\"Expected RectF with left < right and top < bottom.\");if(i.left<0||i.top<0||i.right>1||i.bottom>1)throw Error(\"Expected RectF values to be in [0,1].\");gn(s,1,(i.left+i.right)/2),gn(s,2,(i.top+i.bottom)/2),gn(s,4,i.right-i.left),gn(s,3,i.bottom-i.top)}else gn(s,1,.5),gn(s,2,.5),gn(s,4,1),gn(s,3,1);if(n?.rotationDegrees){if(n?.rotationDegrees%90!=0)throw Error(\"Expected rotation to be a multiple of 90°.\");if(gn(s,5,-Math.PI*n.rotationDegrees/180),n?.rotationDegrees%180!=0){const[t,r]=Uo(e);n=dn(s,3)*r/t,i=dn(s,4)*t/r,gn(s,4,n),gn(s,3,i)}}t.g.addProtoToStream(s.g(),\"mediapipe.NormalizedRect\",t.T,r)}t.g.qa(e,t.aa,r??performance.now()),t.finishProcessing()}function Wa(t,e,n){if(t.baseOptions?.g())throw Error(\"Task is not initialized with image mode. 'runningMode' must be set to 'IMAGE'.\");Ha(t,e,n,t.B+1)}function za(t,e,n,r){if(!t.baseOptions?.g())throw Error(\"Task is not initialized with video mode. 'runningMode' must be set to 'VIDEO'.\");Ha(t,e,n,r)}function Ka(t,e,n,r){var i=e.data;const s=e.width,o=s*(e=e.height);if((i instanceof Uint8Array||i instanceof Float32Array)&&i.length!==o)throw Error(\"Unsupported channel count: \"+i.length/o);return t=new ya([i],n,!1,t.g.i.canvas,t.O,s,e),r?t.clone():t}var Ya=class extends $o{constructor(t,e,n,r){super(t),this.g=t,this.aa=e,this.T=n,this.pa=r,this.O=new sa}l(t,e=!0){if(\"runningMode\"in t&&fn(this.baseOptions,2,!!t.runningMode&&\"IMAGE\"!==t.runningMode),void 0!==t.canvas&&this.g.i.canvas!==t.canvas)throw Error(\"You must create a new task to reset the canvas.\");return super.l(t,e)}close(){this.O.close(),super.close()}};Ya.prototype.close=Ya.prototype.close;var $a=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect_in\",!1),this.j={detections:[]},sn(t=this.h=new Is,0,1,e=new Ls),gn(this.h,2,.5),gn(this.h,3,.3)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return\"minDetectionConfidence\"in t&&gn(this.h,2,t.minDetectionConfidence??.5),\"minSuppressionThreshold\"in t&&gn(this.h,3,t.minSuppressionThreshold??.3),this.l(t)}D(t,e){return this.j={detections:[]},Wa(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},za(this,t,n,e),this.j}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect_in\"),$i(t,\"detections\");const e=new Di;Xn(e,Ps,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.face_detector.FaceDetectorGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect_in\"),Vi(n,\"DETECTIONS:detections\"),n.o(e),Ki(t,n),this.g.attachProtoVectorListener(\"detections\",((t,e)=>{for(const e of t)t=os(e),this.j.detections.push(bo(t));Wo(this,e)})),this.g.attachEmptyPacketListener(\"detections\",(t=>{Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};$a.prototype.detectForVideo=$a.prototype.F,$a.prototype.detect=$a.prototype.D,$a.prototype.setOptions=$a.prototype.o,$a.createFromModelPath=async function(t,e){return Xa($a,t,{baseOptions:{modelAssetPath:e}})},$a.createFromModelBuffer=function(t,e){return Xa($a,t,{baseOptions:{modelAssetBuffer:e}})},$a.createFromOptions=function(t,e){return Xa($a,t,e)};var qa=Ba([61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]),Ja=Ba([263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]),Za=Ba([276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]),Qa=Ba([474,475],[475,476],[476,477],[477,474]),tc=Ba([33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]),ec=Ba([46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]),nc=Ba([469,470],[470,471],[471,472],[472,469]),rc=Ba([10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]),ic=[...qa,...Ja,...Za,...tc,...ec,...rc],sc=Ba([127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]);function oc(t){t.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]}}var ac=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect\",!1),this.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]},this.outputFacialTransformationMatrixes=this.outputFaceBlendshapes=!1,sn(t=this.h=new Us,0,1,e=new Ls),this.v=new Cs,sn(this.h,0,3,this.v),this.s=new Is,sn(this.h,0,2,this.s),pn(this.s,4,1),gn(this.s,2,.5),gn(this.v,2,.5),gn(this.h,4,.5)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return\"numFaces\"in t&&pn(this.s,4,t.numFaces??1),\"minFaceDetectionConfidence\"in t&&gn(this.s,2,t.minFaceDetectionConfidence??.5),\"minTrackingConfidence\"in t&&gn(this.h,4,t.minTrackingConfidence??.5),\"minFacePresenceConfidence\"in t&&gn(this.v,2,t.minFacePresenceConfidence??.5),\"outputFaceBlendshapes\"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),\"outputFacialTransformationMatrixes\"in t&&(this.outputFacialTransformationMatrixes=!!t.outputFacialTransformationMatrixes),this.l(t)}D(t,e){return oc(this),Wa(this,t,e),this.j}F(t,e,n){return oc(this),za(this,t,n,e),this.j}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect\"),$i(t,\"face_landmarks\");const e=new Di;Xn(e,Bs,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.face_landmarker.FaceLandmarkerGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"NORM_LANDMARKS:face_landmarks\"),n.o(e),Ki(t,n),this.g.attachProtoVectorListener(\"face_landmarks\",((t,e)=>{for(const e of t)t=us(e),this.j.faceLandmarks.push(ko(t));Wo(this,e)})),this.g.attachEmptyPacketListener(\"face_landmarks\",(t=>{Wo(this,t)})),this.outputFaceBlendshapes&&($i(t,\"blendshapes\"),Vi(n,\"BLENDSHAPES:blendshapes\"),this.g.attachProtoVectorListener(\"blendshapes\",((t,e)=>{if(this.outputFaceBlendshapes)for(const e of t)t=ns(e),this.j.faceBlendshapes.push(Ao(t.g()??[]));Wo(this,e)})),this.g.attachEmptyPacketListener(\"blendshapes\",(t=>{Wo(this,t)}))),this.outputFacialTransformationMatrixes&&($i(t,\"face_geometry\"),Vi(n,\"FACE_GEOMETRY:face_geometry\"),this.g.attachProtoVectorListener(\"face_geometry\",((t,e)=>{if(this.outputFacialTransformationMatrixes)for(const e of t)(t=en(Os(e),ls,2))&&this.j.facialTransformationMatrixes.push({rows:un(t,1)??0??0,columns:un(t,2)??0??0,data:Ve(t,3,$t,je()).slice()??[]});Wo(this,e)})),this.g.attachEmptyPacketListener(\"face_geometry\",(t=>{Wo(this,t)}))),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};ac.prototype.detectForVideo=ac.prototype.F,ac.prototype.detect=ac.prototype.D,ac.prototype.setOptions=ac.prototype.o,ac.createFromModelPath=function(t,e){return Xa(ac,t,{baseOptions:{modelAssetPath:e}})},ac.createFromModelBuffer=function(t,e){return Xa(ac,t,{baseOptions:{modelAssetBuffer:e}})},ac.createFromOptions=function(t,e){return Xa(ac,t,e)},ac.FACE_LANDMARKS_LIPS=qa,ac.FACE_LANDMARKS_LEFT_EYE=Ja,ac.FACE_LANDMARKS_LEFT_EYEBROW=Za,ac.FACE_LANDMARKS_LEFT_IRIS=Qa,ac.FACE_LANDMARKS_RIGHT_EYE=tc,ac.FACE_LANDMARKS_RIGHT_EYEBROW=ec,ac.FACE_LANDMARKS_RIGHT_IRIS=nc,ac.FACE_LANDMARKS_FACE_OVAL=rc,ac.FACE_LANDMARKS_CONTOURS=ic,ac.FACE_LANDMARKS_TESSELATION=sc;var cc=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect\",!0),sn(t=this.j=new Gs,0,1,e=new Ls)}get baseOptions(){return en(this.j,Ls,1)}set baseOptions(t){sn(this.j,0,1,t)}o(t){return super.l(t)}Oa(t,e,n){const r=\"function\"!=typeof e?e:{};if(this.h=\"function\"==typeof e?e:n,Wa(this,t,r??{}),!this.h)return this.s}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect\"),$i(t,\"stylized_image\");const e=new Di;Xn(e,js,this.j);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.face_stylizer.FaceStylizerGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"STYLIZED_IMAGE:stylized_image\"),n.o(e),Ki(t,n),this.g.U(\"stylized_image\",((t,e)=>{var n=!this.h,r=t.data,i=t.width;const s=i*(t=t.height);if(r instanceof Uint8Array)if(r.length===3*s){const e=new Uint8ClampedArray(4*s);for(let t=0;t<s;++t)e[4*t]=r[3*t],e[4*t+1]=r[3*t+1],e[4*t+2]=r[3*t+2],e[4*t+3]=255;r=new ImageData(e,i,t)}else{if(r.length!==4*s)throw Error(\"Unsupported channel count: \"+r.length/s);r=new ImageData(new Uint8ClampedArray(r.buffer,r.byteOffset,r.length),i,t)}else if(!(r instanceof WebGLTexture))throw Error(`Unsupported format: ${r.constructor.name}`);i=new Da([r],!1,!1,this.g.i.canvas,this.O,i,t),this.s=n=n?i.clone():i,this.h&&this.h(n),Wo(this,e)})),this.g.attachEmptyPacketListener(\"stylized_image\",(t=>{this.s=null,this.h&&this.h(null),Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};cc.prototype.stylize=cc.prototype.Oa,cc.prototype.setOptions=cc.prototype.o,cc.createFromModelPath=function(t,e){return Xa(cc,t,{baseOptions:{modelAssetPath:e}})},cc.createFromModelBuffer=function(t,e){return Xa(cc,t,{baseOptions:{modelAssetBuffer:e}})},cc.createFromOptions=function(t,e){return Xa(cc,t,e)};var hc=Ba([0,1],[1,2],[2,3],[3,4],[0,5],[5,6],[6,7],[7,8],[5,9],[9,10],[10,11],[11,12],[9,13],[13,14],[14,15],[15,16],[13,17],[0,17],[17,18],[18,19],[19,20]);function uc(t){t.gestures=[],t.landmarks=[],t.worldLandmarks=[],t.handedness=[]}function lc(t){return 0===t.gestures.length?{gestures:[],landmarks:[],worldLandmarks:[],handedness:[],handednesses:[]}:{gestures:t.gestures,landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handedness:t.handedness,handednesses:t.handedness}}function dc(t,e=!0){const n=[];for(const i of t){var r=ns(i);t=[];for(const n of r.g())r=e&&null!=un(n,1)?un(n,1)??0:-1,t.push({score:dn(n,2)??0,index:r,categoryName:ln(n,3)??\"\"??\"\",displayName:ln(n,4)??\"\"??\"\"});n.push(t)}return n}var fc=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect\",!1),this.gestures=[],this.landmarks=[],this.worldLandmarks=[],this.handedness=[],sn(t=this.j=new Ys,0,1,e=new Ls),this.s=new Ks,sn(this.j,0,2,this.s),this.C=new zs,sn(this.s,0,3,this.C),this.v=new Ws,sn(this.s,0,2,this.v),this.h=new Hs,sn(this.j,0,3,this.h),gn(this.v,2,.5),gn(this.s,4,.5),gn(this.C,2,.5)}get baseOptions(){return en(this.j,Ls,1)}set baseOptions(t){sn(this.j,0,1,t)}o(t){if(pn(this.v,3,t.numHands??1),\"minHandDetectionConfidence\"in t&&gn(this.v,2,t.minHandDetectionConfidence??.5),\"minTrackingConfidence\"in t&&gn(this.s,4,t.minTrackingConfidence??.5),\"minHandPresenceConfidence\"in t&&gn(this.C,2,t.minHandPresenceConfidence??.5),t.cannedGesturesClassifierOptions){var e=new Vs,n=e,r=To(t.cannedGesturesClassifierOptions,en(this.h,Vs,3)?.h());sn(n,0,2,r),sn(this.h,0,3,e)}else void 0===t.cannedGesturesClassifierOptions&&en(this.h,Vs,3)?.g();return t.customGesturesClassifierOptions?(sn(n=e=new Vs,0,2,r=To(t.customGesturesClassifierOptions,en(this.h,Vs,4)?.h())),sn(this.h,0,4,e)):void 0===t.customGesturesClassifierOptions&&en(this.h,Vs,4)?.g(),this.l(t)}Ja(t,e){return uc(this),Wa(this,t,e),lc(this)}Ka(t,e,n){return uc(this),za(this,t,n,e),lc(this)}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect\"),$i(t,\"hand_gestures\"),$i(t,\"hand_landmarks\"),$i(t,\"world_hand_landmarks\"),$i(t,\"handedness\");const e=new Di;Xn(e,Qs,this.j);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.gesture_recognizer.GestureRecognizerGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"HAND_GESTURES:hand_gestures\"),Vi(n,\"LANDMARKS:hand_landmarks\"),Vi(n,\"WORLD_LANDMARKS:world_hand_landmarks\"),Vi(n,\"HANDEDNESS:handedness\"),n.o(e),Ki(t,n),this.g.attachProtoVectorListener(\"hand_landmarks\",((t,e)=>{for(const e of t){t=us(e);const n=[];for(const e of rn(t,hs,1))n.push({x:dn(e,1)??0,y:dn(e,2)??0,z:dn(e,3)??0,visibility:dn(e,4)??0});this.landmarks.push(n)}Wo(this,e)})),this.g.attachEmptyPacketListener(\"hand_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoVectorListener(\"world_hand_landmarks\",((t,e)=>{for(const e of t){t=cs(e);const n=[];for(const e of rn(t,as,1))n.push({x:dn(e,1)??0,y:dn(e,2)??0,z:dn(e,3)??0,visibility:dn(e,4)??0});this.worldLandmarks.push(n)}Wo(this,e)})),this.g.attachEmptyPacketListener(\"world_hand_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoVectorListener(\"hand_gestures\",((t,e)=>{this.gestures.push(...dc(t,!1)),Wo(this,e)})),this.g.attachEmptyPacketListener(\"hand_gestures\",(t=>{Wo(this,t)})),this.g.attachProtoVectorListener(\"handedness\",((t,e)=>{this.handedness.push(...dc(t)),Wo(this,e)})),this.g.attachEmptyPacketListener(\"handedness\",(t=>{Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};function pc(t){return{landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handednesses:t.handedness,handedness:t.handedness}}fc.prototype.recognizeForVideo=fc.prototype.Ka,fc.prototype.recognize=fc.prototype.Ja,fc.prototype.setOptions=fc.prototype.o,fc.createFromModelPath=function(t,e){return Xa(fc,t,{baseOptions:{modelAssetPath:e}})},fc.createFromModelBuffer=function(t,e){return Xa(fc,t,{baseOptions:{modelAssetBuffer:e}})},fc.createFromOptions=function(t,e){return Xa(fc,t,e)},fc.HAND_CONNECTIONS=hc;var gc=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect\",!1),this.landmarks=[],this.worldLandmarks=[],this.handedness=[],sn(t=this.h=new Ks,0,1,e=new Ls),this.s=new zs,sn(this.h,0,3,this.s),this.j=new Ws,sn(this.h,0,2,this.j),pn(this.j,3,1),gn(this.j,2,.5),gn(this.s,2,.5),gn(this.h,4,.5)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return\"numHands\"in t&&pn(this.j,3,t.numHands??1),\"minHandDetectionConfidence\"in t&&gn(this.j,2,t.minHandDetectionConfidence??.5),\"minTrackingConfidence\"in t&&gn(this.h,4,t.minTrackingConfidence??.5),\"minHandPresenceConfidence\"in t&&gn(this.s,2,t.minHandPresenceConfidence??.5),this.l(t)}D(t,e){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],Wa(this,t,e),pc(this)}F(t,e,n){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],za(this,t,n,e),pc(this)}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect\"),$i(t,\"hand_landmarks\"),$i(t,\"world_hand_landmarks\"),$i(t,\"handedness\");const e=new Di;Xn(e,to,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.hand_landmarker.HandLandmarkerGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"LANDMARKS:hand_landmarks\"),Vi(n,\"WORLD_LANDMARKS:world_hand_landmarks\"),Vi(n,\"HANDEDNESS:handedness\"),n.o(e),Ki(t,n),this.g.attachProtoVectorListener(\"hand_landmarks\",((t,e)=>{for(const e of t)t=us(e),this.landmarks.push(ko(t));Wo(this,e)})),this.g.attachEmptyPacketListener(\"hand_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoVectorListener(\"world_hand_landmarks\",((t,e)=>{for(const e of t)t=cs(e),this.worldLandmarks.push(So(t));Wo(this,e)})),this.g.attachEmptyPacketListener(\"world_hand_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoVectorListener(\"handedness\",((t,e)=>{var n=this.handedness,r=n.push;const i=[];for(const e of t){t=ns(e);const n=[];for(const e of t.g())n.push({score:dn(e,2)??0,index:un(e,1)??0??-1,categoryName:ln(e,3)??\"\"??\"\",displayName:ln(e,4)??\"\"??\"\"});i.push(n)}r.call(n,...i),Wo(this,e)})),this.g.attachEmptyPacketListener(\"handedness\",(t=>{Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};gc.prototype.detectForVideo=gc.prototype.F,gc.prototype.detect=gc.prototype.D,gc.prototype.setOptions=gc.prototype.o,gc.createFromModelPath=function(t,e){return Xa(gc,t,{baseOptions:{modelAssetPath:e}})},gc.createFromModelBuffer=function(t,e){return Xa(gc,t,{baseOptions:{modelAssetBuffer:e}})},gc.createFromOptions=function(t,e){return Xa(gc,t,e)},gc.HAND_CONNECTIONS=hc;var mc=Ba([0,1],[1,2],[2,3],[3,7],[0,4],[4,5],[5,6],[6,8],[9,10],[11,12],[11,13],[13,15],[15,17],[15,19],[15,21],[17,19],[12,14],[14,16],[16,18],[16,20],[16,22],[18,20],[11,23],[12,24],[23,24],[23,25],[24,26],[25,27],[26,28],[27,29],[28,30],[29,31],[30,32],[27,31],[28,32]);function yc(t){t.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]}}function _c(t){try{if(!t.C)return t.h;t.C(t.h)}finally{Yo(t)}}function vc(t,e){t=us(t),e.push(ko(t))}var Ec=class extends Ya{constructor(t,e){super(new Va(t,e),\"input_frames_image\",null,!1),this.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]},this.outputPoseSegmentationMasks=this.outputFaceBlendshapes=!1,sn(t=this.j=new io,0,1,e=new Ls),this.J=new zs,sn(this.j,0,2,this.J),this.Z=new eo,sn(this.j,0,3,this.Z),this.s=new Is,sn(this.j,0,4,this.s),this.H=new Cs,sn(this.j,0,5,this.H),this.v=new no,sn(this.j,0,6,this.v),this.K=new ro,sn(this.j,0,7,this.K),gn(this.s,2,.5),gn(this.s,3,.3),gn(this.H,2,.5),gn(this.v,2,.5),gn(this.v,3,.3),gn(this.K,2,.5),gn(this.J,2,.5)}get baseOptions(){return en(this.j,Ls,1)}set baseOptions(t){sn(this.j,0,1,t)}o(t){return\"minFaceDetectionConfidence\"in t&&gn(this.s,2,t.minFaceDetectionConfidence??.5),\"minFaceSuppressionThreshold\"in t&&gn(this.s,3,t.minFaceSuppressionThreshold??.3),\"minFacePresenceConfidence\"in t&&gn(this.H,2,t.minFacePresenceConfidence??.5),\"outputFaceBlendshapes\"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),\"minPoseDetectionConfidence\"in t&&gn(this.v,2,t.minPoseDetectionConfidence??.5),\"minPoseSuppressionThreshold\"in t&&gn(this.v,3,t.minPoseSuppressionThreshold??.3),\"minPosePresenceConfidence\"in t&&gn(this.K,2,t.minPosePresenceConfidence??.5),\"outputPoseSegmentationMasks\"in t&&(this.outputPoseSegmentationMasks=!!t.outputPoseSegmentationMasks),\"minHandLandmarksConfidence\"in t&&gn(this.J,2,t.minHandLandmarksConfidence??.5),this.l(t)}D(t,e,n){const r=\"function\"!=typeof e?e:{};return this.C=\"function\"==typeof e?e:n,yc(this),Wa(this,t,r),_c(this)}F(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.C=\"function\"==typeof n?n:r,yc(this),za(this,t,i,e),_c(this)}m(){var t=new qi;Yi(t,\"input_frames_image\"),$i(t,\"pose_landmarks\"),$i(t,\"pose_world_landmarks\"),$i(t,\"face_landmarks\"),$i(t,\"left_hand_landmarks\"),$i(t,\"left_hand_world_landmarks\"),$i(t,\"right_hand_landmarks\"),$i(t,\"right_hand_world_landmarks\");const e=new Di,n=new bi;Ke(n,1,ae(\"type.googleapis.com/mediapipe.tasks.vision.holistic_landmarker.proto.HolisticLandmarkerGraphOptions\"),\"\"),function(t,e){if(null!=e)if(Array.isArray(e))De(t,2,Re(e));else{if(!(\"string\"==typeof e||e instanceof N||O(e)))throw Error(\"invalid value in Any.value field: \"+e+\" expected a ByteString, a base64 encoded string, a Uint8Array or a jspb array\");Ke(t,2,ft(e,!1),U())}}(n,this.j.g());const r=new Xi;Gi(r,\"mediapipe.tasks.vision.holistic_landmarker.HolisticLandmarkerGraph\"),hn(r,8,bi,n),ji(r,\"IMAGE:input_frames_image\"),Vi(r,\"POSE_LANDMARKS:pose_landmarks\"),Vi(r,\"POSE_WORLD_LANDMARKS:pose_world_landmarks\"),Vi(r,\"FACE_LANDMARKS:face_landmarks\"),Vi(r,\"LEFT_HAND_LANDMARKS:left_hand_landmarks\"),Vi(r,\"LEFT_HAND_WORLD_LANDMARKS:left_hand_world_landmarks\"),Vi(r,\"RIGHT_HAND_LANDMARKS:right_hand_landmarks\"),Vi(r,\"RIGHT_HAND_WORLD_LANDMARKS:right_hand_world_landmarks\"),r.o(e),Ki(t,r),zo(this,t),this.g.attachProtoListener(\"pose_landmarks\",((t,e)=>{vc(t,this.h.poseLandmarks),Wo(this,e)})),this.g.attachEmptyPacketListener(\"pose_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoListener(\"pose_world_landmarks\",((t,e)=>{var n=this.h.poseWorldLandmarks;t=cs(t),n.push(So(t)),Wo(this,e)})),this.g.attachEmptyPacketListener(\"pose_world_landmarks\",(t=>{Wo(this,t)})),this.outputPoseSegmentationMasks&&(Vi(r,\"POSE_SEGMENTATION_MASK:pose_segmentation_mask\"),Ko(this,\"pose_segmentation_mask\"),this.g.U(\"pose_segmentation_mask\",((t,e)=>{this.h.poseSegmentationMasks=[Ka(this,t,!0,!this.C)],Wo(this,e)})),this.g.attachEmptyPacketListener(\"pose_segmentation_mask\",(t=>{this.h.poseSegmentationMasks=[],Wo(this,t)}))),this.g.attachProtoListener(\"face_landmarks\",((t,e)=>{vc(t,this.h.faceLandmarks),Wo(this,e)})),this.g.attachEmptyPacketListener(\"face_landmarks\",(t=>{Wo(this,t)})),this.outputFaceBlendshapes&&($i(t,\"extra_blendshapes\"),Vi(r,\"FACE_BLENDSHAPES:extra_blendshapes\"),this.g.attachProtoListener(\"extra_blendshapes\",((t,e)=>{var n=this.h.faceBlendshapes;this.outputFaceBlendshapes&&(t=ns(t),n.push(Ao(t.g()??[]))),Wo(this,e)})),this.g.attachEmptyPacketListener(\"extra_blendshapes\",(t=>{Wo(this,t)}))),this.g.attachProtoListener(\"left_hand_landmarks\",((t,e)=>{vc(t,this.h.leftHandLandmarks),Wo(this,e)})),this.g.attachEmptyPacketListener(\"left_hand_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoListener(\"left_hand_world_landmarks\",((t,e)=>{var n=this.h.leftHandWorldLandmarks;t=cs(t),n.push(So(t)),Wo(this,e)})),this.g.attachEmptyPacketListener(\"left_hand_world_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoListener(\"right_hand_landmarks\",((t,e)=>{vc(t,this.h.rightHandLandmarks),Wo(this,e)})),this.g.attachEmptyPacketListener(\"right_hand_landmarks\",(t=>{Wo(this,t)})),this.g.attachProtoListener(\"right_hand_world_landmarks\",((t,e)=>{var n=this.h.rightHandWorldLandmarks;t=cs(t),n.push(So(t)),Wo(this,e)})),this.g.attachEmptyPacketListener(\"right_hand_world_landmarks\",(t=>{Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Ec.prototype.detectForVideo=Ec.prototype.F,Ec.prototype.detect=Ec.prototype.D,Ec.prototype.setOptions=Ec.prototype.o,Ec.createFromModelPath=function(t,e){return Xa(Ec,t,{baseOptions:{modelAssetPath:e}})},Ec.createFromModelBuffer=function(t,e){return Xa(Ec,t,{baseOptions:{modelAssetBuffer:e}})},Ec.createFromOptions=function(t,e){return Xa(Ec,t,e)},Ec.HAND_CONNECTIONS=hc,Ec.POSE_CONNECTIONS=mc,Ec.FACE_LANDMARKS_LIPS=qa,Ec.FACE_LANDMARKS_LEFT_EYE=Ja,Ec.FACE_LANDMARKS_LEFT_EYEBROW=Za,Ec.FACE_LANDMARKS_LEFT_IRIS=Qa,Ec.FACE_LANDMARKS_RIGHT_EYE=tc,Ec.FACE_LANDMARKS_RIGHT_EYEBROW=ec,Ec.FACE_LANDMARKS_RIGHT_IRIS=nc,Ec.FACE_LANDMARKS_FACE_OVAL=rc,Ec.FACE_LANDMARKS_CONTOURS=ic,Ec.FACE_LANDMARKS_TESSELATION=sc;var wc=class extends Ya{constructor(t,e){super(new Va(t,e),\"input_image\",\"norm_rect\",!0),this.j={classifications:[]},sn(t=this.h=new ao,0,1,e=new Ls)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return sn(this.h,0,2,To(t,en(this.h,ws,2))),this.l(t)}sa(t,e){return this.j={classifications:[]},Wa(this,t,e),this.j}ta(t,e,n){return this.j={classifications:[]},za(this,t,n,e),this.j}m(){var t=new qi;Yi(t,\"input_image\"),Yi(t,\"norm_rect\"),$i(t,\"classifications\");const e=new Di;Xn(e,co,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.image_classifier.ImageClassifierGraph\"),ji(n,\"IMAGE:input_image\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"CLASSIFICATIONS:classifications\"),n.o(e),Ki(t,n),this.g.attachProtoListener(\"classifications\",((t,e)=>{this.j=function(t){const e={classifications:rn(t,ps,1).map((t=>Ao(en(t,ts,4)?.g()??[],un(t,2)??0,ln(t,3)??\"\")))};return null!=ie(Ce(t,2))&&(e.timestampMs=ie(Ce(t,2))??0),e}(gs(t)),Wo(this,e)})),this.g.attachEmptyPacketListener(\"classifications\",(t=>{Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};wc.prototype.classifyForVideo=wc.prototype.ta,wc.prototype.classify=wc.prototype.sa,wc.prototype.setOptions=wc.prototype.o,wc.createFromModelPath=function(t,e){return Xa(wc,t,{baseOptions:{modelAssetPath:e}})},wc.createFromModelBuffer=function(t,e){return Xa(wc,t,{baseOptions:{modelAssetBuffer:e}})},wc.createFromOptions=function(t,e){return Xa(wc,t,e)};var Tc=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect\",!0),this.h=new ho,this.embeddings={embeddings:[]},sn(t=this.h,0,1,e=new Ls)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){var e=this.h,n=en(this.h,As,2);return n=n?n.clone():new As,void 0!==t.l2Normalize?fn(n,1,t.l2Normalize):\"l2Normalize\"in t&&De(n,1),void 0!==t.quantize?fn(n,2,t.quantize):\"quantize\"in t&&De(n,2),sn(e,0,2,n),this.l(t)}za(t,e){return Wa(this,t,e),this.embeddings}Aa(t,e,n){return za(this,t,n,e),this.embeddings}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect\"),$i(t,\"embeddings_out\");const e=new Di;Xn(e,uo,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.image_embedder.ImageEmbedderGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"EMBEDDINGS:embeddings_out\"),n.o(e),Ki(t,n),this.g.attachProtoListener(\"embeddings_out\",((t,e)=>{t=Es(t),this.embeddings=function(t){return{embeddings:rn(t,_s,1).map((t=>{const e={headIndex:un(t,3)??0??-1,headName:ln(t,4)??\"\"??\"\"};if(void 0!==tn(t,ms,$e(t,1)))t=Ve(t=en(t,ms,$e(t,1)),1,$t,je()),e.floatEmbedding=t.slice();else{const n=new Uint8Array(0);e.quantizedEmbedding=en(t,ys,$e(t,2))?.oa()?.h()??n}return e})),timestampMs:ie(Ce(t,2))??0}}(t),Wo(this,e)})),this.g.attachEmptyPacketListener(\"embeddings_out\",(t=>{Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Tc.cosineSimilarity=function(t,e){if(t.floatEmbedding&&e.floatEmbedding)t=Lo(t.floatEmbedding,e.floatEmbedding);else{if(!t.quantizedEmbedding||!e.quantizedEmbedding)throw Error(\"Cannot compute cosine similarity between quantized and float embeddings.\");t=Lo(xo(t.quantizedEmbedding),xo(e.quantizedEmbedding))}return t},Tc.prototype.embedForVideo=Tc.prototype.Aa,Tc.prototype.embed=Tc.prototype.za,Tc.prototype.setOptions=Tc.prototype.o,Tc.createFromModelPath=function(t,e){return Xa(Tc,t,{baseOptions:{modelAssetPath:e}})},Tc.createFromModelBuffer=function(t,e){return Xa(Tc,t,{baseOptions:{modelAssetBuffer:e}})},Tc.createFromOptions=function(t,e){return Xa(Tc,t,e)};var Ac=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){this.confidenceMasks?.forEach((t=>{t.close()})),this.categoryMask?.close()}};function bc(t){t.categoryMask=void 0,t.confidenceMasks=void 0,t.qualityScores=void 0}function kc(t){try{const e=new Ac(t.confidenceMasks,t.categoryMask,t.qualityScores);if(!t.j)return e;t.j(e)}finally{Yo(t)}}Ac.prototype.close=Ac.prototype.close;var Sc=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect\",!1),this.s=[],this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new mo,this.v=new lo,sn(this.h,0,3,this.v),sn(t=this.h,0,1,e=new Ls)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return void 0!==t.displayNamesLocale?De(this.h,2,ae(t.displayNamesLocale)):\"displayNamesLocale\"in t&&De(this.h,2),\"outputCategoryMask\"in t&&(this.outputCategoryMask=t.outputCategoryMask??!1),\"outputConfidenceMasks\"in t&&(this.outputConfidenceMasks=t.outputConfidenceMasks??!0),super.l(t)}I(){!function(t){const e=rn(t.da(),Xi,1).filter((t=>(ln(t,1)??\"\").includes(\"mediapipe.tasks.TensorsToSegmentationCalculator\")));if(t.s=[],e.length>1)throw Error(\"The graph has more than one mediapipe.tasks.TensorsToSegmentationCalculator.\");1===e.length&&(en(e[0],Di,7)?.l()?.g()??new Map).forEach(((e,n)=>{t.s[Number(n)]=ln(e,1)??\"\"}))}(this)}segment(t,e,n){const r=\"function\"!=typeof e?e:{};return this.j=\"function\"==typeof e?e:n,bc(this),Wa(this,t,r),kc(this)}Ma(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.j=\"function\"==typeof n?n:r,bc(this),za(this,t,i,e),kc(this)}Da(){return this.s}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect\");const e=new Di;Xn(e,yo,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.image_segmenter.ImageSegmenterGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect\"),n.o(e),Ki(t,n),zo(this,t),this.outputConfidenceMasks&&($i(t,\"confidence_masks\"),Vi(n,\"CONFIDENCE_MASKS:confidence_masks\"),Ko(this,\"confidence_masks\"),this.g.ca(\"confidence_masks\",((t,e)=>{this.confidenceMasks=t.map((t=>Ka(this,t,!0,!this.j))),Wo(this,e)})),this.g.attachEmptyPacketListener(\"confidence_masks\",(t=>{this.confidenceMasks=[],Wo(this,t)}))),this.outputCategoryMask&&($i(t,\"category_mask\"),Vi(n,\"CATEGORY_MASK:category_mask\"),Ko(this,\"category_mask\"),this.g.U(\"category_mask\",((t,e)=>{this.categoryMask=Ka(this,t,!1,!this.j),Wo(this,e)})),this.g.attachEmptyPacketListener(\"category_mask\",(t=>{this.categoryMask=void 0,Wo(this,t)}))),$i(t,\"quality_scores\"),Vi(n,\"QUALITY_SCORES:quality_scores\"),this.g.attachFloatVectorListener(\"quality_scores\",((t,e)=>{this.qualityScores=t,Wo(this,e)})),this.g.attachEmptyPacketListener(\"quality_scores\",(t=>{this.categoryMask=void 0,Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Sc.prototype.getLabels=Sc.prototype.Da,Sc.prototype.segmentForVideo=Sc.prototype.Ma,Sc.prototype.segment=Sc.prototype.segment,Sc.prototype.setOptions=Sc.prototype.o,Sc.createFromModelPath=function(t,e){return Xa(Sc,t,{baseOptions:{modelAssetPath:e}})},Sc.createFromModelBuffer=function(t,e){return Xa(Sc,t,{baseOptions:{modelAssetBuffer:e}})},Sc.createFromOptions=function(t,e){return Xa(Sc,t,e)};var xc=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){this.confidenceMasks?.forEach((t=>{t.close()})),this.categoryMask?.close()}};xc.prototype.close=xc.prototype.close;var Lc=class extends Hn{constructor(t){super(t)}},Rc=[0,oi,-2],Fc=[0,Qr,-3,hi,Qr,-1],Ic=[0,Fc],Mc=[0,Fc,oi,-1],Pc=class extends Hn{constructor(t){super(t)}},Oc=[0,Qr,-1,hi],Cc=class extends Hn{constructor(t){super(t)}},Uc=class extends Hn{constructor(t){super(t)}},Dc=[1,2,3,4,5,6,7,8,9,10,14,15],Nc=class extends Hn{constructor(t){super(t)}};Nc.prototype.g=Ai([0,pi,[0,Dc,gi,Fc,gi,[0,Fc,Rc],gi,Ic,gi,[0,Ic,Rc],gi,Oc,gi,[0,Qr,-3,hi,vi],gi,[0,Qr,-3,hi],gi,[0,fi,Qr,-2,hi,oi,hi,-1,2,Qr,Rc],gi,Mc,gi,[0,Mc,Rc],Qr,Rc,fi,gi,[0,Qr,-3,hi,Rc,-1],gi,[0,pi,Oc]],fi,[0,fi,oi,-1,hi]]);var Bc=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect_in\",!1),this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new mo,this.s=new lo,sn(this.h,0,3,this.s),sn(t=this.h,0,1,e=new Ls)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return\"outputCategoryMask\"in t&&(this.outputCategoryMask=t.outputCategoryMask??!1),\"outputConfidenceMasks\"in t&&(this.outputConfidenceMasks=t.outputConfidenceMasks??!0),super.l(t)}segment(t,e,n,r){const i=\"function\"!=typeof n?n:{};this.j=\"function\"==typeof n?n:r,this.qualityScores=this.categoryMask=this.confidenceMasks=void 0,n=this.B+1,r=new Nc;const s=new Uc;var o=new Lc;if(pn(o,1,255),sn(s,0,12,o),e.keypoint&&e.scribble)throw Error(\"Cannot provide both keypoint and scribble.\");if(e.keypoint){var a=new Pc;fn(a,3,!0),gn(a,1,e.keypoint.x),gn(a,2,e.keypoint.y),on(s,5,Dc,a)}else{if(!e.scribble)throw Error(\"Must provide either a keypoint or a scribble.\");for(a of(o=new Cc,e.scribble))fn(e=new Pc,3,!0),gn(e,1,a.x),gn(e,2,a.y),hn(o,1,Pc,e);on(s,15,Dc,o)}hn(r,1,Uc,s),this.g.addProtoToStream(r.g(),\"drishti.RenderData\",\"roi_in\",n),Wa(this,t,i);t:{try{const t=new xc(this.confidenceMasks,this.categoryMask,this.qualityScores);if(!this.j){var c=t;break t}this.j(t)}finally{Yo(this)}c=void 0}return c}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"roi_in\"),Yi(t,\"norm_rect_in\");const e=new Di;Xn(e,yo,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.interactive_segmenter.InteractiveSegmenterGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"ROI:roi_in\"),ji(n,\"NORM_RECT:norm_rect_in\"),n.o(e),Ki(t,n),zo(this,t),this.outputConfidenceMasks&&($i(t,\"confidence_masks\"),Vi(n,\"CONFIDENCE_MASKS:confidence_masks\"),Ko(this,\"confidence_masks\"),this.g.ca(\"confidence_masks\",((t,e)=>{this.confidenceMasks=t.map((t=>Ka(this,t,!0,!this.j))),Wo(this,e)})),this.g.attachEmptyPacketListener(\"confidence_masks\",(t=>{this.confidenceMasks=[],Wo(this,t)}))),this.outputCategoryMask&&($i(t,\"category_mask\"),Vi(n,\"CATEGORY_MASK:category_mask\"),Ko(this,\"category_mask\"),this.g.U(\"category_mask\",((t,e)=>{this.categoryMask=Ka(this,t,!1,!this.j),Wo(this,e)})),this.g.attachEmptyPacketListener(\"category_mask\",(t=>{this.categoryMask=void 0,Wo(this,t)}))),$i(t,\"quality_scores\"),Vi(n,\"QUALITY_SCORES:quality_scores\"),this.g.attachFloatVectorListener(\"quality_scores\",((t,e)=>{this.qualityScores=t,Wo(this,e)})),this.g.attachEmptyPacketListener(\"quality_scores\",(t=>{this.categoryMask=void 0,Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Bc.prototype.segment=Bc.prototype.segment,Bc.prototype.setOptions=Bc.prototype.o,Bc.createFromModelPath=function(t,e){return Xa(Bc,t,{baseOptions:{modelAssetPath:e}})},Bc.createFromModelBuffer=function(t,e){return Xa(Bc,t,{baseOptions:{modelAssetBuffer:e}})},Bc.createFromOptions=function(t,e){return Xa(Bc,t,e)};var Gc=class extends Ya{constructor(t,e){super(new Va(t,e),\"input_frame_gpu\",\"norm_rect\",!1),this.j={detections:[]},sn(t=this.h=new _o,0,1,e=new Ls)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return void 0!==t.displayNamesLocale?De(this.h,2,ae(t.displayNamesLocale)):\"displayNamesLocale\"in t&&De(this.h,2),void 0!==t.maxResults?pn(this.h,3,t.maxResults):\"maxResults\"in t&&De(this.h,3),void 0!==t.scoreThreshold?gn(this.h,4,t.scoreThreshold):\"scoreThreshold\"in t&&De(this.h,4),void 0!==t.categoryAllowlist?mn(this.h,5,t.categoryAllowlist):\"categoryAllowlist\"in t&&De(this.h,5),void 0!==t.categoryDenylist?mn(this.h,6,t.categoryDenylist):\"categoryDenylist\"in t&&De(this.h,6),this.l(t)}D(t,e){return this.j={detections:[]},Wa(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},za(this,t,n,e),this.j}m(){var t=new qi;Yi(t,\"input_frame_gpu\"),Yi(t,\"norm_rect\"),$i(t,\"detections\");const e=new Di;Xn(e,vo,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.ObjectDetectorGraph\"),ji(n,\"IMAGE:input_frame_gpu\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"DETECTIONS:detections\"),n.o(e),Ki(t,n),this.g.attachProtoVectorListener(\"detections\",((t,e)=>{for(const e of t)t=os(e),this.j.detections.push(bo(t));Wo(this,e)})),this.g.attachEmptyPacketListener(\"detections\",(t=>{Wo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Gc.prototype.detectForVideo=Gc.prototype.F,Gc.prototype.detect=Gc.prototype.D,Gc.prototype.setOptions=Gc.prototype.o,Gc.createFromModelPath=async function(t,e){return Xa(Gc,t,{baseOptions:{modelAssetPath:e}})},Gc.createFromModelBuffer=function(t,e){return Xa(Gc,t,{baseOptions:{modelAssetBuffer:e}})},Gc.createFromOptions=function(t,e){return Xa(Gc,t,e)};var jc=class{constructor(t,e,n){this.landmarks=t,this.worldLandmarks=e,this.segmentationMasks=n}close(){this.segmentationMasks?.forEach((t=>{t.close()}))}};function Vc(t){t.landmarks=[],t.worldLandmarks=[],t.segmentationMasks=void 0}function Xc(t){try{const e=new jc(t.landmarks,t.worldLandmarks,t.segmentationMasks);if(!t.s)return e;t.s(e)}finally{Yo(t)}}jc.prototype.close=jc.prototype.close;var Hc=class extends Ya{constructor(t,e){super(new Va(t,e),\"image_in\",\"norm_rect\",!1),this.landmarks=[],this.worldLandmarks=[],this.outputSegmentationMasks=!1,sn(t=this.h=new Eo,0,1,e=new Ls),this.v=new ro,sn(this.h,0,3,this.v),this.j=new no,sn(this.h,0,2,this.j),pn(this.j,4,1),gn(this.j,2,.5),gn(this.v,2,.5),gn(this.h,4,.5)}get baseOptions(){return en(this.h,Ls,1)}set baseOptions(t){sn(this.h,0,1,t)}o(t){return\"numPoses\"in t&&pn(this.j,4,t.numPoses??1),\"minPoseDetectionConfidence\"in t&&gn(this.j,2,t.minPoseDetectionConfidence??.5),\"minTrackingConfidence\"in t&&gn(this.h,4,t.minTrackingConfidence??.5),\"minPosePresenceConfidence\"in t&&gn(this.v,2,t.minPosePresenceConfidence??.5),\"outputSegmentationMasks\"in t&&(this.outputSegmentationMasks=t.outputSegmentationMasks??!1),this.l(t)}D(t,e,n){const r=\"function\"!=typeof e?e:{};return this.s=\"function\"==typeof e?e:n,Vc(this),Wa(this,t,r),Xc(this)}F(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.s=\"function\"==typeof n?n:r,Vc(this),za(this,t,i,e),Xc(this)}m(){var t=new qi;Yi(t,\"image_in\"),Yi(t,\"norm_rect\"),$i(t,\"normalized_landmarks\"),$i(t,\"world_landmarks\"),$i(t,\"segmentation_masks\");const e=new Di;Xn(e,wo,this.h);const n=new Xi;Gi(n,\"mediapipe.tasks.vision.pose_landmarker.PoseLandmarkerGraph\"),ji(n,\"IMAGE:image_in\"),ji(n,\"NORM_RECT:norm_rect\"),Vi(n,\"NORM_LANDMARKS:normalized_landmarks\"),Vi(n,\"WORLD_LANDMARKS:world_landmarks\"),n.o(e),Ki(t,n),zo(this,t),this.g.attachProtoVectorListener(\"normalized_landmarks\",((t,e)=>{this.landmarks=[];for(const e of t)t=us(e),this.landmarks.push(ko(t));Wo(this,e)})),this.g.attachEmptyPacketListener(\"normalized_landmarks\",(t=>{this.landmarks=[],Wo(this,t)})),this.g.attachProtoVectorListener(\"world_landmarks\",((t,e)=>{this.worldLandmarks=[];for(const e of t)t=cs(e),this.worldLandmarks.push(So(t));Wo(this,e)})),this.g.attachEmptyPacketListener(\"world_landmarks\",(t=>{this.worldLandmarks=[],Wo(this,t)})),this.outputSegmentationMasks&&(Vi(n,\"SEGMENTATION_MASK:segmentation_masks\"),Ko(this,\"segmentation_masks\"),this.g.ca(\"segmentation_masks\",((t,e)=>{this.segmentationMasks=t.map((t=>Ka(this,t,!0,!this.s))),Wo(this,e)})),this.g.attachEmptyPacketListener(\"segmentation_masks\",(t=>{this.segmentationMasks=[],Wo(this,t)}))),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Hc.prototype.detectForVideo=Hc.prototype.F,Hc.prototype.detect=Hc.prototype.D,Hc.prototype.setOptions=Hc.prototype.o,Hc.createFromModelPath=function(t,e){return Xa(Hc,t,{baseOptions:{modelAssetPath:e}})},Hc.createFromModelBuffer=function(t,e){return Xa(Hc,t,{baseOptions:{modelAssetBuffer:e}})},Hc.createFromOptions=function(t,e){return Xa(Hc,t,e)},Hc.POSE_CONNECTIONS=mc;export{La as DrawingUtils,$a as FaceDetector,ac as FaceLandmarker,cc as FaceStylizer,Po as FilesetResolver,fc as GestureRecognizer,gc as HandLandmarker,Ec as HolisticLandmarker,wc as ImageClassifier,Tc as ImageEmbedder,Sc as ImageSegmenter,Ac as ImageSegmenterResult,Bc as InteractiveSegmenter,xc as InteractiveSegmenterResult,Da as MPImage,ya as MPMask,Gc as ObjectDetector,Hc as PoseLandmarker,Ya as VisionTaskRunner};\n//# sourceMappingURL=vision_bundle_mjs.js.map\n"], "mappings": ";;;AAAA,IAAI,IAAE,eAAa,OAAO,OAAK,OAAK,CAAC;AAAE,SAAS,IAAG;AAAC,QAAM,MAAM,cAAc;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOA,KAAE,OAAO,aAAa,MAAM,MAAKA,EAAC,GAAE,QAAMD,KAAEC,KAAED,KAAEC;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAE,IAAM,IAAE,eAAa,OAAO;AAAY,IAAI;AAAE,IAAM,IAAE,eAAa,OAAO;AAAY,SAAS,EAAED,IAAE;AAAC,MAAG;AAAE,IAAAA,MAAG,UAAI,IAAI,gBAAa,OAAOA,EAAC;AAAA,OAAM;AAAC,QAAIE,KAAE;AAAE,UAAMC,KAAE,IAAI,WAAW,IAAEH,GAAE,MAAM;AAAE,aAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAI;AAAC,UAAIH,KAAED,GAAE,WAAWI,EAAC;AAAE,UAAGH,KAAE;AAAI,QAAAE,GAAED,IAAG,IAAED;AAAA,WAAM;AAAC,YAAGA,KAAE;AAAK,UAAAE,GAAED,IAAG,IAAED,MAAG,IAAE;AAAA,aAAQ;AAAC,cAAGA,MAAG,SAAOA,MAAG,OAAM;AAAC,gBAAGA,MAAG,SAAOG,KAAEJ,GAAE,QAAO;AAAC,oBAAMK,KAAEL,GAAE,WAAW,EAAEI,EAAC;AAAE,kBAAGC,MAAG,SAAOA,MAAG,OAAM;AAAC,gBAAAJ,KAAE,QAAMA,KAAE,SAAOI,KAAE,QAAM,OAAMF,GAAED,IAAG,IAAED,MAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,KAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,IAAE,KAAG,KAAIE,GAAED,IAAG,IAAE,KAAGD,KAAE;AAAI;AAAA,cAAQ;AAAC,cAAAG;AAAA,YAAG;AAAC,YAAAH,KAAE;AAAA,UAAK;AAAC,UAAAE,GAAED,IAAG,IAAED,MAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,IAAE,KAAG;AAAA,QAAG;AAAC,QAAAE,GAAED,IAAG,IAAE,KAAGD,KAAE;AAAA,MAAG;AAAA,IAAC;AAAC,IAAAD,KAAEE,OAAIC,GAAE,SAAOA,KAAEA,GAAE,SAAS,GAAED,EAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAE,GAAE;AAAC,OAAQ,IAAE,CAAC,eAAe,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO;AAAI,QAAG,SAAO,IAAE,EAAE,EAAE,CAAC,CAAC,IAAG;AAAC,UAAE;AAAK,YAAM;AAAA,IAAC;AAAC,MAAE;AAAC;AAAnF;AAAoB;AAAI;AAA4D,IAAI;AAAJ,IAAM,IAAE,KAAG,EAAE,SAAS;AAAE,IAAE,QAAM,KAAG;AAAE,IAAM,IAAE,EAAE;AAAU,SAAS,EAAEA,IAAE;AAAC,SAAM,CAAC,CAAC,MAAI,CAAC,CAAC,KAAG,EAAE,OAAO,KAAM,CAAC,EAAC,OAAMC,GAAC,MAAIA,MAAG,MAAIA,GAAE,QAAQD,EAAC,CAAE;AAAE;AAAC,SAAS,EAAEC,IAAE;AAAC,MAAIC;AAAE,UAAOA,KAAE,EAAE,eAAaA,KAAEA,GAAE,eAAaA,KAAE,KAAI,MAAIA,GAAE,QAAQD,EAAC;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,CAAC,CAAC,MAAI,CAAC,CAAC,KAAG,EAAE,OAAO,SAAO;AAAE;AAAC,SAAS,IAAG;AAAC,SAAO,EAAE,IAAE,EAAE,UAAU,KAAG,EAAE,QAAQ,KAAG,EAAE,OAAO,MAAI,EAAE,CAAC,EAAE,KAAG,EAAE,MAAM,MAAI,EAAE,MAAM;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAE,GAAG,EAAEA,EAAC,GAAEA;AAAC;AAAC,IAAE,KAAG,EAAE,iBAAe,MAAK,EAAE,GAAG,IAAE,WAAU;AAAC;AAAE,IAAI,IAAE,CAAC,EAAE,MAAI,EAAE,SAAS,KAAG,EAAE,MAAM;AAAG,CAAC,EAAE,SAAS,KAAG,EAAE,GAAE,EAAE,GAAE,EAAE,QAAQ,MAAI,EAAE,KAAG,CAAC,EAAE,KAAG,EAAE,OAAO,KAAG,CAAC,EAAE,KAAG,EAAE,OAAO,KAAG,CAAC,EAAE,KAAG,EAAE,MAAM,MAAI,EAAE,IAAE,EAAE,gBAAgB,IAAE,EAAE,MAAM,MAAI,EAAE,KAAG,EAAE,OAAO;AAAG,IAAI,IAAE,CAAC;AAAP,IAAS,IAAE;AAAK,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAED,GAAE;AAAO,MAAIE,KAAE,IAAED,KAAE;AAAE,EAAAC,KAAE,IAAEA,KAAE,KAAK,MAAMA,EAAC,IAAE,MAAI,KAAK,QAAQF,GAAEC,KAAE,CAAC,CAAC,MAAIC,KAAE,MAAI,KAAK,QAAQF,GAAEC,KAAE,CAAC,CAAC,IAAEC,KAAE,IAAEA,KAAE;AAAG,QAAMC,KAAE,IAAI,WAAWD,EAAC;AAAE,MAAIE,KAAE;AAAE,SAAO,SAASJ,IAAEC,IAAE;AAAC,aAASC,GAAED,IAAE;AAAC,aAAKE,KAAEH,GAAE,UAAQ;AAAC,cAAMC,KAAED,GAAE,OAAOG,IAAG,GAAED,KAAE,EAAED,EAAC;AAAE,YAAG,QAAMC;AAAE,iBAAOA;AAAE,YAAG,CAAC,cAAc,KAAKD,EAAC;AAAE,gBAAM,MAAM,sCAAoCA,EAAC;AAAA,MAAC;AAAC,aAAOA;AAAA,IAAC;AAAC,MAAE;AAAE,QAAIE,KAAE;AAAE,eAAO;AAAC,YAAMH,KAAEE,GAAE,EAAE,GAAEC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,EAAE,GAAEG,KAAEH,GAAE,EAAE;AAAE,UAAG,OAAKG,MAAG,OAAKL;AAAE;AAAM,MAAAC,GAAED,MAAG,IAAEG,MAAG,CAAC,GAAE,MAAIC,OAAIH,GAAEE,MAAG,IAAE,MAAIC,MAAG,CAAC,GAAE,MAAIC,MAAGJ,GAAEG,MAAG,IAAE,MAAIC,EAAC;AAAA,IAAE;AAAA,EAAC,EAAEL,IAAG,SAASA,IAAE;AAAC,IAAAG,GAAEC,IAAG,IAAEJ;AAAA,EAAC,CAAE,GAAEI,OAAIF,KAAEC,GAAE,SAAS,GAAEC,EAAC,IAAED;AAAC;AAAC,SAAS,IAAG;AAAC,MAAG,CAAC,GAAE;AAAC,QAAE,CAAC;AAAE,QAAIH,KAAE,iEAAiE,MAAM,EAAE,GAAEC,KAAE,CAAC,OAAM,MAAK,OAAM,OAAM,IAAI;AAAE,aAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,KAAEH,GAAE,OAAOC,GAAEC,EAAC,EAAE,MAAM,EAAE,CAAC;AAAE,QAAEA,EAAC,IAAEC;AAAE,eAAQH,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAI;AAAC,cAAMC,KAAEE,GAAEH,EAAC;AAAE,mBAAS,EAAEC,EAAC,MAAI,EAAEA,EAAC,IAAED;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAI,IAAE,eAAa,OAAO;AAA1B,IAAqC,IAAE,CAAC,KAAG,cAAY,OAAO;AAAK,SAAS,EAAEA,IAAE;AAAC,MAAG,CAAC,GAAE;AAAC,QAAIC;AAAE,eAASA,OAAIA,KAAE,IAAG,EAAE,GAAEA,KAAE,EAAEA,EAAC;AAAE,QAAIC,KAAE,MAAM,KAAK,MAAMF,GAAE,SAAO,CAAC,CAAC,GAAEG,KAAEF,GAAE,EAAE,KAAG;AAAG,QAAIK,KAAE,GAAEC,KAAE;AAAE,WAAKD,KAAEN,GAAE,SAAO,GAAEM,MAAG,GAAE;AAAC,UAAIF,KAAEJ,GAAEM,EAAC,GAAED,KAAEL,GAAEM,KAAE,CAAC,GAAEE,KAAER,GAAEM,KAAE,CAAC,GAAEG,KAAER,GAAEG,MAAG,CAAC;AAAE,MAAAA,KAAEH,IAAG,IAAEG,OAAI,IAAEC,MAAG,CAAC,GAAEA,KAAEJ,IAAG,KAAGI,OAAI,IAAEG,MAAG,CAAC,GAAEA,KAAEP,GAAE,KAAGO,EAAC,GAAEN,GAAEK,IAAG,IAAEE,KAAEL,KAAEC,KAAEG;AAAA,IAAC;AAAC,YAAOC,KAAE,GAAED,KAAEL,IAAEH,GAAE,SAAOM,IAAE;AAAA,MAAC,KAAK;AAAE,QAAAE,KAAEP,IAAG,MAAIQ,KAAET,GAAEM,KAAE,CAAC,OAAK,CAAC,KAAGH;AAAA,MAAE,KAAK;AAAE,QAAAH,KAAEA,GAAEM,EAAC,GAAEJ,GAAEK,EAAC,IAAEN,GAAED,MAAG,CAAC,IAAEC,IAAG,IAAED,OAAI,IAAES,MAAG,CAAC,IAAED,KAAEL;AAAA,IAAC;AAAC,WAAOD,GAAE,KAAK,EAAE;AAAA,EAAC;AAAC,OAAID,KAAE,IAAGC,KAAE,GAAEC,KAAEH,GAAE,SAAO,OAAME,KAAEC;AAAG,IAAAF,MAAG,OAAO,aAAa,MAAM,MAAKD,GAAE,SAASE,IAAEA,MAAG,KAAK,CAAC;AAAE,SAAOD,MAAG,OAAO,aAAa,MAAM,MAAKC,KAAEF,GAAE,SAASE,EAAC,IAAEF,EAAC,GAAE,KAAKC,EAAC;AAAC;AAAC,IAAM,IAAE;AAAR,IAAiB,IAAE,EAAC,KAAI,KAAI,GAAE,KAAI,KAAI,IAAG;AAAE,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG;AAAE;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAG,CAAC;AAAE,WAAO,EAAEA,EAAC;AAAE,IAAE,KAAKA,EAAC,MAAIA,KAAEA,GAAE,QAAQ,GAAE,CAAC,IAAGA,KAAE,KAAKA,EAAC;AAAE,QAAMC,KAAE,IAAI,WAAWD,GAAE,MAAM;AAAE,WAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,IAAAD,GAAEC,EAAC,IAAEF,GAAE,WAAWE,EAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,KAAG,QAAMA,MAAGA,cAAa;AAAU;AAAC,IAAI,IAAE,CAAC;AAAE,SAAS,IAAG;AAAC,SAAO,UAAI,IAAI,EAAE,MAAK,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,IAAE,CAAC;AAAE,MAAIC,KAAED,GAAE;AAAE,SAAO,SAAOC,KAAE,QAAMA,MAAG,EAAEA,EAAC,IAAEA,KAAE,YAAU,OAAOA,KAAE,EAAEA,EAAC,IAAE,QAAMA,KAAED,GAAE,IAAEC;AAAC;AAAC,IAAI,IAAE,MAAK;AAAA,EAAC,IAAG;AAAC,WAAO,IAAI,WAAW,EAAE,IAAI,KAAG,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,QAAG,EAAEA,EAAC,GAAE,KAAK,IAAED,IAAE,QAAMA,MAAG,MAAIA,GAAE;AAAO,YAAM,MAAM,wDAAwD;AAAA,EAAC;AAAC;AAAE,IAAI;AAAJ,IAAM;AAAE,SAAS,EAAEA,IAAE;AAAC,MAAGA,OAAI;AAAE,UAAM,MAAM,yBAAyB;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,EAAAD,GAAE,sCAAoCA,GAAE,oCAAkC,CAAC,IAAGA,GAAE,kCAAkC,WAASC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,KAAE,MAAMA,EAAC,GAAE,SAAS,GAAEA;AAAC;AAAC,IAAI,IAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO;AAAzD,IAA2D,IAAE,oBAAI;AAAI,SAAS,EAAEA,IAAEC,IAAEC,KAAE,OAAGC,KAAE,OAAG;AAAC,SAAOH,KAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,IAAEG,MAAG,OAAO,OAAKH,KAAE,OAAO,IAAIA,EAAC,IAAE,QAAMA,KAAE,OAAOA,EAAC,IAAE,OAAO,IAAEC,IAAEC,MAAG,EAAE,IAAIF,EAAC,GAAEA;AAAC;AAAC,IAAI,IAAE,EAAE,OAAM,QAAO,MAAG,IAAE;AAA1B,IAA4B,IAAE,EAAE,QAAO,KAAK;AAA5C,IAA8C,IAAE,EAAE,QAAO,KAAK;AAA9D,IAAgE,IAAE,EAAE,QAAO,OAAM,IAAE;AAAnF,IAAqF,IAAE,EAAE,QAAO,OAAO,GAAE,IAAE;AAAE,IAAM,IAAE,IAAE,IAAE;AAAZ,IAAiB,IAAE,EAAC,IAAG,EAAC,OAAM,GAAE,cAAa,MAAG,UAAS,MAAG,YAAW,MAAE,EAAC;AAA1E,IAA4E,KAAG,OAAO;AAAiB,SAAS,GAAGA,IAAEC,IAAE;AAAC,OAAG,KAAKD,MAAG,GAAGA,IAAE,CAAC,GAAEA,GAAE,CAAC,KAAGC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,OAAG,KAAKD,MAAG,GAAGA,IAAE,CAAC,GAAEA,GAAE,CAAC,IAAEC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,GAAGA,IAAE,EAAE,GAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGA,IAAE,UAAQ,IAAED,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGA,IAAE,UAAQ,KAAGD,GAAE;AAAC;AAAC,SAAS,KAAI;AAAC,SAAM,cAAY,OAAO;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,MAAM,UAAU,MAAM,KAAKA,EAAC;AAAC;AAAC,IAAI;AAAJ,IAAO,KAAG,CAAC;AAAX,IAAa,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAE,CAACA,MAAG,YAAU,OAAOA,MAAGA,GAAE,OAAK;AAAG;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,SAAOA,MAAG,YAAU,OAAOA,MAAG,CAAC,MAAM,QAAQA,EAAC,KAAGA,GAAE,gBAAc;AAAM;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAG,QAAMD;AAAE,QAAG,YAAU,OAAOA;AAAE,MAAAA,KAAEA,KAAE,IAAI,EAAEA,IAAE,CAAC,IAAE,EAAE;AAAA,aAAUA,GAAE,gBAAc;AAAE,UAAG,EAAEA,EAAC;AAAE,QAAAA,KAAEA,GAAE,SAAO,IAAI,EAAE,IAAI,WAAWA,EAAC,GAAE,CAAC,IAAE,EAAE;AAAA,WAAM;AAAC,YAAG,CAACC;AAAE,gBAAM,MAAM;AAAE,QAAAD,KAAE;AAAA,MAAM;AAAA;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAE,CAAC,MAAM,QAAQA,EAAC,KAAGA,GAAE,WAAS,CAAC,EAAE,KAAG,IAAEA,GAAE,CAAC;AAAG;AAAC,IAAM,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,MAAG,IAAEA;AAAE,UAAM,MAAM;AAAC;AAAC,GAAG,IAAG,EAAE,GAAE,KAAG,OAAO,OAAO,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAE,GAAE,KAAK,IAAEF,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAG,KAAK,IAAE,KAAK,EAAE,QAAO;AAAC,YAAMF,KAAE,KAAK,EAAE,KAAK,GAAG;AAAE,aAAM,EAAC,MAAK,OAAG,OAAM,KAAK,IAAE,KAAK,EAAE,KAAK,KAAK,GAAEA,EAAC,IAAEA,GAAC;AAAA,IAAC;AAAC,WAAM,EAAC,MAAK,MAAG,OAAM,OAAM;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,IAAI,GAAG,KAAK,GAAE,KAAK,GAAE,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,IAAEA,GAAE,CAAC,IAAE;AAAM;AAAC,IAAI,KAAG,OAAO,OAAO,CAAC,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,KAAG,MAAGA;AAAC;AAAC,IAAI,KAAG,GAAI,CAAAA,OAAG,YAAU,OAAOA,EAAE;AAAjC,IAAmC,KAAG,GAAI,CAAAA,OAAG,YAAU,OAAOA,EAAE;AAAhE,IAAkE,KAAG,GAAI,CAAAA,OAAG,aAAW,OAAOA,EAAE;AAAhG,IAAkG,KAAG,cAAY,OAAO,EAAE,UAAQ,YAAU,OAAO,EAAE,OAAO,CAAC;AAA7J,IAA+J,KAAG,GAAI,CAAAA,OAAG,KAAGA,MAAG,MAAIA,MAAG,KAAG,QAAMA,GAAE,CAAC,IAAE,GAAGA,IAAE,EAAE,IAAE,GAAGA,IAAE,EAAE,CAAE;AAAE,IAAM,KAAG,OAAO,iBAAiB,SAAS;AAA1C,IAA4C,KAAG,KAAG,OAAO,OAAO,gBAAgB,IAAE;AAAlF,IAAyF,KAAG,OAAO,iBAAiB,SAAS;AAA7H,IAA+H,KAAG,KAAG,OAAO,OAAO,gBAAgB,IAAE;AAAO,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,GAAE,SAAOC,GAAE;AAAO,WAAM;AAAG,MAAGD,GAAE,SAAOC,GAAE,UAAQD,OAAIC;AAAE,WAAM;AAAG,WAAQC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,UAAMC,KAAEH,GAAEE,EAAC,GAAEE,KAAEH,GAAEC,EAAC;AAAE,QAAGC,KAAEC;AAAE,aAAM;AAAG,QAAGD,KAAEC;AAAE,aAAM;AAAA,EAAE;AAAC;AAAC,IAAM,KAAG,cAAY,OAAO,WAAW,UAAU;AAAM,IAAI;AAAJ,IAAO,KAAG;AAAV,IAAY,KAAG;AAAE,SAAS,GAAGJ,IAAE;AAAC,QAAMC,KAAED,OAAI;AAAE,OAAGC,IAAE,MAAID,KAAEC,MAAG,eAAa;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAGA,KAAE,GAAE;AAAC,OAAG,CAACA,EAAC;AAAE,UAAK,CAACC,IAAEC,EAAC,IAAE,GAAG,IAAG,EAAE;AAAE,SAAGD,OAAI,GAAE,KAAGC,OAAI;AAAA,EAAC;AAAM,OAAGF,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,YAAK,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;AAAE,EAAAA,GAAE,WAAW,GAAE,CAACD,IAAE,IAAE,GAAE,KAAG,GAAE,KAAGC,GAAE,UAAU,GAAE,IAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,QAAMC,KAAE,aAAWD,MAAGD,OAAI;AAAG,SAAO,OAAO,cAAcE,EAAC,IAAEA,KAAE,GAAGF,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,QAAMC,KAAE,aAAWD;AAAE,SAAOC,OAAID,KAAE,CAACA,OAAI,GAAE,MAAID,KAAE,IAAE,CAACA,OAAI,OAAKC,KAAEA,KAAE,MAAI,KAAI,YAAU,QAAOD,KAAE,GAAGA,IAAEC,EAAC,KAAGC,KAAE,CAACF,KAAEA,KAAEE,KAAE,MAAIF,KAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,QAAK,IAAGC,QAAK,MAAI;AAAQ,QAAIC,KAAE,MAAI,aAAWD,KAAED;AAAA;AAAQ,OAAG,IAAEE,KAAE,MAAI,OAAOD,EAAC,KAAG,OAAO,EAAE,IAAE,OAAOD,EAAC,MAAIA,MAAG,WAASA,MAAG,WAASE,KAAE,YAAUF,OAAI,KAAGC,MAAG,MAAI,WAASA,KAAEA,MAAG,KAAG,QAAOC,MAAG,UAAQD,IAAEA,MAAG,GAAED,MAAG,QAAME,MAAGF,KAAE,QAAM,GAAEA,MAAG,MAAKE,MAAG,QAAMD,MAAGC,KAAE,QAAM,GAAEA,MAAG,MAAKA,KAAED,KAAE,GAAGC,EAAC,IAAE,GAAGF,EAAC;AAAG,SAAOE;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,SAAOA,KAAE,OAAOA,EAAC,GAAE,UAAU,MAAMA,GAAE,MAAM,IAAEA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAGA,GAAE,SAAO;AAAG,OAAG,OAAOA,EAAC,CAAC;AAAA,WAAU,GAAG;AAAE,IAAAA,KAAE,OAAOA,EAAC,GAAE,KAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,MAAI,GAAE,KAAG,OAAOA,MAAG,OAAO,EAAE,IAAE,OAAO,UAAU,CAAC;AAAA,OAAM;AAAC,UAAMC,KAAE,EAAE,QAAMD,GAAE,CAAC;AAAG,SAAG,KAAG;AAAE,UAAME,KAAEF,GAAE;AAAO,aAAQG,KAAEF,IAAEG,MAAGF,KAAED,MAAG,IAAEA,IAAEG,MAAGF,IAAEC,KAAEC,IAAEA,MAAG,GAAE;AAAC,YAAMH,KAAE,OAAOD,GAAE,MAAMG,IAAEC,EAAC,CAAC;AAAE,YAAI,KAAI,KAAG,MAAI,KAAGH,IAAE,MAAI,eAAa,MAAI,KAAK,MAAM,KAAG,UAAU,GAAE,QAAM,GAAE,QAAM;AAAA,IAAE;AAAC,QAAGA,IAAE;AAAC,YAAK,CAACD,IAAEC,EAAC,IAAE,GAAG,IAAG,EAAE;AAAE,WAAGD,IAAE,KAAGC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAOA,KAAE,CAACA,IAAED,KAAEA,KAAE,IAAE,CAACA,KAAEC,MAAG,GAAE,CAACD,IAAEC,EAAC;AAAC;AAAC,IAAM,KAAG,cAAY,OAAO,SAAO,OAAO,SAAO;AAAjD,IAAwD,KAAG,cAAY,OAAO,SAAO,OAAO,UAAQ;AAApG,IAA2G,KAAG,OAAO;AAArH,IAAmI,KAAG,OAAO;AAA7I,IAAsJ,KAAG,KAAK;AAAM,SAAS,GAAGD,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,KAAEA,KAAE,UAAQA,MAAG,eAAaA,MAAG,gBAAcA,KAAE,OAAOA,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,MAAG,aAAW,OAAOA,KAAEA,KAAE,YAAU,OAAOA,KAAE,CAAC,CAACA,KAAE;AAAM;AAAC,IAAM,KAAG;AAAiC,SAAS,GAAGA,IAAE;AAAC,UAAO,OAAOA,IAAE;AAAA,IAAC,KAAI;AAAS,aAAM;AAAA,IAAG,KAAI;AAAS,aAAO,GAAGA,EAAC;AAAA,IAAE,KAAI;AAAS,aAAO,GAAG,KAAKA,EAAC;AAAA,IAAE;AAAQ,aAAM;AAAA,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA;AAAE,WAAOA;AAAE,MAAG,YAAU,OAAOA,MAAGA;AAAE,IAAAA,KAAE,CAACA;AAAA,WAAU,YAAU,OAAOA;AAAE;AAAO,SAAO,GAAGA,EAAC,IAAE,IAAEA,KAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA;AAAE,WAAOA;AAAE,MAAG,YAAU,OAAOA,MAAGA;AAAE,IAAAA,KAAE,CAACA;AAAA,WAAU,YAAU,OAAOA;AAAE;AAAO,SAAO,GAAGA,EAAC,IAAEA,OAAI,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,GAAE,CAAC;AAAE,WAAM;AAAG,QAAMC,KAAED,GAAE;AAAO,SAAOC,KAAE,MAAI,OAAKA,MAAG,OAAOD,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,KAAE,GAAGA,EAAC,GAAE,GAAGA,EAAC,MAAI,GAAGA,EAAC,GAAEA,KAAE,GAAG,IAAG,EAAE,IAAGA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAE,GAAG,OAAOD,EAAC,CAAC;AAAE,MAAG,GAAGC,EAAC;AAAE,WAAO,OAAOA,EAAC;AAAE,MAAG,QAAMA,KAAED,GAAE,QAAQ,GAAG,OAAKA,KAAEA,GAAE,UAAU,GAAEC,EAAC,IAAGA,KAAED,GAAE,QAAO,EAAE,QAAMA,GAAE,CAAC,IAAEC,KAAE,MAAI,OAAKA,MAAG,OAAOD,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE,UAAQC,KAAE,MAAI,OAAKA,MAAG,OAAOD,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE;AAAQ,QAAG,GAAGA,EAAC,GAAEA,KAAE,IAAG,cAAYC,KAAE;AAAI,UAAG,GAAG;AAAE,QAAAD,KAAE,MAAI,OAAO,IAAEC,EAAC,KAAG,OAAO,EAAE,IAAE,OAAOD,OAAI,CAAC;AAAA,WAAO;AAAC,cAAK,CAACE,IAAEC,EAAC,IAAE,GAAGH,IAAEC,EAAC;AAAE,QAAAD,KAAE,MAAI,GAAGE,IAAEC,EAAC;AAAA,MAAC;AAAA;AAAM,MAAAH,KAAE,GAAGA,IAAEC,EAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,KAAEA,KAAE,YAAU,OAAOA,MAAG,GAAGA,EAAC,IAAEA,KAAE,OAAOA,EAAC,KAAGA,KAAE,GAAG,IAAGA,EAAC,GAAEA,KAAE,GAAGA,EAAC,IAAE,OAAOA,EAAC,IAAE,OAAOA,EAAC,IAAGA,MAAG,GAAGA,EAAC,IAAE,YAAU,OAAOA,KAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA;AAAE,WAAOA;AAAE,MAAIC,KAAE,OAAOD;AAAE,MAAG,aAAWC;AAAE,WAAO,OAAO,GAAG,IAAGD,EAAC,CAAC;AAAE,MAAG,GAAGA,EAAC,GAAE;AAAC,QAAG,aAAWC;AAAE,aAAOA,KAAE,GAAG,OAAOD,EAAC,CAAC,GAAE,GAAGC,EAAC,KAAGA,MAAG,IAAED,KAAE,OAAOC,EAAC,KAAG,QAAMA,KAAED,GAAE,QAAQ,GAAG,OAAKA,KAAEA,GAAE,UAAU,GAAEC,EAAC,IAAG,GAAGD,EAAC,MAAI,GAAGA,EAAC,GAAEA,KAAE,GAAG,IAAG,EAAE,KAAIA;AAAE,QAAG,aAAWC;AAAE,cAAOD,KAAE,GAAGA,EAAC,MAAI,KAAG,GAAGA,EAAC,IAAEA,KAAE,SAASA,IAAE;AAAC,YAAGA,KAAE,GAAE;AAAC,aAAGA,EAAC;AAAE,cAAIC,KAAE,GAAG,IAAG,EAAE;AAAE,iBAAOD,KAAE,OAAOC,EAAC,GAAE,GAAGD,EAAC,IAAEA,KAAEC;AAAA,QAAC;AAAC,eAAO,GAAGA,KAAE,OAAOD,EAAC,CAAC,IAAEC,MAAG,GAAGD,EAAC,GAAE,GAAG,IAAG,EAAE;AAAA,MAAE,EAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,YAAU,OAAOA;AAAE,UAAM,MAAM;AAAE,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,MAAG,YAAU,OAAOA;AAAE,UAAM,MAAM;AAAE,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,KAAEA,KAAE;AAAM;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMH,MAAG,YAAU,OAAOA,MAAGA,GAAE,MAAI;AAAG,WAAOA;AAAE,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,WAAOE,KAAE,IAAEC,OAAIH,KAAEC,GAAE,CAAC,OAAK,IAAID,KAAE,IAAIC,MAAG,CAAC,GAAED,KAAEC,GAAE,CAAC,IAAED,KAAGC,KAAED,MAAGC,KAAE,IAAIA,OAAEA,KAAE,QAAOA;AAAE,MAAIG,KAAEF,KAAE,IAAEF,GAAE,CAAC;AAAE,SAAO,MAAII,OAAIA,MAAG,KAAGD,KAAGC,MAAG,IAAED,IAAEC,OAAIF,MAAG,GAAGF,IAAEI,EAAC,GAAE,IAAIH,GAAED,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,MAAGD;AAAE,OAAE;AAAC,UAAG,CAAC,GAAGA,KAAED,EAAC;AAAE,cAAM,EAAE,OAAO;AAAE,cAAO,OAAOC,IAAE;AAAA,QAAC,KAAI;AAAS,UAAAA,KAAE,GAAGA,EAAC;AAAE,gBAAM;AAAA,QAAE,KAAI;AAAS,cAAGD,KAAEC,KAAE,GAAG,IAAGA,EAAC,GAAE,GAAGD,EAAC,GAAE;AAAC,gBAAG,CAAC,4BAA4B,KAAKA,EAAC;AAAE,oBAAM,MAAM,OAAOA,EAAC,CAAC;AAAA,UAAC,WAAS,GAAGA,EAAC,KAAG,CAAC,OAAO,cAAcA,EAAC;AAAE,kBAAM,MAAM,OAAOA,EAAC,CAAC;AAAE,UAAAC,KAAE,KAAG,OAAOA,EAAC,IAAE,GAAGA,EAAC,IAAEA,KAAE,MAAI,MAAI,GAAGA,EAAC,IAAEA,GAAE,KAAK,KAAG,MAAI,OAAOA,EAAC;AAAE,gBAAM;AAAA,QAAE;AAAQ,UAAAA,KAAE,GAAGA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA;AAAM,IAAAA,KAAE,GAAGD,EAAC;AAAE,SAAM,YAAU,QAAOE,KAAE,SAAOF,KAAEC,MAAGC,KAAE,IAAE,SAAOF,OAAI,GAAGC,KAAE,CAACC,EAAC,IAAED,KAAEC;AAAC;AAAC,IAAM,KAAG,CAAC;AAAE,IAAI,KAAG,WAAU;AAAC,MAAG;AAAC,WAAO,EAAE,IAAI,cAAc,IAAG;AAAA,MAAC,cAAa;AAAC,cAAM;AAAA,MAAC;AAAA,IAAC,GAAC,GAAE;AAAA,EAAE,QAAC;AAAM,WAAM;AAAA,EAAE;AAAC,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,cAAa;AAAC,SAAK,IAAE,oBAAI;AAAA,EAAG;AAAA,EAAC,IAAIF,IAAE;AAAC,WAAO,KAAK,EAAE,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAEC,IAAE;AAAC,WAAO,KAAK,EAAE,IAAID,IAAEC,EAAC,GAAE,KAAK,OAAK,KAAK,EAAE,MAAK;AAAA,EAAI;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAOA,KAAE,KAAK,EAAE,OAAOA,EAAC,GAAE,KAAK,OAAK,KAAK,EAAE,MAAKA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,MAAM,GAAE,KAAK,OAAK,KAAK,EAAE;AAAA,EAAI;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,EAAE,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,EAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,EAAE,KAAK;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,EAAE,OAAO;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAE;AAAC,WAAO,KAAK,EAAE,QAAQD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAC;AAAC;AAAC,IAAM,KAAG,MAAI,OAAO,eAAe,GAAG,WAAU,IAAI,SAAS,GAAE,OAAO,iBAAiB,GAAG,WAAU,EAAC,MAAK,EAAC,OAAM,GAAE,cAAa,MAAG,YAAW,MAAG,UAAS,KAAE,EAAC,CAAC,GAAE,MAAI,cAAc,IAAG;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,IAAEA,GAAE;AAAE,UAAM,MAAM,gCAAgC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAEC,KAAE,IAAGC,KAAE,IAAG;AAAC,UAAM;AAAE,QAAIC,KAAE,IAAEJ,GAAE,CAAC;AAAE,IAAAI,MAAG,IAAG,GAAGJ,IAAEI,EAAC,GAAE,KAAK,IAAEA,IAAE,KAAK,IAAEH,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAE,KAAK,IAAE,KAAGC;AAAE,aAAQE,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAI;AAAC,YAAMG,KAAER,GAAEK,EAAC,GAAEI,KAAEP,GAAEM,GAAE,CAAC,GAAE,OAAG,IAAE;AAAE,UAAIF,KAAEE,GAAE,CAAC;AAAE,MAAAP,KAAE,WAASK,OAAIA,KAAE,QAAMA,KAAEH,GAAEK,GAAE,CAAC,GAAE,OAAG,MAAG,QAAO,QAAOJ,EAAC,GAAE,MAAM,IAAIK,IAAEH,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGN,KAAE,IAAG;AAAC,QAAG,MAAI,KAAK;AAAK,aAAO,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,KAAE,IAAG;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAE,MAAM,QAAQ;AAAE,aAAQC,IAAE,EAAEA,KAAED,GAAE,KAAK,GAAG;AAAM,OAACC,KAAEA,GAAE,OAAO,CAAC,IAAEH,GAAEG,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAEH,GAAEG,GAAE,CAAC,CAAC,GAAEF,GAAE,KAAKE,EAAC;AAAE,WAAOF;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,OAAG,IAAI,GAAE,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,MAAM,OAAO,KAAK,EAAEA,IAAE,MAAG,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAIA,KAAE,KAAK,GAAG;AAAE,WAAO,IAAI,GAAGA,IAAE,IAAG,IAAI;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,GAAG;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAIA,KAAE,KAAK,GAAG;AAAE,WAAO,IAAI,GAAGA,IAAE,GAAG,UAAU,KAAI,IAAI;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAE;AAAC,UAAM,QAAS,CAACC,IAAEC,OAAI;AAAC,MAAAH,GAAE,KAAKC,IAAE,KAAK,IAAIE,EAAC,GAAEA,IAAE,IAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAIH,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,SAAOD,KAAE,KAAK,EAAEA,IAAE,MAAG,KAAE,KAAG,OAAK,QAAMC,MAAG,MAAM,OAAOD,EAAC,GAAE,QAAM,MAAM,IAAIA,IAAE,KAAK,EAAEC,IAAE,MAAG,MAAG,KAAK,GAAE,OAAG,KAAK,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAE;AAAC,UAAMC,KAAE,KAAK,EAAED,GAAE,CAAC,GAAE,OAAG,IAAE;AAAE,IAAAA,KAAEA,GAAE,CAAC,GAAEA,KAAE,KAAK,IAAE,WAASA,KAAE,OAAKA,KAAE,KAAK,EAAEA,IAAE,OAAG,MAAG,QAAO,OAAG,KAAK,CAAC,GAAE,MAAM,IAAIC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,MAAM,IAAI,KAAK,EAAEA,IAAE,OAAG,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,IAAAA,KAAE,KAAK,EAAEA,IAAE,OAAG,KAAE;AAAE,UAAMC,KAAE,MAAM,IAAID,EAAC;AAAE,QAAG,WAASC,IAAE;AAAC,UAAIC,KAAE,KAAK;AAAE,aAAOA,OAAIA,KAAE,KAAK,EAAED,IAAE,OAAG,MAAGC,IAAE,KAAK,IAAG,KAAK,CAAC,OAAKD,MAAG,MAAM,IAAID,IAAEE,EAAC,GAAEA,MAAGD;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,MAAM,KAAK,MAAM,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,MAAM,KAAK;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOL,KAAE,GAAGA,IAAEG,IAAED,IAAEG,EAAC,GAAED,OAAIJ,KAAE,GAAGA,EAAC,IAAGA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,CAACA,IAAE,KAAK,IAAIA,EAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAO;AAAP,IAAU;AAAG,SAAS,KAAI;AAAC,SAAO,YAAK,IAAI,GAAG,GAAG,CAAC,CAAC,GAAE,QAAO,QAAO,QAAO,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMJ,IAAE;AAAC,QAAG,MAAM,QAAQA,EAAC;AAAE,MAAAA,KAAE,GAAGA,EAAC,IAAE,SAAOI,MAAG,KAAG,IAAEJ,GAAE,CAAC,KAAGA,KAAE,GAAGA,IAAEC,IAAEC,IAAE,WAASC,IAAEC,EAAC;AAAA,aAAU,GAAGJ,EAAC,GAAE;AAAC,YAAMK,KAAE,CAAC;AAAE,eAAQG,MAAKR;AAAE,QAAAK,GAAEG,EAAC,IAAE,GAAGR,GAAEQ,EAAC,GAAEP,IAAEC,IAAEC,IAAEC,EAAC;AAAE,MAAAJ,KAAEK;AAAA,IAAC;AAAM,MAAAL,KAAEC,GAAED,IAAEG,EAAC;AAAE,WAAOH;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,MAAGD,KAAE,IAAEF,GAAE,CAAC,IAAE,GAAEQ,KAAEL,KAAE,CAAC,EAAE,KAAGE,MAAG;AAAO,EAAAF,KAAE,GAAGH,EAAC;AAAE,WAAQA,KAAE,GAAEA,KAAEG,GAAE,QAAOH;AAAI,IAAAG,GAAEH,EAAC,IAAE,GAAGG,GAAEH,EAAC,GAAEC,IAAEC,IAAEM,IAAEJ,EAAC;AAAE,SAAOF,QAAKF,KAAE,GAAGA,EAAC,OAAKG,GAAE,CAAC,IAAE,GAAGH,EAAC,IAAGE,GAAEG,IAAEF,EAAC,IAAGA;AAAC;AAAC,SAAS,GAAGH,IAAE;AAAC,SAAO,GAAGA,IAAE,IAAG,QAAO,QAAO,KAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAI,KAAGA,GAAE,OAAO,IAAEA,cAAa,KAAGA,GAAE,GAAG,EAAE,IAAE,SAASA,IAAE;AAAC,YAAO,OAAOA,IAAE;AAAA,MAAC,KAAI;AAAS,eAAO,SAASA,EAAC,IAAEA,KAAE,OAAOA,EAAC;AAAA,MAAE,KAAI;AAAS,eAAO,GAAGA,EAAC,IAAE,OAAOA,EAAC,IAAE,OAAOA,EAAC;AAAA,MAAE,KAAI;AAAU,eAAOA,KAAE,IAAE;AAAA,MAAE,KAAI;AAAS,YAAGA;AAAE,cAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,gBAAG,GAAGA,EAAC;AAAE;AAAA,UAAM,OAAK;AAAC,gBAAG,EAAEA,EAAC;AAAE,qBAAO,EAAEA,EAAC;AAAE,gBAAGA,cAAa,GAAE;AAAC,oBAAMC,KAAED,GAAE;AAAE,qBAAO,QAAMC,KAAE,KAAG,YAAU,OAAOA,KAAEA,KAAED,GAAE,IAAE,EAAEC,EAAC;AAAA,YAAC;AAAC,gBAAGD,cAAa;AAAG,qBAAOA,GAAE,GAAG;AAAA,UAAC;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC,EAAEA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,GAAGA,IAAE,IAAG,QAAO,QAAO,KAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAOF,KAAE,GAAGA,IAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,KAAE,IAAE,CAAC,GAAED,OAAI,MAAIC,MAAG,GAAGF,IAAE,KAAK,GAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMH,IAAE;AAAC,QAAII,KAAE;AAAG,IAAAF,MAAGF,KAAE,CAACE,EAAC,GAAEE,MAAG,OAAKJ,KAAE,CAAC,GAAEC,OAAIG,KAAE,YAAUA,MAAG,OAAKH,OAAI;AAAA,EAAG,OAAK;AAAC,QAAG,CAAC,MAAM,QAAQD,EAAC;AAAE,YAAM,MAAM,MAAM;AAAE,QAAG,QAAMI,KAAE,IAAEJ,GAAE,CAAC;AAAG,YAAM,MAAM,MAAM;AAAE,QAAG,KAAGI;AAAE,aAAOJ;AAAE,QAAG,MAAIG,MAAG,MAAIA,OAAIC,MAAG,KAAIF,OAAIE,MAAG,KAAIF,OAAIF,GAAE,CAAC;AAAG,YAAM,MAAM,KAAK;AAAE,OAAE;AAAC,UAAGG,MAAGD,KAAEF,IAAG,QAAO;AAAC,cAAMA,KAAEG,KAAE;AAAE,YAAG,GAAGD,GAAEF,EAAC,CAAC,GAAE;AAAC,eAAIC,KAAED,MAAG,OAAKI,MAAG,OAAK,IAAE,QAAM;AAAK,kBAAM,MAAM,QAAQ;AAAE,UAAAA,KAAE,YAAUA,MAAG,OAAKH,OAAI;AAAG,gBAAM;AAAA,QAAC;AAAA,MAAC;AAAC,UAAGA,IAAE;AAAC,aAAIA,KAAE,KAAK,IAAIA,IAAEE,MAAG,MAAIC,KAAE,IAAE,GAAG,KAAG;AAAK,gBAAM,MAAM,MAAM;AAAE,QAAAA,KAAE,YAAUA,MAAG,OAAKH,OAAI;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,GAAGD,IAAEI,EAAC,GAAEJ;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,KAAE,IAAG;AAAC,MAAG,QAAMF,IAAE;AAAC,QAAG,KAAGA,cAAa;AAAW,aAAOC,KAAED,KAAE,IAAI,WAAWA,EAAC;AAAE,QAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,UAAIG,KAAE,IAAEH,GAAE,CAAC;AAAE,aAAO,IAAEG,KAAEH,MAAGC,YAAI,MAAIE,MAAG,CAAC,EAAE,KAAGA,OAAI,EAAE,KAAGA,MAAG,EAAE,KAAGA,OAAIF,MAAG,GAAGD,IAAE,UAAQ,KAAGG,GAAE,GAAEH,MAAG,GAAGA,IAAE,IAAG,IAAEG,KAAE,KAAGD,IAAE,MAAG,IAAE;AAAA,IAAE;AAAC,WAAOF,GAAE,MAAI,KAAGA,KAAE,KAAGG,KAAE,KAAGD,KAAEF,GAAE,GAAG,CAAC,KAAGA,KAAE,IAAIA,GAAE,YAAY,GAAGE,IAAEC,IAAE,IAAE,CAAC,IAAEH,cAAa,MAAI,EAAE,IAAEA,GAAE,OAAKE,KAAE,GAAGF,GAAE,EAAE,EAAE,CAAC,GAAEA,KAAE,IAAI,GAAGE,IAAEF,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,IAAGA;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAED,MAAG,IAAED,KAAE,KAAG,IAAGG,KAAE,CAAC,EAAE,KAAGH;AAAG,SAAOD,KAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAGH,EAAC;AAAE,QAAII,KAAED,GAAE;AAAO,UAAME,KAAE,MAAIJ,KAAEE,GAAEC,KAAE,CAAC,IAAE;AAAO,SAAIA,MAAGC,KAAE,KAAG,GAAEJ,KAAE,MAAIA,KAAE,IAAE,GAAEA,KAAEG,IAAEH;AAAI,MAAAE,GAAEF,EAAC,IAAEC,GAAEC,GAAEF,EAAC,CAAC;AAAE,QAAGI,IAAE;AAAC,MAAAJ,KAAEE,GAAEF,EAAC,IAAE,CAAC;AAAE,iBAAUD,MAAKK;AAAE,QAAAJ,GAAED,EAAC,IAAEE,GAAEG,GAAEL,EAAC,CAAC;AAAA,IAAC;AAAC,YAAOA,KAAE,GAAGA,EAAC,OAAKG,GAAE,CAAC,IAAE,GAAGH,EAAC,IAAGG;AAAA,EAAC,EAAEH,IAAEC,IAAG,CAAAD,OAAG,GAAGA,IAAEI,IAAED,EAAC,CAAE,GAAE,GAAGH,IAAE,MAAIE,KAAE,IAAE,EAAE,GAAEF;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAED,GAAE,GAAEE,KAAE,IAAED,GAAE,CAAC;AAAE,SAAO,IAAEC,KAAE,IAAIF,GAAE,YAAY,GAAGC,IAAEC,IAAE,KAAE,CAAC,IAAEF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,GAAGD,KAAEA,GAAE,GAAE,IAAEA,GAAE,CAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,OAAKD;AAAE,WAAO;AAAK,MAAIE,KAAEF,MAAG,MAAID,KAAE,IAAE;AAAI,QAAMM,KAAEP,GAAE,SAAO;AAAE,SAAOI,MAAGG,MAAG,MAAIN,KAAED,GAAEO,EAAC,EAAEL,EAAC,IAAEC,MAAG,MAAIF,MAAG,SAAOA,KAAED,GAAEO,EAAC,EAAEL,EAAC,MAAI,QAAMF,GAAEI,EAAC,KAAG,QAAM,OAAKA,MAAGJ,KAAE,UAAI,CAAC,IAAG,CAAC,KAAG,MAAI,MAAIA,GAAE,CAAC,IAAEI,KAAE,GAAE,EAAEJ,KAAE,MAAM,GAAE,UAAU,GAAE,SAASA,IAAE;AAAC,MAAE,WAAY,MAAI;AAAC,YAAMA;AAAA,IAAC,GAAG,CAAC;AAAA,EAAC,EAAEA,EAAC,KAAIC,MAAGG,MAAGG,KAAEP,GAAEI,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGL,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE;AAAE,MAAII,KAAE,IAAED,GAAE,CAAC;AAAE,SAAO,GAAGC,EAAC,GAAE,GAAGD,IAAEC,IAAEH,IAAEC,EAAC,GAAEF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,MAAIH,KAAE,IAAE,IAAGI,KAAEH,KAAEE;AAAE,MAAII,KAAER,GAAE,SAAO;AAAE,SAAOK,MAAGG,MAAG,MAAIP,MAAGD,GAAEQ,EAAC,EAAEN,EAAC,IAAEC,IAAEF,MAAGI,MAAGG,MAAGR,GAAEK,EAAC,IAAEF,IAAE,MAAIF,OAAIC,OAAKF,KAAEA,GAAEQ,EAAC,MAAI,OAAOR,GAAEE,EAAC,IAAGD,OAAI,WAASE,OAAID,OAAIM,KAAEP,MAAG,KAAG,QAAM,aAAW,QAAME,OAAIH,GAAEQ,KAAEJ,EAAC,IAAE,EAAC,CAACF,EAAC,GAAEC,GAAC,GAAE,GAAGH,IAAEC,MAAG,GAAG,KAAGD,GAAEK,EAAC,IAAEF,KAAGF;AAAE;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAGF,KAAEA,GAAE,GAAG,CAAC;AAAE,QAAMG,KAAE,GAAGH,IAAEE,IAAED,EAAC,GAAEG,KAAE,GAAGD,EAAC;AAAE,SAAO,QAAMC,MAAGA,OAAID,MAAG,GAAGH,IAAEE,IAAED,IAAEG,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,MAAIC,KAAE,KAAGD,KAAEA,GAAE,GAAG,CAAC;AAAE,QAAME,KAAE,GAAGF,IAAEC,IAAE,CAAC,GAAEE,KAAE,GAAGD,IAAE,IAAE;AAAE,SAAO,QAAMC,MAAGA,OAAID,MAAG,GAAGF,IAAEC,IAAE,GAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,KAAI;AAAC,SAAO,WAAS,KAAG,IAAE;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEL,GAAE,GAAEQ,KAAE,KAAGR,KAAE,IAAEK,GAAE,CAAC,KAAG,IAAEF;AAAE,EAAAC,KAAE,CAAC,CAACA;AAAE,MAAIK,KAAE,KAAGN,KAAE,GAAGE,IAAEL,IAAEC,EAAC,GAAG,CAAC;AAAE,MAAG,EAAE,IAAEQ,KAAG;AAAC,QAAEA,OAAIN,KAAE,GAAGA,EAAC,GAAEM,KAAE,GAAGA,IAAET,EAAC,GAAEA,KAAE,GAAGK,IAAEL,IAAEC,IAAEE,EAAC;AAAG,QAAIC,KAAE,GAAEI,KAAE;AAAE,WAAKJ,KAAED,GAAE,QAAOC,MAAI;AAAC,YAAMJ,KAAEE,GAAEC,GAAEC,EAAC,CAAC;AAAE,cAAMJ,OAAIG,GAAEK,IAAG,IAAER;AAAA,IAAE;AAAC,IAAAQ,KAAEJ,OAAID,GAAE,SAAOK,KAAGC,KAAE,GAAGA,IAAET,EAAC,GAAEE,KAAE,SAAO,KAAGO,KAAGA,KAAEP,MAAG,OAAM,GAAGC,IAAEM,EAAC,GAAE,IAAEA,MAAG,OAAO,OAAON,EAAC;AAAA,EAAC;AAAC,SAAO,MAAIK,MAAG,MAAIA,MAAG,KAAGC,KAAE,GAAGA,EAAC,MAAIL,KAAEK,IAAEA,MAAG,GAAEA,OAAIL,MAAG,GAAGD,IAAEM,EAAC,GAAE,OAAO,OAAON,EAAC,MAAI,MAAIK,MAAG,GAAGC,EAAC,MAAIN,KAAE,GAAGA,EAAC,GAAEM,KAAE,GAAGA,IAAET,EAAC,GAAES,KAAE,GAAGA,IAAET,IAAEI,EAAC,GAAE,GAAGD,IAAEM,EAAC,GAAET,KAAE,GAAGK,IAAEL,IAAEC,IAAEE,EAAC,IAAG,GAAGM,EAAC,MAAIR,KAAEQ,IAAEA,KAAE,GAAGA,IAAET,IAAEI,EAAC,GAAEK,OAAIR,MAAG,GAAGE,IAAEM,EAAC,KAAIN;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOH,KAAE,GAAGA,IAAEC,IAAEC,IAAEC,EAAC,GAAE,MAAM,QAAQH,EAAC,IAAEA,KAAE;AAAE;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,MAAID,OAAIA,KAAE,GAAGA,IAAEC,EAAC,IAAG,IAAED;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,CAAC,EAAE,IAAEA,OAAI,CAAC,EAAE,IAAEA,OAAI,CAAC,EAAE,OAAKA;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,EAAAA,KAAE,GAAGA,EAAC;AAAE,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAMC,KAAEF,GAAEC,EAAC,IAAE,GAAGD,GAAEC,EAAC,CAAC;AAAE,UAAM,QAAQC,GAAE,CAAC,CAAC,MAAIA,GAAE,CAAC,IAAE,GAAGA,GAAE,CAAC,CAAC;AAAA,EAAE;AAAC,SAAOF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAGJ,KAAEA,GAAE,GAAG,CAAC;AAAE,KAAGI,EAAC,GAAE,GAAGJ,IAAEI,IAAEH,KAAG,QAAME,KAAE,MAAI,OAAOD,EAAC,IAAEA,OAAIC,MAAG,SAAOD,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,KAAGH,EAAC;AAAE,MAAII,KAAE,EAAE,EAAE,KAAGJ,OAAI,QAAMA;AAAG,QAAMO,MAAGJ,KAAE,GAAGJ,IAAEC,IAAEC,IAAEE,EAAC,OAAK;AAAG,MAAGC,MAAG,CAACG,IAAE;AAAC,QAAIC,KAAEJ,KAAEG,KAAE,IAAEJ,GAAE,CAAC,IAAE;AAAE,KAAC,CAACI,MAAG,IAAEC,MAAG,GAAGA,EAAC,KAAG,IAAEA,MAAG,EAAE,KAAGA,SAAML,KAAE,GAAGA,EAAC,GAAEK,KAAE,GAAGA,IAAER,EAAC,GAAEA,KAAE,GAAGD,IAAEC,IAAEC,IAAEE,EAAC,IAAGK,KAAE,MAAI,GAAGA,IAAER,EAAC,GAAEQ,KAAE,GAAGN,KAAE,MAAIM,KAAE,KAAGA,IAAER,IAAE,IAAE,GAAEQ,OAAIJ,MAAG,GAAGD,IAAEK,EAAC;AAAA,EAAC;AAAC,SAAOL;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAG,SAAO,GAAG,GAAGF,KAAEA,GAAE,CAAC,GAAEA,IAAE,IAAEA,GAAE,CAAC,GAAEE,EAAC,MAAID,KAAEA,KAAE;AAAE;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAG;AAAE,WAAOA,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,oBAAI;AAAK,MAAG,KAAKA;AAAE,WAAOA,GAAE,CAAC;AAAE,QAAMC,KAAE,oBAAI;AAAI,SAAO,OAAO,eAAeD,IAAE,GAAE,EAAC,OAAMC,GAAC,CAAC,GAAEA;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGJ,EAAC,GAAEK,KAAE,GAAGD,IAAEJ,IAAEC,IAAEC,EAAC;AAAE,SAAOG,OAAIF,OAAIE,OAAIJ,KAAE,GAAGD,IAAEC,IAAEI,EAAC,IAAGD,GAAE,IAAIF,IAAEC,EAAC,IAAGF;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEJ,GAAE,IAAIG,EAAC;AAAE,MAAG,QAAMC;AAAE,WAAOA;AAAE,EAAAA,KAAE;AAAE,WAAQJ,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAI;AAAC,UAAMK,KAAEF,GAAEH,EAAC;AAAE,YAAM,GAAGC,IAAEC,IAAEG,EAAC,MAAI,MAAID,OAAIF,KAAE,GAAGD,IAAEC,IAAEE,EAAC,IAAGA,KAAEC;AAAA,EAAE;AAAC,SAAOL,GAAE,IAAIG,IAAEC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,IAAEL,GAAE,CAAC;AAAE,MAAG,SAAOG,KAAE,GAAGH,IAAEK,IAAEH,IAAEC,EAAC,MAAIA,GAAE,MAAI;AAAG,YAAOF,KAAE,GAAGE,EAAC,OAAKA,MAAG,GAAGH,IAAEK,IAAEH,IAAED,EAAC,GAAEA,GAAE;AAAE,MAAG,MAAM,QAAQE,EAAC,GAAE;AAAC,UAAMH,KAAE,IAAEG,GAAE,CAAC;AAAE,IAAAC,KAAE,IAAEJ,KAAE,GAAG,GAAGG,IAAEH,IAAE,KAAE,GAAEC,IAAE,IAAE,IAAE,KAAGD,KAAEG,KAAE,GAAGC,IAAEH,IAAE,IAAE;AAAA,EAAC;AAAM,IAAAG,KAAE,GAAG,QAAOH,IAAE,IAAE;AAAE,SAAOG,OAAID,MAAG,GAAGH,IAAEK,IAAEH,IAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAGJ,KAAEA,GAAE,GAAG,CAAC;AAAE,UAAOC,KAAE,GAAGE,KAAE,GAAGH,IAAEI,IAAEF,IAAEC,EAAC,GAAEF,IAAE,OAAGG,EAAC,OAAKD,MAAG,QAAMF,MAAG,GAAGD,IAAEI,IAAEF,IAAED,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,KAAE,OAAG;AAAC,MAAG,SAAOF,KAAE,GAAGD,IAAEC,IAAEC,IAAEC,EAAC;AAAG,WAAOF;AAAE,MAAG,EAAE,KAAGE,KAAE,KAAGH,KAAEA,GAAE,GAAG,CAAC,KAAI;AAAC,UAAMI,KAAE,GAAGH,EAAC;AAAE,IAAAG,OAAIH,MAAG,GAAGD,IAAEG,IAAED,IAAED,KAAEG,EAAC;AAAA,EAAC;AAAC,SAAOH;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEG,IAAE;AAAC,EAAAR,KAAEA,GAAE;AAAE,MAAIS,KAAE,CAAC,EAAE,IAAER;AAAG,QAAMK,KAAEG,KAAE,IAAEL;AAAE,EAAAC,KAAE,CAAC,CAACA,IAAEG,YAAI,CAACC;AAAE,MAAIF,KAAE,KAAGH,KAAE,GAAGJ,IAAEC,IAAEE,EAAC,GAAG,CAAC;AAAE,MAAG,EAAEM,KAAE,CAAC,EAAE,IAAEF,MAAI;AAAC,QAAIG,KAAEN,IAAE,IAAEH;AAAE,UAAMD,KAAE,CAAC,EAAE,KAAGO,KAAE,GAAGA,IAAEN,EAAC;AAAI,IAAAD,OAAI,KAAG;AAAG,QAAIG,KAAE,CAACH,IAAEK,KAAE,MAAGG,KAAE,GAAEC,KAAE;AAAE,WAAKD,KAAEE,GAAE,QAAOF,MAAI;AAAC,YAAMP,KAAE,GAAGS,GAAEF,EAAC,GAAEN,IAAE,OAAG,CAAC;AAAE,UAAGD,cAAaC,IAAE;AAAC,YAAG,CAACF,IAAE;AAAC,gBAAMA,KAAE,CAAC,EAAE,KAAG,IAAEC,GAAE,EAAE,CAAC;AAAI,UAAAE,YAAI,CAACH,KAAEK,YAAIL;AAAA,QAAC;AAAC,QAAAU,GAAED,IAAG,IAAER;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAQ,KAAED,OAAIE,GAAE,SAAOD,KAAGF,MAAG,GAAEA,KAAEF,KAAE,KAAGE,KAAE,MAAIA,IAAE,GAAGG,IAAEH,KAAEJ,KAAE,IAAEI,KAAE,KAAGA,EAAC,GAAEP,MAAG,OAAO,OAAOU,EAAC;AAAA,EAAC;AAAC,MAAGF,MAAG,EAAE,IAAED,MAAG,CAACH,GAAE,WAAS,MAAIE,MAAG,MAAIA,MAAG,KAAGC,MAAI;AAAC,SAAI,GAAGA,EAAC,MAAIH,KAAE,GAAGA,EAAC,GAAEG,KAAE,GAAGA,IAAEN,EAAC,GAAEA,KAAE,GAAGD,IAAEC,IAAEE,IAAEC,EAAC,IAAGF,KAAEE,IAAEI,KAAED,IAAEG,KAAE,GAAEA,KAAER,GAAE,QAAOQ;AAAI,OAACH,KAAEL,GAAEQ,EAAC,QAAM,IAAE,GAAGH,EAAC,OAAKL,GAAEQ,EAAC,IAAE;AAAG,IAAAF,MAAG,GAAE,GAAGN,IAAEM,KAAEN,GAAE,SAAO,MAAIM,KAAE,KAAGA,EAAC,GAAED,KAAEC;AAAA,EAAC;AAAC,SAAO,MAAIF,MAAG,MAAIA,MAAG,KAAGC,KAAE,GAAGA,EAAC,MAAIN,KAAEM,KAAGA,MAAG,CAACH,GAAE,UAAQ,KAAGG,OAAI,CAACE,MAAG,KAAGF,MAAG,IAAE,UAAQN,MAAG,GAAGG,IAAEG,EAAC,GAAE,OAAO,OAAOH,EAAC,MAAI,MAAIE,MAAG,GAAGC,EAAC,MAAI,GAAGH,KAAE,GAAGA,EAAC,GAAEG,KAAE,GAAGA,KAAE,GAAGA,IAAEN,EAAC,GAAEA,IAAEI,EAAC,CAAC,GAAEJ,KAAE,GAAGD,IAAEC,IAAEE,IAAEC,EAAC,IAAG,GAAGG,EAAC,MAAIJ,KAAEI,KAAGA,KAAE,GAAGA,IAAEN,IAAEI,EAAC,OAAKF,MAAG,GAAGC,IAAEG,EAAC,KAAIH;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAEH,GAAE,EAAE,CAAC;AAAE,SAAO,GAAGA,IAAEG,IAAEF,IAAEC,IAAE,GAAG,GAAE,OAAG,EAAE,IAAEC,GAAE;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,QAAMA,OAAIA,KAAE,SAAQ,GAAGH,IAAEE,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMA,OAAIA,KAAE;AAAQ,KAAE;AAAC,QAAIC,KAAE,KAAGJ,KAAEA,GAAE,GAAG,CAAC;AAAE,QAAG,GAAGI,EAAC,GAAE,QAAMD,IAAE;AAAC,YAAMA,KAAE,GAAGH,EAAC;AAAE,UAAG,GAAGG,IAAEH,IAAEI,IAAEF,EAAC,MAAID;AAAE,cAAM;AAAE,MAAAE,GAAE,IAAID,IAAE,CAAC;AAAA,IAAC;AAAM,MAAAE,KAAE,GAAGJ,IAAEI,IAAEF,IAAED,EAAC;AAAE,OAAGD,IAAEI,IAAEH,IAAEE,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAE;AAAC,SAAM,SAAOD,KAAE,MAAI,IAAEC,KAAE,IAAED,KAAE,KAAGA;AAAG;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAO,KAAGD,MAAGC,OAAIF,MAAG,MAAKA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAEJ,GAAE,EAAE,CAAC;AAAE,KAAGI,EAAC,GAAEJ,KAAE,GAAGA,IAAEI,IAAEF,IAAED,IAAE,GAAE,IAAE,GAAEE,KAAE,QAAMA,KAAEA,KAAE,IAAID,MAAEF,GAAE,KAAKG,EAAC,GAAEH,GAAE,CAAC,IAAE,KAAG,IAAEG,GAAE,EAAE,CAAC,KAAG,KAAGH,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,GAAG,GAAGD,IAAEC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,GAAG,GAAGD,IAAEC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,GAAGD,IAAEC,EAAC,KAAG;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMA,MAAG,aAAW,OAAOA;AAAE,UAAMF,KAAE,OAAOE,IAAE,MAAM,4BAA4B,YAAUF,KAAEA,KAAEE,KAAE,MAAM,QAAQA,EAAC,IAAE,UAAQF,KAAE,WAAWE,IAAG;AAAE,KAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMA,IAAE;AAAC,QAAG,YAAU,OAAOA;AAAE,YAAM,EAAE,OAAO;AAAE,QAAG,CAAC,GAAGA,EAAC;AAAE,YAAM,EAAE,OAAO;AAAE,IAAAA,MAAG;AAAA,EAAC;AAAC,KAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMA,MAAG,YAAU,OAAOA;AAAE,UAAM,MAAM,uDAAuD,OAAOA,OAAMA,IAAG;AAAE,KAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC;AAAC,UAAMM,KAAER,GAAE;AAAE,QAAIS,KAAE,IAAED,GAAE,CAAC;AAAE,QAAG,GAAGC,EAAC,GAAE,QAAMP;AAAE,SAAGM,IAAEC,IAAER,EAAC;AAAA,SAAM;AAAC,UAAIE,KAAEH,KAAE,IAAEE,GAAE,CAAC,GAAEE,KAAE,GAAGJ,EAAC,GAAEK,KAAED,MAAG,OAAO,SAASF,EAAC;AAAE,WAAIE,OAAIJ,KAAE,IAAGK,OAAIH,KAAE,GAAGA,EAAC,GAAEC,KAAE,GAAEH,KAAE,GAAGA,KAAE,GAAGA,IAAES,EAAC,GAAEA,IAAE,IAAE,GAAEJ,KAAE,QAAIL,MAAG,IAAGI,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,cAAMH,KAAEC,GAAEE,EAAC,GAAEI,KAAE,GAAGP,EAAC;AAAE,eAAO,GAAGA,IAAEO,EAAC,MAAIH,OAAIH,KAAE,GAAGA,EAAC,GAAEC,KAAE,GAAEH,KAAE,GAAGA,KAAE,GAAGA,IAAES,EAAC,GAAEA,IAAE,IAAE,GAAEJ,KAAE,QAAIH,GAAEE,EAAC,IAAEI;AAAA,MAAE;AAAC,MAAAR,OAAIG,OAAIE,OAAIH,KAAE,GAAGA,EAAC,GAAEF,KAAE,GAAGA,KAAE,GAAGA,IAAES,EAAC,GAAEA,IAAE,IAAE,IAAG,GAAGP,IAAEF,EAAC,IAAG,GAAGQ,IAAEC,IAAER,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,KAAG,IAAEF,GAAE,EAAE,CAAC,CAAC,GAAE,GAAGA,IAAEC,IAAE,IAAG,GAAE,IAAE,EAAE,KAAK,GAAGC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,SAAO,MAAM,sBAAsBD,mBAAkBC,KAAI;AAAC;AAAC,SAAS,KAAI;AAAC,SAAO,MAAM,6CAA6C;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,MAAM,0CAA0CA,QAAOD,IAAG;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,YAAU,OAAOA;AAAE,WAAM,EAAC,QAAO,EAAEA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAG,MAAM,QAAQA,EAAC;AAAE,WAAM,EAAC,QAAO,IAAI,WAAWA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc;AAAW,WAAM,EAAC,QAAOA,IAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc;AAAY,WAAM,EAAC,QAAO,IAAI,WAAWA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc;AAAE,WAAM,EAAC,QAAO,EAAEA,EAAC,KAAG,IAAI,WAAW,CAAC,GAAE,GAAE,KAAE;AAAE,MAAGA,cAAa;AAAW,WAAM,EAAC,QAAO,IAAI,WAAWA,GAAE,QAAOA,GAAE,YAAWA,GAAE,UAAU,GAAE,GAAE,MAAE;AAAE,QAAM,MAAM,2IAA2I;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,QAAMG,KAAER,GAAE;AAAE,MAAIS,KAAET,GAAE;AAAE,KAAE;AAAC,IAAAE,KAAEM,GAAEC,IAAG,GAAEN,OAAI,MAAID,OAAIG,IAAEA,MAAG;AAAA,EAAC,SAAOA,KAAE,MAAI,MAAIH;AAAG,OAAIG,KAAE,OAAKD,OAAI,MAAIF,OAAI,IAAGG,KAAE,GAAEA,KAAE,MAAI,MAAIH,IAAEG,MAAG;AAAE,IAAAH,KAAEM,GAAEC,IAAG,GAAEL,OAAI,MAAIF,OAAIG;AAAE,MAAG,GAAGL,IAAES,EAAC,GAAEP,KAAE;AAAI,WAAOD,GAAEE,OAAI,GAAEC,OAAI,CAAC;AAAE,QAAM,GAAG;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,MAAIC,KAAE,GAAEC,KAAEF,GAAE;AAAE,QAAMG,KAAED,KAAE,IAAGE,KAAEJ,GAAE;AAAE,SAAKE,KAAEC,MAAG;AAAC,UAAMA,KAAEC,GAAEF,IAAG;AAAE,QAAGD,MAAGE,IAAE,MAAI,MAAIA;AAAG,aAAO,GAAGH,IAAEE,EAAC,GAAE,CAAC,EAAE,MAAID;AAAA,EAAE;AAAC,QAAM,GAAG;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,QAAMC,KAAED,GAAE;AAAE,MAAIE,KAAEF,GAAE,GAAEG,KAAEF,GAAEC,IAAG,GAAEE,KAAE,MAAID;AAAE,MAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,GAAE,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,IAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,IAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,MAAGD,MAAG,IAAG,MAAIA,MAAG,MAAIF,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG;AAAM,UAAM,GAAG;AAAE,SAAO,GAAGF,IAAEE,EAAC,GAAEE;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,SAAO,GAAGA,EAAC,MAAI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAED,GAAE;AAAE,QAAME,KAAEF,GAAE,GAAEG,KAAEF,GAAEC,EAAC,GAAEE,KAAEH,GAAEC,KAAE,CAAC,GAAEG,KAAEJ,GAAEC,KAAE,CAAC;AAAE,SAAOD,KAAEA,GAAEC,KAAE,CAAC,GAAE,GAAGF,IAAEA,GAAE,IAAE,CAAC,IAAGG,MAAG,IAAEC,MAAG,IAAEC,MAAG,KAAGJ,MAAG,QAAM;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,EAAC;AAAE,EAAAA,KAAE,KAAGC,MAAG,MAAI;AAAE,QAAMC,KAAED,OAAI,KAAG;AAAI,SAAOA,MAAG,SAAQ,OAAKC,KAAED,KAAE,MAAID,MAAG,IAAE,KAAG,KAAGE,KAAE,uBAAqBF,KAAEC,KAAED,KAAE,KAAK,IAAI,GAAEE,KAAE,GAAG,KAAGD,KAAE;AAAQ;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,GAAGA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE,EAAC,IAAGC,KAAE,MAAE,IAAE,CAAC,GAAE;AAAC,EAAAF,GAAE,KAAGE,IAAED,OAAIA,KAAE,GAAGA,EAAC,GAAED,GAAE,IAAEC,GAAE,QAAOD,GAAE,IAAEC,GAAE,GAAED,GAAE,IAAE,GAAEA,GAAE,IAAEA,GAAE,EAAE,QAAOA,GAAE,IAAEA,GAAE;AAAE;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,GAAE,IAAEC,IAAEA,KAAED,GAAE;AAAE,UAAM,GAAGA,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAGA,KAAE;AAAE,UAAM,MAAM,yCAAyCA,IAAG;AAAE,QAAMC,KAAEF,GAAE,GAAEG,KAAED,KAAED;AAAE,MAAGE,KAAEH,GAAE;AAAE,UAAM,GAAGC,IAAED,GAAE,IAAEE,EAAC;AAAE,SAAOF,GAAE,IAAEG,IAAED;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,MAAG,KAAGA;AAAE,WAAO,EAAE;AAAE,MAAIC,KAAE,GAAGF,IAAEC,EAAC;AAAE,SAAOD,GAAE,MAAIA,GAAE,IAAEE,KAAEF,GAAE,EAAE,SAASE,IAAEA,KAAED,EAAC,KAAGD,KAAEA,GAAE,GAAEE,KAAEA,QAAKD,KAAEC,KAAED,MAAG,IAAI,WAAW,CAAC,IAAE,KAAGD,GAAE,MAAME,IAAED,EAAC,IAAE,IAAI,WAAWD,GAAE,SAASE,IAAED,EAAC,CAAC,IAAG,KAAGC,GAAE,SAAO,EAAE,IAAE,IAAI,EAAEA,IAAE,CAAC;AAAC;AAAC,GAAG,UAAU,SAAO,QAAO,GAAG,UAAU,KAAG;AAAG,IAAI,KAAG,CAAC;AAAE,SAAS,GAAGF,IAAE;AAAC,MAAIC,KAAED,GAAE;AAAE,MAAGC,GAAE,KAAGA,GAAE;AAAE,WAAM;AAAG,EAAAD,GAAE,IAAEA,GAAE,EAAE;AAAE,MAAIE,KAAE,GAAGF,GAAE,CAAC;AAAE,MAAGC,KAAEC,OAAI,GAAE,GAAGA,MAAG,MAAI,KAAGA,MAAG;AAAG,UAAM,GAAGA,IAAEF,GAAE,CAAC;AAAE,MAAGC,KAAE;AAAE,UAAM,MAAM,yBAAyBA,mBAAkBD,GAAE,IAAI;AAAE,SAAOA,GAAE,IAAEC,IAAED,GAAE,IAAEE,IAAE;AAAE;AAAC,SAAS,GAAGF,IAAE;AAAC,UAAOA,GAAE,GAAE;AAAA,IAAC,KAAK;AAAE,WAAGA,GAAE,IAAE,GAAGA,EAAC,IAAE,GAAGA,GAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,SAAGA,KAAEA,GAAE,GAAEA,GAAE,IAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,UAAG,KAAGA,GAAE;AAAE,WAAGA,EAAC;AAAA,WAAM;AAAC,YAAIC,KAAE,GAAGD,GAAE,CAAC;AAAE,WAAGA,KAAEA,GAAE,GAAEA,GAAE,IAAEC,EAAC;AAAA,MAAC;AAAC;AAAA,IAAM,KAAK;AAAE,SAAGD,KAAEA,GAAE,GAAEA,GAAE,IAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,WAAIC,KAAED,GAAE,OAAI;AAAC,YAAG,CAAC,GAAGA,EAAC;AAAE,gBAAM,MAAM,uCAAuC;AAAE,YAAG,KAAGA,GAAE,GAAE;AAAC,cAAGA,GAAE,KAAGC;AAAE,kBAAM,MAAM,yBAAyB;AAAE;AAAA,QAAK;AAAC,WAAGD,EAAC;AAAA,MAAC;AAAC;AAAA,IAAM;AAAQ,YAAM,GAAGA,GAAE,GAAEA,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE,EAAE,GAAEI,KAAE,GAAGJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,EAAE,IAAEI;AAAE,MAAII,KAAEH,KAAEF;AAAE,MAAGK,MAAG,MAAIR,GAAE,EAAE,IAAEK,IAAEH,GAAED,IAAED,IAAE,QAAO,QAAO,MAAM,GAAEQ,KAAEH,KAAEL,GAAE,EAAE,IAAGQ;AAAE,UAAM,MAAM,wDAAwDJ,0BAAyBA,KAAEI,wFAAuF;AAAE,SAAOR,GAAE,EAAE,IAAEK,IAAEL,GAAE,EAAE,IAAEG,IAAEF;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIQ,KAAE,GAAGR,GAAE,CAAC,GAAES,KAAE,GAAGT,KAAEA,GAAE,GAAEQ,EAAC;AAAE,MAAGR,KAAEA,GAAE,GAAE,GAAE;AAAC,QAAIM,IAAEC,KAAEP;AAAE,KAACM,KAAE,OAAKA,KAAE,IAAE,IAAI,YAAY,SAAQ,EAAC,OAAM,KAAE,CAAC,IAAGE,KAAEC,KAAED,IAAED,KAAE,MAAIE,MAAGD,OAAID,GAAE,SAAOA,KAAEA,GAAE,SAASE,IAAED,EAAC;AAAE,QAAG;AAAC,UAAIE,KAAEJ,GAAE,OAAOC,EAAC;AAAA,IAAC,SAAOP,IAAN;AAAS,UAAG,WAAS,GAAE;AAAC,YAAG;AAAC,UAAAM,GAAE,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;AAAA,QAAC,SAAON,IAAN;AAAA,QAAS;AAAC,YAAG;AAAC,UAAAM,GAAE,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC,GAAE,IAAE;AAAA,QAAE,SAAON,IAAN;AAAS,cAAE;AAAA,QAAE;AAAA,MAAC;AAAC,YAAK,CAAC,MAAI,IAAE,SAAQA;AAAA,IAAC;AAAA,EAAC,OAAK;AAAC,IAAAQ,MAAGE,KAAED,MAAGD,IAAEC,KAAE,CAAC;AAAE,QAAIN,IAAEC,KAAE;AAAK,WAAKM,KAAEF,MAAG;AAAC,UAAI,IAAER,GAAEU,IAAG;AAAE,UAAE,MAAID,GAAE,KAAK,CAAC,IAAE,IAAE,MAAIC,MAAGF,KAAE,EAAE,KAAGL,KAAEH,GAAEU,IAAG,GAAE,IAAE,OAAK,QAAM,MAAIP,OAAIO,MAAI,EAAE,KAAGD,GAAE,MAAM,KAAG,MAAI,IAAE,KAAGN,EAAC,KAAG,IAAE,MAAIO,MAAGF,KAAE,IAAE,EAAE,KAAGL,KAAEH,GAAEU,IAAG,GAAE,QAAM,MAAIP,OAAI,QAAM,KAAGA,KAAE,OAAK,QAAM,KAAGA,MAAG,OAAK,QAAM,OAAKG,KAAEN,GAAEU,IAAG,OAAKA,MAAI,EAAE,KAAGD,GAAE,MAAM,KAAG,MAAI,MAAI,KAAGN,OAAI,IAAE,KAAGG,EAAC,KAAG,KAAG,MAAII,MAAGF,KAAE,IAAE,EAAE,KAAGL,KAAEH,GAAEU,IAAG,GAAE,QAAM,MAAIP,OAAIA,KAAE,OAAK,KAAG,OAAK,MAAI,KAAG,QAAM,OAAKG,KAAEN,GAAEU,IAAG,OAAK,QAAM,OAAKH,KAAEP,GAAEU,IAAG,OAAKA,MAAI,EAAE,MAAI,KAAG,IAAE,MAAI,MAAI,KAAGP,OAAI,MAAI,KAAGG,OAAI,IAAE,KAAGC,IAAE,KAAG,OAAME,GAAE,KAAK,SAAO,KAAG,KAAG,OAAM,SAAO,OAAK,EAAE,MAAI,EAAE,GAAEA,GAAE,UAAQ,SAAOL,KAAE,EAAEA,IAAEK,EAAC,GAAEA,GAAE,SAAO;AAAA,IAAE;AAAC,IAAAC,KAAE,EAAEN,IAAEK,EAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,GAAGV,IAAE;AAAC,QAAMC,KAAE,GAAGD,GAAE,CAAC;AAAE,SAAO,GAAGA,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,GAAGH,GAAE,CAAC;AAAE,OAAIG,KAAEH,GAAE,EAAE,IAAEG,IAAEH,GAAE,EAAE,IAAEG;AAAG,IAAAD,GAAE,KAAKD,GAAED,GAAE,CAAC,CAAC;AAAC;AAAC,IAAI,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAOA;AAAC;AAAC,IAAI;AAAG,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,EAAAD,GAAE,IAAEA,GAAE,EAAED,IAAEC,GAAE,GAAEA,GAAE,GAAEC,EAAC,IAAED,GAAE,EAAED,IAAEC,GAAE,GAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,SAAK,IAAE,GAAGD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMD,KAAE,CAAC;AAAG,QAAG;AAAC,aAAOA,OAAI,KAAG,KAAI,GAAG,IAAI;AAAA,IAAC,UAAC;AAAQ,MAAAA,OAAI,KAAG;AAAA,IAAO;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIA,KAAE;AAAG,WAAOA,GAAE,IAAEA,GAAE,EAAE,MAAKA,GAAE,GAAEA,GAAE,GAAE,IAAE,IAAEA,GAAE,EAAE,MAAKA,GAAE,GAAEA,GAAE,cAAa,IAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,KAAK;AAAE,WAAO,IAAI,KAAK,YAAY,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAE,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,EAAE,KAAG,IAAE,KAAK,EAAE,CAAC;AAAA,EAAG;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAED,GAAE;AAAE;AAAC,IAAAC,MAAGD,KAAE,GAAGC,EAAC,OAAKA;AAAE,QAAIM,KAAEP,GAAE;AAAO,QAAGO,IAAE;AAAC,UAAIL,KAAEF,GAAEO,KAAE,CAAC,GAAEJ,KAAE,GAAGD,EAAC;AAAE,MAAAC,KAAEI,OAAIL,KAAE;AAAO,UAAIE,KAAEJ;AAAE,UAAGG,IAAE;AAAC,WAAE;AAAC,cAAIE,IAAEG,KAAEN,IAAEO,KAAE;AAAG,cAAGD;AAAE,qBAAQR,MAAKQ;AAAE,oBAAM,CAACR,EAAC,KAAGK,YAAI,CAAC,IAAGL,EAAC,IAAEQ,GAAER,EAAC,KAAGG,KAAEK,GAAER,EAAC,GAAE,MAAM,QAAQG,EAAC,MAAI,GAAGA,EAAC,KAAG,GAAGA,EAAC,KAAG,MAAIA,GAAE,UAAQA,KAAE,OAAM,QAAMA,OAAIM,KAAE,OAAI,QAAMN,QAAKE,YAAI,CAAC,IAAGL,EAAC,IAAEG;AAAI,cAAGM,OAAIJ,KAAEG,KAAGH;AAAE,qBAAQL,MAAKK,IAAE;AAAC,cAAAI,KAAEJ;AAAE,oBAAM;AAAA,YAAC;AAAC,UAAAI,KAAE;AAAA,QAAI;AAAC,QAAAD,KAAE,QAAMC,KAAE,QAAMP,KAAEO,OAAIP;AAAA,MAAC;AAAC,aAAKK,KAAE,MAAI,SAAOF,KAAED,GAAEG,KAAE,CAAC,MAAI,GAAGF,EAAC,KAAG,GAAGA,EAAC,KAAG,MAAIA,GAAE,OAAME;AAAI,YAAID,KAAE;AAAG,OAACF,OAAIJ,MAAGQ,MAAGF,QAAKL,MAAGK,MAAGE,MAAGC,QAAKL,GAAE,SAAOG,MAAGH,KAAE,MAAM,UAAU,MAAM,KAAKA,IAAE,GAAEG,EAAC,GAAEE,MAAGL,GAAE,KAAKK,EAAC,IAAGH,KAAEF;AAAA,IAAC;AAAM,MAAAE,KAAEN;AAAA,EAAC;AAAC,SAAOM;AAAC;AAAC,SAAS,GAAGN,IAAE;AAAC,SAAOA,KAAE,QAAQ,KAAKA,EAAC,KAAG,GAAGA,EAAC,GAAE,IAAI,GAAG,IAAG,EAAE,KAAG,OAAK,YAAK,IAAI,GAAG,GAAE,CAAC;AAAC;AAAC,GAAG,UAAU,IAAE,IAAG,GAAG,UAAU,WAAS,WAAU;AAAC,MAAG;AAAC,WAAO,KAAG,IAAG,GAAG,IAAI,EAAE,SAAS;AAAA,EAAC,UAAC;AAAQ,SAAG;AAAA,EAAM;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,IAAED,OAAI,GAAE,KAAK,IAAEC,OAAI;AAAA,EAAC;AAAC;AAAE,IAAI;AAAG,SAAS,GAAGD,IAAE;AAAC,SAAOA,KAAE,UAAU,KAAKA,EAAC,KAAG,GAAGA,EAAC,GAAE,IAAI,GAAG,IAAG,EAAE,KAAG,OAAK,YAAK,IAAI,GAAG,GAAE,CAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,IAAED,OAAI,GAAE,KAAK,IAAEC,OAAI;AAAA,EAAC;AAAC;AAAE,IAAI;AAAG,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,SAAKA,KAAE,KAAGD,KAAE;AAAK,IAAAD,GAAE,EAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,MAAGA,OAAI,IAAEC,MAAG,QAAM,GAAEA,QAAK;AAAE,EAAAF,GAAE,EAAE,KAAKC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAKA,KAAE;AAAK,IAAAD,GAAE,EAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,QAAK;AAAE,EAAAD,GAAE,EAAE,KAAKC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAGA,MAAG;AAAE,OAAGD,IAAEC,EAAC;AAAA,OAAM;AAAC,aAAQC,KAAE,GAAEA,KAAE,GAAEA;AAAI,MAAAF,GAAE,EAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,OAAI;AAAE,IAAAD,GAAE,EAAE,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,EAAAD,GAAE,EAAE,KAAKC,OAAI,IAAE,GAAG,GAAED,GAAE,EAAE,KAAKC,OAAI,IAAE,GAAG,GAAED,GAAE,EAAE,KAAKC,OAAI,KAAG,GAAG,GAAED,GAAE,EAAE,KAAKC,OAAI,KAAG,GAAG;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,QAAIA,GAAE,WAASD,GAAE,EAAE,KAAKC,EAAC,GAAED,GAAE,KAAGC,GAAE;AAAO;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,KAAGF,GAAE,GAAE,IAAEC,KAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,SAAO,GAAGD,IAAEC,IAAE,CAAC,GAAEA,KAAED,GAAE,EAAE,IAAI,GAAE,GAAGA,IAAEC,EAAC,GAAEA,GAAE,KAAKD,GAAE,CAAC,GAAEC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAIC,KAAED,GAAE,IAAI;AAAE,OAAIC,KAAEF,GAAE,IAAEA,GAAE,EAAE,OAAO,IAAEE,IAAEA,KAAE;AAAK,IAAAD,GAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,QAAK,GAAEF,GAAE;AAAI,EAAAC,GAAE,KAAKC,EAAC,GAAEF,GAAE;AAAG;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,KAAGF,IAAEC,IAAE,CAAC,GAAE,GAAGD,GAAE,GAAEE,GAAE,MAAM,GAAE,GAAGF,IAAEA,GAAE,EAAE,IAAI,CAAC,GAAE,GAAGA,IAAEE,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMD,OAAID,KAAE,GAAGD,IAAEC,EAAC,GAAEE,GAAED,IAAEF,EAAC,GAAE,GAAGA,IAAEC,EAAC;AAAE;AAAC,SAAS,KAAI;AAAC,QAAMD,KAAE,MAAK;AAAA,IAAC,cAAa;AAAC,YAAM,MAAM;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,OAAO,eAAeA,IAAEA,GAAE,SAAS,GAAEA;AAAC;AAAC,IAAI,KAAG,GAAG;AAAV,IAAY,KAAG,GAAG;AAAlB,IAAoB,KAAG,GAAG;AAA1B,IAA4B,KAAG,GAAG;AAAlC,IAAoC,KAAG,GAAG;AAA1C,IAA4C,KAAG,GAAG;AAAlD,IAAoD,KAAG,GAAG;AAA1D,IAA4D,KAAG,GAAG;AAAlE,IAAoE,KAAG,GAAG;AAA1E,IAA4E,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAEF,IAAE,KAAK,IAAEC,IAAED,KAAE,IAAG,KAAK,IAAE,CAAC,CAACA,MAAGE,OAAIF,MAAG;AAAA,EAAE;AAAC;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGD,IAAEC,IAAE,EAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,KAAGJ,IAAEE,IAAE,GAAGD,IAAEE,EAAC,GAAEC,EAAC;AAAC;AAAC,IAAM,KAAG,GAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAI,GAAGA,IAAE,GAAGC,IAAEE,IAAED,EAAC,GAAEE,EAAC,GAAE;AAAG,GAAG,EAAE;AAA5E,IAA8E,KAAG,GAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAI,GAAGA,IAAE,GAAGC,IAAEE,IAAED,IAAE,IAAE,GAAEE,EAAC,GAAE;AAAG,GAAG,EAAE;AAAE,IAAI,KAAG,OAAO;AAAd,IAAgB,KAAG,OAAO;AAA1B,IAA4B,KAAG,OAAO;AAAtC,IAAwC,KAAG,OAAO;AAAE,IAAI;AAAJ,IAAO;AAAG,SAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAED,GAAEH,EAAC;AAAE,MAAGI;AAAE,WAAOA;AAAE,GAACA,KAAE,CAAC,GAAG,KAAGD,IAAEC,GAAE,IAAE,SAASJ,IAAE;AAAC,YAAO,OAAOA,IAAE;AAAA,MAAC,KAAI;AAAU,eAAO,YAAK,CAAC,GAAE,QAAO,IAAE;AAAA,MAAE,KAAI;AAAS,eAAOA,KAAE,IAAE,SAAO,MAAIA,KAAE,YAAK,CAAC,GAAE,MAAM,KAAE,CAAC,CAACA,IAAE,MAAM;AAAA,MAAE,KAAI;AAAS,eAAM,CAAC,GAAEA,EAAC;AAAA,MAAE,KAAI;AAAS,eAAOA;AAAA,IAAC;AAAA,EAAC,EAAEG,GAAE,CAAC,CAAC;AAAE,MAAIE,KAAEF,GAAE,CAAC;AAAE,MAAIK,KAAE;AAAE,EAAAH,MAAGA,GAAE,gBAAc,WAASD,GAAE,KAAGC,IAAE,cAAY,QAAOA,KAAEF,GAAE,EAAEK,EAAC,OAAKJ,GAAE,KAAG,MAAG,YAAKC,KAAE,YAAKF,GAAEK,KAAE,CAAC,IAAEH,KAAEF,GAAEK,MAAG,CAAC;AAAI,QAAMC,KAAE,CAAC;AAAE,SAAKJ,MAAG,MAAM,QAAQA,EAAC,KAAGA,GAAE,UAAQ,YAAU,OAAOA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,KAAG;AAAC,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,MAAAG,GAAEJ,GAAEC,EAAC,CAAC,IAAED;AAAE,IAAAA,KAAEF,GAAE,EAAEK,EAAC;AAAA,EAAC;AAAC,OAAIF,KAAE,GAAE,WAASD,MAAG;AAAC,QAAIL;AAAE,gBAAU,OAAOK,OAAIC,MAAGD,IAAEA,KAAEF,GAAE,EAAEK,EAAC;AAAG,QAAID,KAAE;AAAO,QAAGF,cAAa,KAAGL,KAAEK,MAAGL,KAAE,IAAGQ,OAAKR,MAAA,gBAAAA,GAAG,GAAE;AAAC,MAAAK,KAAEF,GAAE,EAAEK,EAAC,GAAED,KAAEJ;AAAE,UAAIO,KAAEF;AAAE,oBAAY,OAAOH,OAAIA,KAAEA,GAAE,GAAEE,GAAEG,EAAC,IAAEL,KAAGE,KAAEF;AAAA,IAAC;AAAC,SAAIK,KAAEJ,KAAE,GAAE,YAAU,QAAOD,KAAEF,GAAE,EAAEK,EAAC,MAAIH,KAAE,MAAIK,MAAGL,IAAEA,KAAEF,GAAE,EAAEK,EAAC,IAAGF,KAAEI,IAAEJ,MAAI;AAAC,YAAMH,KAAEM,GAAEH,EAAC;AAAE,MAAAC,KAAEL,GAAEE,IAAEE,IAAEN,IAAEO,IAAEJ,EAAC,IAAEF,GAAEG,IAAEE,IAAEN,IAAEG,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOA,GAAEH,EAAC,IAAEI;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,SAAO,MAAM,QAAQA,EAAC,IAAEA,GAAE,CAAC,aAAY,KAAGA,KAAE,CAAC,IAAGA,EAAC,IAAE,CAACA,IAAE,MAAM;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAOD,cAAa,KAAGA,GAAE,IAAE,MAAM,QAAQA,EAAC,IAAE,GAAGA,IAAEC,IAAE,KAAE,IAAE;AAAM;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE;AAAE,EAAAF,GAAEC,EAAC,IAAEE,KAAE,CAACH,IAAEC,IAAEC,OAAIE,GAAEJ,IAAEC,IAAEC,IAAEC,EAAC,IAAEC;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE;AAAE,MAAIM,IAAEC;AAAE,EAAAT,GAAEC,EAAC,IAAE,CAACD,IAAEC,IAAEC,OAAIG,GAAEL,IAAEC,IAAEC,IAAEO,YAAI,GAAG,IAAG,IAAG,IAAGN,EAAC,EAAE,IAAEK,YAAI,GAAGL,EAAC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,MAAIC,KAAED,GAAE,EAAE;AAAE,MAAG,QAAMC;AAAE,WAAOA;AAAE,QAAMC,KAAE,GAAG,IAAG,IAAG,IAAGF,EAAC;AAAE,SAAOC,KAAEC,GAAE,KAAG,CAACF,IAAEC,OAAI,GAAGD,IAAEC,IAAEC,EAAC,IAAE,CAACF,IAAEC,OAAI;AAAC,UAAME,KAAE,IAAEH,GAAE,CAAC;AAAE,WAAK,GAAGC,EAAC,KAAG,KAAGA,GAAE,KAAG;AAAC,UAAIG,KAAEH,GAAE,GAAEI,KAAEH,GAAEE,EAAC;AAAE,UAAG,QAAMC,IAAE;AAAC,YAAIG,KAAEN,GAAE;AAAG,QAAAM,OAAIA,KAAEA,GAAEJ,EAAC,OAAK,SAAOI,KAAE,GAAGA,EAAC,OAAKH,KAAEH,GAAEE,EAAC,IAAEI;AAAA,MAAG;AAAC,cAAMH,MAAGA,GAAEJ,IAAED,IAAEI,EAAC,MAAIA,MAAGC,KAAEJ,IAAG,GAAE,GAAGI,EAAC,GAAEA,GAAE,KAAGA,KAAE,UAAQG,KAAEH,GAAE,EAAE,IAAED,IAAEC,GAAE,EAAE,IAAED,IAAEC,KAAE,GAAGA,GAAE,GAAEG,EAAC,IAAGJ,KAAEJ,IAAEK,QAAKG,KAAEJ,GAAE,CAAC,KAAGI,GAAE,KAAKH,EAAC,IAAED,GAAE,CAAC,IAAE,CAACC,EAAC;AAAA,IAAG;AAAC,WAAO,QAAMF,MAAG,GAAGH,EAAC,GAAE;AAAA,EAAE,GAAEA,GAAE,EAAE,IAAEC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,QAAMC,MAAGD,KAAE,GAAGA,EAAC,GAAG,CAAC,EAAE;AAAE,MAAGA,KAAEA,GAAE,CAAC,GAAE;AAAC,UAAME,KAAE,GAAGF,EAAC,GAAEG,KAAE,GAAG,IAAG,IAAG,IAAGH,EAAC,EAAE;AAAE,WAAM,CAACA,IAAEI,IAAEC,OAAIJ,GAAED,IAAEI,IAAEC,IAAEF,IAAED,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAEC,EAAC,IAAEC,GAAE;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,QAAMG,KAAEN,GAAE;AAAE,EAAAF,GAAEC,EAAC,IAAE,CAACD,IAAEC,IAAEC,OAAIM,GAAER,IAAEC,IAAEC,IAAEG,YAAI,GAAG,IAAG,IAAG,IAAGF,EAAC,EAAE,IAAEC,YAAI,GAAGD,EAAC,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAE;AAAC,MAAIC,KAAED,GAAE,EAAE;AAAE,MAAG,CAACC,IAAE;AAAC,UAAMC,KAAE,GAAG,IAAG,IAAG,IAAGF,EAAC;AAAE,IAAAC,KAAE,CAACD,IAAEC,OAAI,GAAGD,IAAEC,IAAEC,EAAC,GAAEF,GAAE,EAAE,IAAEC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAQC,KAAE,IAAEH,GAAE,CAAC,GAAEI,KAAE,MAAID,KAAE,IAAE,IAAGE,KAAEL,GAAE,QAAOQ,KAAE,MAAIL,KAAE,IAAE,GAAEM,KAAEJ,MAAG,MAAIF,KAAE,KAAG,IAAGK,KAAEC,IAAED,MAAI;AAAC,UAAML,KAAEH,GAAEQ,EAAC;AAAE,QAAG,QAAML;AAAE;AAAS,UAAME,KAAEG,KAAEJ,IAAEK,KAAE,GAAGP,IAAEG,EAAC;AAAE,IAAAI,MAAGA,GAAER,IAAEE,IAAEE,EAAC;AAAA,EAAC;AAAC,MAAG,MAAIF,IAAE;AAAC,IAAAA,KAAEH,GAAEK,KAAE,CAAC;AAAE,eAAUL,MAAKG;AAAE,MAAAC,KAAE,CAACJ,IAAE,OAAO,MAAMI,EAAC,KAAG,SAAOC,KAAEF,GAAEC,EAAC,OAAKK,KAAE,GAAGP,IAAEE,EAAC,MAAIK,GAAER,IAAEI,IAAED,EAAC;AAAA,EAAC;AAAC,MAAGJ,KAAE,GAAGA,EAAC;AAAE,SAAI,GAAGC,IAAEA,GAAE,EAAE,IAAI,CAAC,GAAEC,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,SAAGD,IAAE,EAAED,GAAEE,EAAC,CAAC,KAAG,IAAI,WAAW,CAAC,CAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAEC,EAAC;AAAE,MAAGC;AAAE,WAAOA;AAAE,OAAIA,KAAEF,GAAE,QAAME,KAAEA,GAAED,EAAC,IAAG;AAAC,QAAIE,MAAGD,KAAE,GAAGA,EAAC,GAAG,CAAC,EAAE;AAAE,QAAGA,KAAEA,GAAE,CAAC,GAAE;AAAC,YAAMD,KAAE,GAAGC,EAAC,GAAEE,KAAE,GAAG,IAAG,IAAG,IAAGF,EAAC,EAAE;AAAE,MAAAA,KAAEF,GAAE,KAAG,GAAGI,IAAEH,EAAC,IAAE,CAACD,IAAEE,IAAEG,OAAIF,GAAEH,IAAEE,IAAEG,IAAED,IAAEH,EAAC;AAAA,IAAC;AAAM,MAAAC,KAAEC;AAAE,WAAOH,GAAEC,EAAC,IAAEC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,MAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,QAAIC,KAAE,IAAED,GAAE,CAAC;AAAE,QAAG,IAAEC;AAAE,aAAOD;AAAE,aAAQE,KAAE,GAAEC,KAAE,GAAED,KAAEF,GAAE,QAAOE,MAAI;AAAC,YAAMD,KAAEF,GAAEC,GAAEE,EAAC,CAAC;AAAE,cAAMD,OAAID,GAAEG,IAAG,IAAEF;AAAA,IAAE;AAAC,WAAOE,KAAED,OAAIF,GAAE,SAAOG,KAAG,GAAGH,IAAE,UAAQ,IAAEC,GAAE,GAAE,IAAEA,MAAG,OAAO,OAAOD,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,KAAGF,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,GAAI,SAASF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAIA,KAAE,GAAGA,IAAE,GAAG,CAAC,QAAO,MAAM,GAAEG,IAAE,IAAE,GAAEC,EAAC,GAAE,GAAGD,KAAE,IAAEF,GAAE,CAAC,CAAC,IAAGG,KAAE,GAAGH,IAAEE,IAAED,EAAC,cAAa,KAAG,MAAI,IAAEE,GAAE,OAAKA,KAAEA,GAAE,EAAE,GAAG,KAAKJ,EAAC,GAAE,GAAGC,IAAEE,IAAED,IAAEE,EAAC,KAAGA,GAAE,GAAGJ,EAAC,IAAE,MAAM,QAAQI,EAAC,KAAG,KAAG,IAAEA,GAAE,CAAC,MAAI,GAAGH,IAAEE,IAAED,IAAEE,KAAE,GAAGA,EAAC,CAAC,GAAEA,GAAE,KAAKJ,EAAC,KAAG,GAAGC,IAAEE,IAAED,IAAE,CAACF,EAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAGH,cAAa;AAAG,IAAAA,GAAE,QAAS,CAACA,IAAEI,OAAI;AAAC,SAAGL,IAAEE,IAAE,GAAG,CAACG,IAAEJ,EAAC,GAAEE,IAAE,KAAE,GAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,WAAU,MAAM,QAAQH,EAAC;AAAE,aAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAI;AAAC,YAAMG,KAAEP,GAAEI,EAAC;AAAE,YAAM,QAAQG,EAAC,KAAG,GAAGR,IAAEE,IAAE,GAAGM,IAAEL,IAAE,KAAE,GAAEC,EAAC;AAAA,IAAC;AAAC,CAAE;AAAE,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,MAAGD,KAAE,SAASD,IAAE;AAAC,QAAG,QAAMA;AAAE,aAAOA;AAAE,UAAMC,KAAE,OAAOD;AAAE,QAAG,aAAWC;AAAE,aAAO,OAAO,GAAG,IAAGD,EAAC,CAAC;AAAE,QAAG,GAAGA,EAAC,GAAE;AAAC,UAAG,aAAWC;AAAE,eAAO,GAAGD,EAAC;AAAE,UAAG,aAAWC;AAAE,eAAO,GAAGD,EAAC;AAAA,IAAC;AAAA,EAAC,EAAEC,EAAC,GAAE,QAAMA,IAAE;AAAC,QAAG,YAAU,OAAOA;AAAE,SAAGA,EAAC;AAAE,QAAG,QAAMA;AAAE,cAAO,GAAGD,IAAEE,IAAE,CAAC,GAAE,OAAOD,IAAE;AAAA,QAAC,KAAI;AAAS,UAAAD,KAAEA,GAAE,GAAE,GAAGC,EAAC,GAAE,GAAGD,IAAE,IAAG,EAAE;AAAE;AAAA,QAAM,KAAI;AAAS,UAAAE,KAAE,OAAO,QAAQ,IAAGD,EAAC,GAAEC,KAAE,IAAI,GAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,GAAE,OAAOA,MAAG,OAAO,EAAE,CAAC,CAAC,GAAE,GAAGF,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAE;AAAA,QAAM;AAAQ,UAAAA,KAAE,GAAGD,EAAC,GAAE,GAAGD,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAA,MAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,QAAMA,OAAI,GAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAEC,EAAC;AAAE;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGD,IAAEE,IAAE,CAAC,GAAEF,GAAE,EAAE,EAAE,KAAKC,KAAE,IAAE,CAAC;AAAE;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,GAAGD,IAAEE,IAAE,EAAED,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,KAAGJ,IAAEE,IAAE,GAAGD,IAAEE,EAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,QAAMA,MAAG,YAAU,OAAOA,MAAG,EAAEA,EAAC,KAAGA,cAAa,IAAEA,KAAE,WAAS,GAAGD,IAAEE,IAAE,GAAGD,EAAC,EAAE,MAAM;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,OAAG,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG;AAAC,IAAI,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAIF,GAAE;AAAE,WAAM;AAAG,MAAIG,KAAEH,GAAE;AAAE,EAAAA,KAAE,GAAGG,EAAC;AAAE,QAAMC,KAAE,GAAGD,EAAC;AAAE,EAAAA,KAAE,KAAGC,MAAG,MAAI;AAAE,QAAMC,KAAED,OAAI,KAAG;AAAK,SAAOJ,KAAE,cAAY,UAAQI,MAAGJ,IAAE,GAAGC,IAAEC,IAAE,QAAMG,KAAEL,KAAE,MAAIG,MAAG,IAAE,KAAG,KAAGE,KAAE,SAAOF,KAAEH,KAAEG,KAAE,KAAK,IAAI,GAAEE,KAAE,IAAI,KAAGL,KAAE,iBAAiB,GAAE;AAAE,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGD,IAAEE,IAAE,CAAC,GAAEF,KAAEA,GAAE,IAAGE,KAAE,YAAK,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC,IAAG,WAAW,GAAE,CAACD,IAAE,IAAE,GAAE,KAAGC,GAAE,UAAU,GAAE,IAAE,GAAE,KAAGA,GAAE,UAAU,GAAE,IAAE,GAAE,GAAGF,IAAE,EAAE,GAAE,GAAGA,IAAE,EAAE;AAAE,GAAG,GAAG,CAAC;AAApa,IAAsa,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGD,IAAEE,IAAE,CAAC,GAAEF,KAAEA,GAAE,GAAE,GAAGC,EAAC,GAAE,GAAGD,IAAE,EAAE;AAAE,GAAG,EAAE;AAA3iB,IAA6iB,KAAG,GAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC;AAAG,aAAQO,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,UAAIL,KAAEH,IAAEI,KAAEF,IAAEG,KAAEJ,GAAEO,EAAC;AAAE,cAAMH,OAAI,GAAGF,IAAEC,IAAE,CAAC,GAAED,KAAEA,GAAE,GAAE,GAAGE,EAAC,GAAE,GAAGF,IAAE,EAAE;AAAA,IAAE;AAAC,GAAG,EAAE;AAA3rB,IAA6rB,KAAG,GAAG,IAAI,SAASH,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,OAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAE,IAAEC,GAAE,MAAM;AAAE,aAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,MAAAD,KAAEF,GAAE,GAAE,GAAGC,GAAEE,EAAC,CAAC,GAAE,GAAGD,IAAE,EAAE;AAAA,EAAC;AAAC,GAAG,EAAE;AAA70B,IAA+0B,KAAG,GAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,GAAE,EAAE,CAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAAr5B,IAAu5B,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,OAAKF,KAAE,GAAGA,GAAE,GAAE,EAAE,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAA9+B,IAAg/B,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,GAAE,EAAE,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAGA,EAAC,IAAG;AAAC,QAAG,YAAU,OAAOA;AAAE,SAAGA,EAAC;AAAE,QAAG,QAAMA;AAAE,cAAO,GAAGD,IAAEE,IAAE,CAAC,GAAE,OAAOD,IAAE;AAAA,QAAC,KAAI;AAAS,UAAAD,KAAEA,GAAE,GAAE,GAAGC,EAAC,GAAE,GAAGD,IAAE,IAAG,EAAE;AAAE;AAAA,QAAM,KAAI;AAAS,UAAAE,KAAE,OAAO,QAAQ,IAAGD,EAAC,GAAEC,KAAE,IAAI,GAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,GAAE,OAAOA,MAAG,OAAO,EAAE,CAAC,CAAC,GAAE,GAAGF,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAE;AAAA,QAAM;AAAQ,UAAAA,KAAE,GAAGD,EAAC,GAAE,GAAGD,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAA,MAAC;AAAA,EAAC;AAAC,GAAG,GAAG,CAAC;AAAl2C,IAAo2C,KAAG,GAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAAv6C,IAAy6C,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,OAAG,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,IAAAC,KAAE,GAAGF,IAAEE,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,SAAGF,GAAE,GAAEC,GAAEC,EAAC,CAAC;AAAE,OAAGF,IAAEE,EAAC;AAAA,EAAC;AAAC,GAAG,EAAE;AAA3oD,IAA6oD,KAAG,GAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,OAAKF,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAAjuD,IAAmuD,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAAtyD,IAAwyD,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,WAAMF,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAA73D,IAA+3D,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAIA,KAAE,GAAGA,EAAC,GAAE,GAAGC,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,KAAE,EAAE,KAAKF,EAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC;AAAG,aAAQO,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,UAAIL,KAAEH,IAAEI,KAAEF,IAAEG,KAAEJ,GAAEO,EAAC;AAAE,cAAMH,MAAG,GAAGF,IAAEC,IAAE,EAAEC,EAAC,CAAC;AAAA,IAAC;AAAC,GAAG,EAAE;AAAhkE,IAAkkE,KAAG,GAAI,SAASL,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,QAAMF,KAAE,GAAGA,EAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAArpE,IAAupE,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,EAAC,CAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAAxtE,IAA0tE,KAAG,SAASA,IAAEC,IAAEC,KAAE,IAAG;AAAC,SAAO,IAAI,GAAGF,IAAEC,IAAEC,EAAC;AAAC,EAAG,SAASF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAIG,KAAE,GAAG,QAAOA,IAAE,IAAE,GAAE,GAAGF,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,IAAE,EAAE,KAAKC,EAAC,GAAE,GAAGH,IAAEG,IAAEC,EAAC,GAAE;AAAG,GAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAM,QAAQH,EAAC;AAAE,aAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI;AAAI,SAAGL,IAAEC,GAAEI,EAAC,GAAEH,IAAEC,IAAEC,EAAC;AAAC,CAAE;AAA97E,IAAg8E,KAAG,GAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIL,GAAE,MAAI,GAAGC,IAAE,IAAEA,GAAE,CAAC,GAAEI,IAAEH,EAAC,GAAE,GAAGF,IAAEC,KAAE,GAAGA,IAAEE,IAAED,EAAC,GAAEE,EAAC,GAAE;AAAG,GAAG,EAAE;AAA3hF,IAA6hF,KAAG,GAAI,SAASJ,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,EAAC,CAAC,GAAE;AAAG,GAAG,IAAG,EAAE;AAA9lF,IAAgmF,KAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,OAAG,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC;AAAG,aAAQO,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,UAAIL,KAAEH,IAAEI,KAAEF,IAAEG,KAAEJ,GAAEO,EAAC;AAAE,cAAMH,OAAI,GAAGF,IAAEC,IAAE,CAAC,GAAE,GAAGD,GAAE,GAAEE,EAAC;AAAA,IAAE;AAAC,GAAG,EAAE;AAA30F,IAA60F,KAAG,GAAI,SAASL,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,OAAKF,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,QAAMA,OAAI,GAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAEC,EAAC;AAAE,GAAG,EAAE;AAAj+F,IAAm+F,KAAG,GAAI,SAASD,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAKA,KAAE,SAASA,IAAE,EAAE,GAAE,GAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAEC,EAAC;AAAE,GAAG,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,SAAK,IAAED,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAE,IAAG,KAAK,IAAE,IAAG,KAAK,eAAa;AAAA,EAAM;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAM,CAACC,IAAEC,OAAI;AAAC,QAAG,GAAG,QAAO;AAAC,YAAMH,KAAE,GAAG,IAAI;AAAE,MAAAA,GAAE,EAAEG,EAAC,GAAE,GAAGH,GAAE,GAAEE,IAAEC,EAAC,GAAED,KAAEF;AAAA,IAAC;AAAM,MAAAE,KAAE,IAAI,MAAK;AAAA,QAAC,YAAYF,IAAEC,IAAE;AAAC,cAAG,GAAG,QAAO;AAAC,kBAAMC,KAAE,GAAG,IAAI;AAAE,eAAGA,IAAEF,IAAEC,EAAC,GAAED,KAAEE;AAAA,UAAC;AAAM,YAAAF,KAAE,IAAI,MAAK;AAAA,cAAC,YAAYA,IAAEC,IAAE;AAAC,qBAAK,IAAE,MAAK,KAAK,IAAE,OAAG,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,GAAE,GAAG,MAAKD,IAAEC,EAAC;AAAA,cAAC;AAAA,cAAC,QAAO;AAAC,qBAAK,IAAE,MAAK,KAAK,IAAE,OAAG,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,GAAE,KAAK,KAAG;AAAA,cAAE;AAAA,YAAC,EAAED,IAAEC,EAAC;AAAE,eAAK,IAAED,IAAE,KAAK,IAAE,KAAK,EAAE,GAAE,KAAK,IAAE,KAAK,IAAE,IAAG,KAAK,EAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,EAAE,EAAC,IAAGD,KAAE,MAAE,IAAE,CAAC,GAAE;AAAC,eAAK,KAAGA;AAAA,QAAC;AAAA,MAAC,EAAEE,IAAEC,EAAC;AAAE,QAAG;AAAC,YAAMA,KAAE,IAAIH,MAAEK,KAAEF,GAAE;AAAE,SAAGF,EAAC,EAAEI,IAAEH,EAAC;AAAE,UAAIE,KAAED;AAAA,IAAC,UAAC;AAAQ,MAAAD,GAAE,EAAE,MAAM,GAAEA,GAAE,IAAE,IAAGA,GAAE,IAAE,IAAG,GAAG,SAAO,OAAK,GAAG,KAAKA,EAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,SAAO,WAAU;AAAC,UAAMC,KAAE,IAAI,MAAK;AAAA,MAAC,cAAa;AAAC,aAAK,IAAE,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,IAAE,IAAI,MAAK;AAAA,UAAC,cAAa;AAAC,iBAAK,IAAE,CAAC;AAAA,UAAC;AAAA,UAAC,SAAQ;AAAC,mBAAO,KAAK,EAAE;AAAA,UAAM;AAAA,UAAC,MAAK;AAAC,kBAAMD,KAAE,KAAK;AAAE,mBAAO,KAAK,IAAE,CAAC,GAAEA;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAE,OAAG,KAAK,GAAEC,IAAE,GAAG,IAAG,IAAG,IAAGD,EAAC,CAAC,GAAE,GAAGC,IAAEA,GAAE,EAAE,IAAI,CAAC;AAAE,UAAMC,KAAE,IAAI,WAAWD,GAAE,CAAC,GAAEE,KAAEF,GAAE,GAAEG,KAAED,GAAE;AAAO,QAAIE,KAAE;AAAE,aAAQL,KAAE,GAAEA,KAAEI,IAAEJ,MAAI;AAAC,YAAMC,KAAEE,GAAEH,EAAC;AAAE,MAAAE,GAAE,IAAID,IAAEI,EAAC,GAAEA,MAAGJ,GAAE;AAAA,IAAM;AAAC,WAAOA,GAAE,IAAE,CAACC,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,CAAC,GAAE,IAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,KAAGF,KAAE,GAAGA,EAAC,OAAK,EAAE,IAAE,SAAOA,EAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMD,IAAE;AAAC,QAAGA,cAAa,IAAG;AAAC,YAAME,KAAEF,GAAE;AAAG,aAAO,MAAKE,OAAIF,KAAEE,GAAEF,EAAC,GAAE,QAAMA,MAAG,GAAGD,IAAEE,IAAE,GAAGD,EAAC,EAAE,MAAM;AAAA,IAAG;AAAC,QAAG,MAAM,QAAQA,EAAC;AAAE;AAAA,EAAM;AAAC,KAAGD,IAAEC,IAAEC,EAAC;AAAC,GAAG,EAAE,CAAC;AAAE,IAAI;AAAJ,IAAO,KAAG,WAAW;AAAa,SAAS,GAAGF,IAAE;AAAC,aAAS,OAAK,KAAG,WAAU;AAAC,QAAIA,KAAE;AAAK,QAAG,CAAC;AAAG,aAAOA;AAAE,QAAG;AAAC,YAAMC,KAAE,CAAAD,OAAGA;AAAE,MAAAA,KAAE,GAAG,aAAa,aAAY,EAAC,YAAWC,IAAE,cAAaA,IAAE,iBAAgBA,GAAC,CAAC;AAAA,IAAC,SAAOD,IAAN;AAAA,IAAS;AAAC,WAAOA;AAAA,EAAC,EAAE;AAAG,MAAIC,KAAE;AAAG,SAAO,IAAI,MAAK;AAAA,IAAC,YAAYD,IAAE;AAAC,WAAK,IAAEA;AAAA,IAAC;AAAA,IAAC,WAAU;AAAC,aAAO,KAAK,IAAE;AAAA,IAAE;AAAA,EAAC,EAAEC,KAAEA,GAAE,gBAAgBD,EAAC,IAAEA,EAAC;AAAC;AAAC,SAAS,GAAGA,OAAKC,IAAE;AAAC,MAAG,MAAIA,GAAE;AAAO,WAAO,GAAGD,GAAE,CAAC,CAAC;AAAE,MAAIE,KAAEF,GAAE,CAAC;AAAE,WAAQG,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,IAAAD,MAAG,mBAAmBD,GAAEE,EAAC,CAAC,IAAEH,GAAEG,KAAE,CAAC;AAAE,SAAO,GAAGD,EAAC;AAAC;AAAC,IAAI,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAA9B,IAAgC,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5E,IAA8E,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,OAAG,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,IAAAC,KAAE,GAAGF,IAAEE,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,SAAGF,GAAE,GAAEC,GAAEC,EAAC,CAAC;AAAE,OAAGF,IAAEE,EAAC;AAAA,EAAC;AAAC,GAAG,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,EAAE;AAA5V,IAA8V,KAAG,CAAC,GAAE,IAAG,EAAE;AAAzW,IAA2W,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvZ,IAAyZ,KAAG,CAAC,CAAC;AAA9Z,IAAga,KAAG,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE;AAAnb,IAAqb,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,IAAE,CAAC;AAAA,EAAC;AAAC;AAAne,IAAqe,KAAG,CAAC;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,EAAE;AAAE,IAAI,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAE,GAAGC,EAAC,GAAE,EAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,MAAK,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAC;AAA/E,IAAiF,KAAG,CAAC,IAAG,CAAC,CAAC;AAA1F,IAA4F,KAAG,CAAC,GAAE,IAAG,GAAE,EAAE;AAAzG,IAA2G,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAE,IAAGC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,IAAGC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,IAAGC,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,MAAK,GAAE,MAAKA,EAAC;AAAA,EAAC;AAAC;AAAlF,IAAoF,KAAG,CAAC,MAAK,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,IAAG,CAAC,IAAG,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,EAAE,GAAE,GAAE,IAAG,CAAC,MAAK,IAAG,IAAG,CAAC,IAAG,CAAC,CAAC,GAAE,KAAI,EAAE,GAAE,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,CAAC,IAAG,CAAC,GAAE,EAAE,GAAE,KAAI,IAAG,EAAE,GAAE,IAAG,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,KAAI,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,KAAI,IAAG,IAAG,EAAE;AAAE,GAAG,UAAU,IAAE,GAAG,EAAE;AAAE,IAAI,KAAG,GAAG,IAAG,EAAE;AAAf,IAAiB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA7D,IAA+D,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAC;AAApI,IAAsI,KAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC;AAA9J,IAAgK,KAAG,GAAG,IAAG,EAAE;AAA3K,IAA6K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzN,IAA2N,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvQ,IAAyQ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAC;AAAvW,IAAyW,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,EAAE,CAAC;AAA3gB,IAA6gB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzjB,IAA2jB,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAA5nB,IAA8nB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1qB,IAA4qB,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAA7uB,IAA+uB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA3xB,IAA6xB,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA9yB,IAAgzB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,GAAE,IAAG,IAAG,EAAE,GAAE,EAAE,CAAC;AAA3H,IAA6H,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzK,IAA2K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,UAAMA,KAAE,GAAG,IAAI;AAAE,WAAO,QAAMA,KAAE,EAAE,IAAEA;AAAA,EAAC;AAAC;AAAlQ,IAAoQ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhT,IAAkT,KAAG,CAAC,GAAE,CAAC;AAAzT,IAA2T,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,EAAE,GAAE,EAAE,CAAC;AAAtZ,IAAwZ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAApc,IAAsc,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAA1d,IAA4d,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAxgB,IAA0gB,KAAG,CAAC,GAAE,IAAG,EAAE;AAArhB,IAAuhB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAnkB,IAAqkB,KAAG,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC;AAAllB,IAAolB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,QAAM,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,QAAM,GAAG,MAAK,CAAC;AAAA,EAAC;AAAC;AAAtrB,IAAwrB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,GAAG,MAAK,CAAC,CAAC,KAAG;AAAA,EAAE;AAAC;AAAlwB,IAAowB,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,CAAC;AAAtyB,IAAwyB,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,EAAE;AAA31B,IAA61B,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAz4B,IAA24B,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA55B,IAA85B,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,EAAE,CAAC;AAA3E,IAA6E,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzH,IAA2H,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvK,IAAyK,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,EAAE;AAA9L,IAAgM,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAA5N,IAA8N,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,OAAG,MAAK,CAAC;AAAA,EAAC;AAAC;AAAxF,IAA0F,KAAG,CAAC,GAAE,IAAG,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5I,IAA8I,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1L,IAA4L,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAxO,IAA0O,KAAG,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,EAAE;AAA/P,IAAiQ,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA/Q,IAAiR,KAAG,CAAC,GAAE,IAAG,EAAE;AAA5R,IAA8R,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA/S,IAAiT,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5I,IAA8I,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1L,IAA4L,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA7M,IAA+M,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,CAAC,GAAE,IAAG,EAAE;AAA3G,IAA6G,KAAG,GAAG,WAAU,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIA,KAAE,KAAK;AAAE,UAAMC,KAAE,IAAED,GAAE,CAAC;AAAE,UAAME,KAAE,IAAED;AAAE,WAAOD,KAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAIC,KAAE;AAAG,YAAMC,KAAE,IAAEH;AAAE,UAAII,KAAE;AAAG,UAAG,QAAMH,IAAE;AAAC,YAAGE;AAAE,iBAAO,GAAG;AAAE,QAAAF,KAAE,CAAC;AAAA,MAAC,WAASA,GAAE,gBAAc,IAAG;AAAC,YAAG,MAAI,IAAEA,GAAE,MAAIE;AAAE,iBAAOF;AAAE,QAAAA,KAAEA,GAAE,EAAE;AAAA,MAAC;AAAM,cAAM,QAAQA,EAAC,IAAEG,KAAE,CAAC,EAAE,KAAG,IAAEH,GAAE,CAAC,MAAIA,KAAE,CAAC;AAAE,UAAGE,IAAE;AAAC,YAAG,CAACF,GAAE;AAAO,iBAAO,GAAG;AAAE,QAAAG,OAAIA,KAAE,MAAG,GAAGH,EAAC;AAAA,MAAE;AAAM,QAAAG,OAAIA,KAAE,OAAGH,KAAE,GAAGA,EAAC;AAAG,aAAOG,OAAI,MAAI,IAAEH,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,MAAI,KAAGD,MAAG,GAAGC,IAAE,EAAE,IAAG,GAAGF,IAAEC,IAAE,GAAEE,KAAE,IAAI,GAAGD,IAAEC,IAAE,IAAG,MAAM,CAAC,GAAEA;AAAA,IAAC,EAAEH,IAAEC,IAAE,GAAGD,IAAEC,IAAE,CAAC,CAAC,GAAE,CAACC,MAAG,OAAKF,GAAE,KAAG,OAAIA;AAAA,EAAC;AAAC,CAAC;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,CAAC,MAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAOA,KAAEA,KAAEA,GAAE,MAAM,IAAE,IAAI,MAAG,WAASD,GAAE,qBAAmB,GAAGC,IAAE,GAAE,GAAGD,GAAE,kBAAkB,CAAC,IAAE,WAASA,GAAE,sBAAoB,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,aAAW,GAAGC,IAAE,GAAED,GAAE,UAAU,IAAE,gBAAeA,MAAG,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,iBAAe,GAAGC,IAAE,GAAED,GAAE,cAAc,IAAE,oBAAmBA,MAAG,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,oBAAkB,GAAGC,IAAE,GAAED,GAAE,iBAAiB,IAAE,uBAAsBA,MAAG,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,mBAAiB,GAAGC,IAAE,GAAED,GAAE,gBAAgB,IAAE,sBAAqBA,MAAG,GAAGC,IAAE,CAAC,GAAEA;AAAC;AAAC,SAAS,GAAGD,IAAEC,KAAE,IAAGC,KAAE,IAAG;AAAC,SAAM,EAAC,YAAWF,GAAE,IAAK,CAAAA,QAAI,EAAC,OAAM,GAAGA,IAAE,CAAC,KAAG,KAAG,IAAG,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,cAAa,GAAGA,IAAE,CAAC,KAAG,MAAI,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE,EAAG,GAAE,WAAUC,IAAE,UAASC,GAAC;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAt4xC,MAAAW,KAAA;AAAu4xC,MAAIV,KAAE,GAAGD,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEE,KAAE,GAAGF,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEG,KAAE,GAAGH,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEI,KAAE,GAAGJ,IAAE,GAAE,IAAG,GAAG,CAAC;AAAE,QAAMK,KAAE,EAAC,YAAW,CAAC,GAAE,WAAU,CAAC,EAAC;AAAE,WAAQL,KAAE,GAAEA,KAAEC,GAAE,QAAOD;AAAI,IAAAK,GAAE,WAAW,KAAK,EAAC,OAAMJ,GAAED,EAAC,GAAE,OAAME,GAAEF,EAAC,KAAG,IAAG,cAAaG,GAAEH,EAAC,KAAG,IAAG,aAAYI,GAAEJ,EAAC,KAAG,GAAE,CAAC;AAAE,OAAIC,MAAEU,MAAA,GAAGX,IAAE,IAAG,CAAC,MAAT,gBAAAW,IAAY,SAAON,GAAE,cAAY,EAAC,SAAQ,GAAGJ,IAAE,CAAC,KAAG,GAAE,SAAQ,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,QAAO,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,EAAC,KAAG,QAAGD,IAAE,IAAG,CAAC,MAAT,mBAAY,IAAI;AAAO,eAAUC,MAAK,GAAGD,IAAE,IAAG,CAAC,EAAE,EAAE;AAAE,MAAAK,GAAE,UAAU,KAAK,EAAC,GAAE,GAAGJ,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,CAAC;AAAE,SAAOI;AAAC;AAAC,SAAS,GAAGL,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAGF,IAAE,IAAG,CAAC;AAAE,IAAAC,GAAE,KAAK,EAAC,GAAE,GAAGC,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAGF,IAAE,IAAG,CAAC;AAAE,IAAAC,GAAE,KAAK,EAAC,GAAE,GAAGC,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,MAAM,KAAKA,IAAG,CAAAA,OAAGA,KAAE,MAAIA,KAAE,MAAIA,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,GAAE,WAASC,GAAE;AAAO,UAAM,MAAM,2EAA2ED,GAAE,cAAcC,GAAE,UAAU;AAAE,MAAIC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEL,GAAE,QAAOK;AAAI,IAAAH,MAAGF,GAAEK,EAAC,IAAEJ,GAAEI,EAAC,GAAEF,MAAGH,GAAEK,EAAC,IAAEL,GAAEK,EAAC,GAAED,MAAGH,GAAEI,EAAC,IAAEJ,GAAEI,EAAC;AAAE,MAAGF,MAAG,KAAGC,MAAG;AAAE,UAAM,MAAM,4DAA4D;AAAE,SAAOF,KAAE,KAAK,KAAKC,KAAEC,EAAC;AAAC;AAAC,IAAI;AAAG,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAG,IAAM,KAAG,IAAI,WAAW,CAAC,GAAE,IAAG,KAAI,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,KAAI,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,KAAI,IAAG,KAAI,IAAG,EAAE,CAAC;AAAE,eAAe,KAAI;AAAC,MAAG,WAAS;AAAG,QAAG;AAAC,YAAM,YAAY,YAAY,EAAE,GAAE,KAAG;AAAA,IAAE,QAAC;AAAM,WAAG;AAAA,IAAE;AAAC,SAAO;AAAE;AAAC,eAAe,GAAGJ,IAAEC,KAAE,MAAK;AAAC,QAAMC,KAAE,MAAM,GAAG,IAAE,kBAAgB;AAAuB,SAAM,EAAC,gBAAe,GAAGD,MAAKD,MAAKE,SAAO,gBAAe,GAAGD,MAAKD,MAAKE,UAAQ;AAAC;AAAC,IAAI,KAAG,MAAK;AAAC;AAAE,SAAS,KAAI;AAAC,MAAIF,KAAE;AAAU,SAAM,eAAa,OAAO,oBAAkB,CAAC,SAASA,KAAE,WAAU;AAAC,YAAOA,KAAEA,GAAE,WAAW,SAAS,QAAQ,KAAG,CAACA,GAAE,SAAS,QAAQ;AAAA,EAAC,EAAEA,EAAC,KAAG,CAAC,GAAGA,KAAEA,GAAE,UAAU,MAAM,0BAA0B,MAAIA,GAAE,UAAQ,KAAG,OAAOA,GAAE,CAAC,CAAC,KAAG;AAAI;AAAC,eAAe,GAAGA,IAAE;AAAC,MAAG,cAAY,OAAO,eAAc;AAAC,UAAMC,KAAE,SAAS,cAAc,QAAQ;AAAE,WAAOA,GAAE,MAAID,GAAE,SAAS,GAAEC,GAAE,cAAY,aAAY,IAAI,QAAS,CAACD,IAAEE,OAAI;AAAC,MAAAD,GAAE,iBAAiB,QAAQ,MAAI;AAAC,QAAAD,GAAE;AAAA,MAAC,GAAG,KAAE,GAAEC,GAAE,iBAAiB,SAAS,CAAAD,OAAG;AAAC,QAAAE,GAAEF,EAAC;AAAA,MAAC,GAAG,KAAE,GAAE,SAAS,KAAK,YAAYC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,gBAAcD,GAAE,SAAS,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,WAASA,GAAE,aAAW,CAACA,GAAE,YAAWA,GAAE,WAAW,IAAE,WAASA,GAAE,eAAa,CAACA,GAAE,cAAaA,GAAE,aAAa,IAAE,WAASA,GAAE,eAAa,CAACA,GAAE,cAAaA,GAAE,aAAa,IAAE,CAACA,GAAE,OAAMA,GAAE,MAAM;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAE,KAAG,QAAQ,MAAM,mHAAmH,GAAEE,GAAED,KAAED,GAAE,EAAE,gBAAgBC,EAAC,CAAC,GAAED,GAAE,EAAE,MAAMC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACF,GAAE,EAAE;AAAO,UAAM,MAAM,8BAA8B;AAAE,MAAGE,KAAEF,GAAE,EAAE,qBAAqBE,EAAC,IAAEF,GAAE,EAAE,qBAAqB,GAAE,EAAEE,KAAEF,GAAE,EAAE,OAAO,WAAW,QAAQ,KAAGA,GAAE,EAAE,OAAO,WAAW,OAAO;AAAG,UAAM,MAAM,0HAA0H;AAAE,EAAAA,GAAE,EAAE,uCAAqCE,GAAE,YAAYA,GAAE,qBAAoB,IAAE,GAAEA,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcD,EAAC,GAAED,GAAE,EAAE,uCAAqCE,GAAE,YAAYA,GAAE,qBAAoB,KAAE;AAAE,QAAK,CAACC,IAAEC,EAAC,IAAE,GAAGH,EAAC;AAAE,SAAM,CAACD,GAAE,KAAGG,OAAIH,GAAE,EAAE,OAAO,SAAOI,OAAIJ,GAAE,EAAE,OAAO,WAASA,GAAE,EAAE,OAAO,QAAMG,IAAEH,GAAE,EAAE,OAAO,SAAOI,KAAG,CAACD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAE,KAAG,QAAQ,MAAM,mHAAmH;AAAE,QAAMG,KAAE,IAAI,YAAYF,GAAE,MAAM;AAAE,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,IAAAC,GAAED,EAAC,IAAEF,GAAE,EAAE,gBAAgBC,GAAEC,EAAC,CAAC;AAAE,EAAAD,KAAED,GAAE,EAAE,QAAQ,IAAEG,GAAE,MAAM,GAAEH,GAAE,EAAE,QAAQ,IAAIG,IAAEF,MAAG,CAAC,GAAEC,GAAED,EAAC;AAAE,aAAUA,MAAKE;AAAE,IAAAH,GAAE,EAAE,MAAMC,EAAC;AAAE,EAAAD,GAAE,EAAE,MAAMC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAE,EAAE,kBAAgBA,GAAE,EAAE,mBAAiB,CAAC,GAAEA,GAAE,EAAE,gBAAgBC,EAAC,IAAEC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,EAAAH,GAAE,EAAE,kBAAgBA,GAAE,EAAE,mBAAiB,CAAC,GAAEA,GAAE,EAAE,gBAAgBC,EAAC,IAAE,CAACD,IAAEC,IAAEG,OAAI;AAAC,IAAAH,MAAGC,GAAEC,IAAEC,EAAC,GAAED,KAAE,CAAC,KAAGA,GAAE,KAAKH,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,iBAAe,SAASA,IAAE;AAAC,SAAO,GAAG,UAASA,EAAC;AAAC,GAAE,GAAG,eAAa,SAASA,IAAE;AAAC,SAAO,GAAG,QAAOA,EAAC;AAAC,GAAE,GAAG,4BAA0B,SAASA,IAAE;AAAC,SAAO,GAAG,sBAAqBA,EAAC;AAAC,GAAE,GAAG,gBAAc,SAASA,IAAE;AAAC,SAAO,GAAG,SAAQA,EAAC;AAAC,GAAE,GAAG,gBAAc,SAASA,IAAE;AAAC,SAAO,GAAG,SAAQA,EAAC;AAAC,GAAE,GAAG,kBAAgB,WAAU;AAAC,SAAO,GAAG;AAAC;AAAE,eAAe,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOH,KAAE,OAAM,OAAMA,IAAEC,IAAEC,IAAEC,IAAEC,OAAI;AAAC,QAAGH,MAAG,MAAM,GAAGA,EAAC,GAAE,CAAC,KAAK;AAAc,YAAM,MAAM,wBAAwB;AAAE,QAAGC,OAAI,MAAM,GAAGA,EAAC,GAAE,CAAC,KAAK;AAAe,YAAM,MAAM,wBAAwB;AAAE,WAAO,KAAK,UAAQE,QAAKH,KAAE,KAAK,QAAQ,aAAWG,GAAE,YAAWA,GAAE,wBAAsBH,GAAE,sBAAoBG,GAAE,uBAAsBA,KAAE,MAAM,KAAK,cAAc,KAAK,UAAQA,EAAC,GAAE,KAAK,gBAAc,KAAK,SAAO,QAAO,IAAIJ,GAAEI,IAAED,EAAC;AAAA,EAAC,GAAGH,IAAEE,GAAE,gBAAeA,GAAE,iBAAgBD,IAAE,EAAC,YAAW,CAAAD,OAAGA,GAAE,SAAS,OAAO,IAAEE,GAAE,eAAe,SAAS,IAAEA,GAAE,mBAAiBF,GAAE,SAAS,OAAO,IAAEE,GAAE,gBAAgB,SAAS,IAAEF,GAAC,CAAC,GAAE,MAAMA,GAAE,EAAEG,EAAC,GAAEH;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGF,GAAE,aAAY,IAAG,CAAC,KAAG,IAAI;AAAG,cAAU,OAAOC,MAAG,GAAGC,IAAE,GAAE,GAAGD,EAAC,CAAC,GAAE,GAAGC,IAAE,CAAC,KAAGD,cAAa,eAAa,GAAGC,IAAE,GAAE,GAAGD,IAAE,KAAE,CAAC,GAAE,GAAGC,IAAE,CAAC,IAAG,GAAGF,GAAE,aAAY,GAAE,GAAEE,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,MAAG;AAAC,UAAMC,KAAED,GAAE,EAAE;AAAO,QAAG,MAAIC;AAAE,YAAM,MAAMD,GAAE,EAAE,CAAC,EAAE,OAAO;AAAE,QAAGC,KAAE;AAAE,YAAM,MAAM,kCAAgCD,GAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,OAAQ,EAAE,KAAK,IAAI,CAAC;AAAA,EAAC,UAAC;AAAQ,IAAAA,GAAE,IAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,EAAAD,GAAE,IAAE,KAAK,IAAIA,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,EAAAD,GAAE,IAAE,IAAI,MAAG,GAAGA,GAAE,GAAE,uBAAuB,GAAE,GAAGA,GAAE,GAAE,aAAa,GAAE,GAAGA,GAAE,GAAE,wBAAwB,GAAE,GAAGC,IAAE,aAAa,GAAE,GAAGA,IAAED,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGD,GAAE,GAAEC,EAAC,GAAE,GAAGD,GAAE,GAAEC,KAAE,aAAa;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,EAAE,gBAAgB,MAAG,eAAcA,GAAE,CAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,IAAEA,IAAE,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,EAAE,sBAAsB,KAAE;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,KAAE,MAAG;AAA/x8C,QAAAU,KAAA,IAAAC,KAAA,IAAAC,KAAA;AAAgy8C,QAAGZ,IAAE;AAAC,YAAMA,KAAED,GAAE,eAAa,CAAC;AAAE,YAAGW,MAAAX,GAAE,gBAAF,gBAAAW,IAAe,uBAAkB,KAAAX,GAAE,gBAAF,mBAAe;AAAe,cAAM,MAAM,6EAA6E;AAAE,UAAG,IAAEY,MAAA,GAAG,KAAK,aAAY,IAAG,CAAC,MAAxB,gBAAAA,IAA2B,UAAK,QAAG,KAAK,aAAY,IAAG,CAAC,MAAxB,mBAA2B,UAAKC,MAAAb,GAAE,gBAAF,gBAAAa,IAAe,uBAAkB,KAAAb,GAAE,gBAAF,mBAAe;AAAgB,cAAM,MAAM,+EAA+E;AAAE,UAAG,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAE,GAAGF,GAAE,aAAY,IAAG,CAAC;AAAE,YAAG,CAACE,IAAE;AAAC,cAAIC,KAAED,KAAE,IAAI,MAAGE,KAAE,IAAI;AAAG,aAAGD,IAAE,GAAE,IAAGC,EAAC;AAAA,QAAC;AAAC,sBAAaH,OAAI,UAAQA,GAAE,YAAUA,KAAEC,IAAEC,KAAE,IAAI,MAAG,GAAGF,IAAE,GAAE,IAAGE,EAAC,MAAIF,KAAEC,IAAEC,KAAE,IAAI,MAAG,GAAGF,IAAE,GAAE,IAAGE,EAAC,KAAI,GAAGH,GAAE,aAAY,GAAE,GAAEE,EAAC;AAAA,MAAC,EAAE,MAAKD,EAAC,GAAEA,GAAE;AAAe,eAAO,MAAMA,GAAE,eAAe,SAAS,CAAC,EAAE,KAAM,CAAAD,OAAG;AAAC,cAAGA,GAAE;AAAG,mBAAOA,GAAE,YAAY;AAAE,gBAAM,MAAM,0BAA0BC,GAAE,mBAAmBD,GAAE,SAAS;AAAA,QAAC,CAAE,EAAE,KAAM,CAAAA,OAAG;AAAC,cAAG;AAAC,iBAAK,EAAE,EAAE,UAAU,YAAY;AAAA,UAAC,QAAC;AAAA,UAAM;AAAC,eAAK,EAAE,EAAE,kBAAkB,KAAI,aAAY,IAAI,WAAWA,EAAC,GAAE,MAAG,OAAG,KAAE,GAAE,GAAG,MAAK,YAAY,GAAE,KAAK,EAAE,GAAE,KAAK,EAAE;AAAA,QAAC,CAAE;AAAE,UAAGC,GAAE,4BAA4B;AAAW,WAAG,MAAKA,GAAE,gBAAgB;AAAA,eAAUA,GAAE;AAAiB,eAAO,eAAeD,IAAE;AAAC,gBAAMC,KAAE,CAAC;AAAE,mBAAQC,KAAE,OAAI;AAAC,kBAAK,EAAC,MAAKC,IAAE,OAAMC,GAAC,IAAE,MAAMJ,GAAE,KAAK;AAAE,gBAAGG;AAAE;AAAM,YAAAF,GAAE,KAAKG,EAAC,GAAEF,MAAGE,GAAE;AAAA,UAAM;AAAC,cAAG,MAAIH,GAAE;AAAO,mBAAO,IAAI,WAAW,CAAC;AAAE,cAAG,MAAIA,GAAE;AAAO,mBAAOA,GAAE,CAAC;AAAE,UAAAD,KAAE,IAAI,WAAWE,EAAC,GAAEA,KAAE;AAAE,qBAAUC,MAAKF;AAAE,YAAAD,GAAE,IAAIG,IAAED,EAAC,GAAEA,MAAGC,GAAE;AAAO,iBAAOH;AAAA,QAAC,EAAEC,GAAE,gBAAgB,EAAE,KAAM,CAAAD,OAAG;AAAC,aAAG,MAAKA,EAAC,GAAE,KAAK,EAAE,GAAE,KAAK,EAAE;AAAA,QAAC,CAAE;AAAA,IAAC;AAAC,WAAO,KAAK,EAAE,GAAE,KAAK,EAAE,GAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,QAAIA;AAAE,QAAG,KAAK,EAAE,GAAI,CAAAC,OAAG;AAAC,MAAAD,KAAE,GAAGC,EAAC;AAAA,IAAC,CAAE,GAAE,CAACD;AAAE,YAAM,MAAM,0CAA0C;AAAE,WAAOA;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,SAAK,EAAE,oBAAqB,CAACD,IAAEC,OAAI;AAAC,WAAK,EAAE,KAAK,MAAMA,EAAC,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,GAAG,GAAE,KAAK,EAAE,SAASD,IAAEC,EAAC,GAAE,KAAK,IAAE,QAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,EAAE,iBAAiB,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,IAAE,QAAO,KAAK,EAAE,WAAW;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAG,CAACD;AAAE,UAAM,MAAM,6CAA6CC,IAAG;AAAE,SAAOD;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,SAASC,IAAEC,IAAE;AAAC,EAAAD,KAAEA,GAAE,MAAM,GAAG;AAAE,MAAIE,IAAEC,KAAE;AAAE,EAAAH,GAAE,CAAC,KAAIG,MAAG,WAASA,GAAE,cAAYA,GAAE,WAAW,SAAOH,GAAE,CAAC,CAAC;AAAE,SAAKA,GAAE,WAASE,KAAEF,GAAE,MAAM;AAAI,IAAAA,GAAE,UAAQ,WAASC,KAAEE,KAAEA,GAAED,EAAC,KAAGC,GAAED,EAAC,MAAI,OAAO,UAAUA,EAAC,IAAEC,GAAED,EAAC,IAAEC,GAAED,EAAC,IAAE,CAAC,IAAEC,GAAED,EAAC,IAAED;AAAC,EAAE,cAAa,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAEH,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,EAAE,gBAAgB,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,kBAAkB,KAAK,CAAC,GAAE,KAAK,EAAE,aAAa,KAAK,CAAC,GAAE,KAAK,EAAE,aAAa,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE;AAAE,MAAGE,KAAE,GAAGC,GAAE,aAAaD,EAAC,GAAE,+BAA+B,GAAEC,GAAE,aAAaD,IAAED,EAAC,GAAEE,GAAE,cAAcD,EAAC,GAAE,CAACC,GAAE,mBAAmBD,IAAEC,GAAE,cAAc;AAAE,UAAM,MAAM,mCAAmCA,GAAE,iBAAiBD,EAAC,GAAG;AAAE,SAAOC,GAAE,aAAaH,GAAE,GAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE,GAAEG,KAAE,GAAGD,GAAE,kBAAkB,GAAE,+BAA+B;AAAE,EAAAA,GAAE,gBAAgBC,EAAC;AAAE,QAAMC,KAAE,GAAGF,GAAE,aAAa,GAAE,yBAAyB;AAAE,EAAAA,GAAE,WAAWA,GAAE,cAAaE,EAAC,GAAEF,GAAE,wBAAwBF,GAAE,CAAC,GAAEE,GAAE,oBAAoBF,GAAE,GAAE,GAAEE,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAa,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,EAAE,CAAC,GAAEA,GAAE,WAAW;AAAE,QAAMG,KAAE,GAAGH,GAAE,aAAa,GAAE,yBAAyB;AAAE,SAAOA,GAAE,WAAWA,GAAE,cAAaG,EAAC,GAAEH,GAAE,wBAAwBF,GAAE,CAAC,GAAEE,GAAE,oBAAoBF,GAAE,GAAE,GAAEE,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAaD,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEC,GAAE,WAAW,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,GAAEA,GAAE,gBAAgB,IAAI,GAAE,IAAI,GAAGA,IAAEC,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGL,IAAEC,IAAE;AAAC,MAAGD,GAAE,GAAE;AAAC,QAAGC,OAAID,GAAE;AAAE,YAAM,MAAM,2CAA2C;AAAA,EAAC;AAAM,IAAAA,GAAE,IAAEC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGH,IAAEC,EAAC,GAAED,GAAE,MAAIA,GAAE,EAAE,GAAEA,GAAE,EAAE,IAAGE,MAAGF,GAAE,MAAIA,GAAE,IAAE,GAAGA,IAAE,IAAE,IAAGE,KAAEF,GAAE,MAAIA,GAAE,MAAIA,GAAE,IAAE,GAAGA,IAAE,KAAE,IAAGE,KAAEF,GAAE,IAAGC,GAAE,WAAWD,GAAE,CAAC,GAAEE,GAAE,KAAK,GAAEF,GAAE,EAAE,GAAEA,KAAEG,GAAE,GAAED,GAAE,EAAE,gBAAgB,IAAI,GAAEF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGF,IAAEC,EAAC,GAAED,KAAE,GAAGC,GAAE,cAAc,GAAE,0BAA0B,GAAEA,GAAE,YAAYA,GAAE,YAAWD,EAAC,GAAEC,GAAE,cAAcA,GAAE,YAAWA,GAAE,gBAAeA,GAAE,aAAa,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,gBAAeA,GAAE,aAAa,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,oBAAmBC,MAAGD,GAAE,MAAM,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,oBAAmBC,MAAGD,GAAE,MAAM,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAED;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,KAAGF,IAAEC,EAAC,GAAED,GAAE,MAAIA,GAAE,IAAE,GAAGC,GAAE,kBAAkB,GAAE,8BAA8B,IAAGA,GAAE,gBAAgBA,GAAE,aAAYD,GAAE,CAAC,GAAEC,GAAE,qBAAqBA,GAAE,aAAYA,GAAE,mBAAkBA,GAAE,YAAWC,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAx5kD,MAAAW;AAAy5kD,GAAAA,MAAAX,GAAE,MAAF,gBAAAW,IAAK,gBAAgBX,GAAE,EAAE,aAAY;AAAK;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAmK;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,QAAG,KAAK,IAAE,GAAGA,GAAE,cAAc,GAAE,gCAAgC,GAAE,KAAK,KAAG,GAAG,MAAK,qKAAoKA,GAAE,aAAa,GAAE,KAAK,IAAE,GAAG,MAAK,KAAK,EAAE,GAAEA,GAAE,eAAe,GAAEA,GAAE,YAAY,KAAK,CAAC,GAAE,CAACA,GAAE,oBAAoB,KAAK,GAAEA,GAAE,WAAW;AAAE,YAAM,MAAM,iCAAiCA,GAAE,kBAAkB,KAAK,CAAC,GAAG;AAAE,SAAK,IAAEA,GAAE,kBAAkB,KAAK,GAAE,SAAS,GAAE,KAAK,IAAEA,GAAE,kBAAkB,KAAK,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAG,KAAK,GAAE;AAAC,YAAMA,KAAE,KAAK;AAAE,MAAAA,GAAE,cAAc,KAAK,CAAC,GAAEA,GAAE,aAAa,KAAK,EAAE,GAAEA,GAAE,aAAa,KAAK,CAAC;AAAA,IAAC;AAAC,SAAK,KAAG,KAAK,EAAE,kBAAkB,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,MAAM,GAAE,KAAK,KAAG,KAAK,EAAE,MAAM;AAAA,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAgd;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,IAAEA,GAAE,MAAM,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,IAAEA,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,SAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,mBAAmB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,qBAAqB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,aAAa,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAA1jC,IAA4jC,KAAG,cAAc,GAAE;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAmjB;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,EAAC,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,SAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,gBAAgB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,gBAAgB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,aAAa,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAE,aAAOD,GAAE,EAAE,KAAM,CAAAA,OAAGA,cAAa,UAAW;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAGA,cAAa,YAAa;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAG,eAAa,OAAO,gBAAcA,cAAa,YAAa;AAAA,IAAE;AAAQ,YAAM,MAAM,0BAA0BC,IAAG;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,QAAGA,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAAC,KAAE,IAAI,aAAaA,EAAC,EAAE,IAAK,CAAAD,OAAGA,KAAE,GAAI;AAAA,SAAM;AAAC,MAAAC,KAAE,IAAI,aAAaD,GAAE,QAAMA,GAAE,MAAM;AAAE,YAAMG,KAAE,GAAGH,EAAC;AAAE,UAAIE,KAAE,GAAGF,EAAC;AAAE,UAAG,GAAGE,IAAEC,IAAE,GAAGH,EAAC,CAAC,GAAE,kEAAkE,MAAM,GAAG,EAAE,SAAS,UAAU,QAAQ,KAAG,UAAU,UAAU,SAAS,KAAK,KAAG,cAAa,QAAM,gBAAe,KAAK,UAAS;AAAC,QAAAE,KAAE,IAAI,aAAaF,GAAE,QAAMA,GAAE,SAAO,CAAC,GAAEG,GAAE,WAAW,GAAE,GAAEH,GAAE,OAAMA,GAAE,QAAOG,GAAE,MAAKA,GAAE,OAAMD,EAAC;AAAE,iBAAQF,KAAE,GAAEG,KAAE,GAAEH,KAAEC,GAAE,QAAO,EAAED,IAAEG,MAAG;AAAE,UAAAF,GAAED,EAAC,IAAEE,GAAEC,EAAC;AAAA,MAAC;AAAM,QAAAA,GAAE,WAAW,GAAE,GAAEH,GAAE,OAAMA,GAAE,QAAOG,GAAE,KAAIA,GAAE,OAAMF,EAAC;AAAA,IAAC;AAAC,IAAAD,GAAE,EAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,UAAMC,KAAE,GAAGF,EAAC;AAAE,IAAAC,KAAE,GAAGD,EAAC;AAAE,UAAMG,KAAE,GAAGH,EAAC,GAAEI,KAAE,GAAGJ,EAAC;AAAE,IAAAE,GAAE,WAAWA,GAAE,YAAW,GAAEE,IAAEJ,GAAE,OAAMA,GAAE,QAAO,GAAEE,GAAE,KAAIA,GAAE,OAAMC,EAAC,GAAE,GAAGH,EAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,UAAM,MAAM,oGAAoG;AAAE,SAAOA,GAAE,MAAIA,GAAE,IAAE,GAAGA,GAAE,OAAO,WAAW,QAAQ,GAAE,yFAAyF,IAAGA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAGA,KAAE,GAAGA,EAAC,GAAE,CAAC;AAAG,QAAGA,GAAE,aAAa,wBAAwB,KAAGA,GAAE,aAAa,0BAA0B,KAAGA,GAAE,aAAa,iBAAiB;AAAE,WAAGA,GAAE;AAAA,SAAS;AAAC,UAAG,CAACA,GAAE,aAAa,6BAA6B;AAAE,cAAM,MAAM,iEAAiE;AAAE,WAAGA,GAAE;AAAA,IAAI;AAAC,SAAO;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,GAAGD,EAAC;AAAE,EAAAC,GAAE,SAAS,GAAE,GAAED,GAAE,OAAMA,GAAE,MAAM,GAAEC,GAAE,cAAcA,GAAE,QAAQ;AAAE,MAAIC,KAAE,GAAGF,IAAE,CAAC;AAAE,SAAOE,OAAIA,KAAE,GAAG,GAAGF,EAAC,GAAEC,IAAED,GAAE,IAAEC,GAAE,SAAOA,GAAE,OAAO,GAAED,GAAE,EAAE,KAAKE,EAAC,GAAEF,GAAE,IAAE,OAAIC,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,EAAAA,GAAE,EAAE,YAAYA,GAAE,EAAE,YAAW,IAAI;AAAC;AAAC,IAAI;AAAJ,IAAO,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEG,IAAE;AAAC,SAAK,IAAER,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,SAAOC,IAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAOG,IAAE,KAAK,MAAI,MAAI,EAAE,MAAI,QAAQ,MAAM,2FAA2F;AAAA,EAAE;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,YAAOP,KAAE,GAAGD,KAAE,MAAK,CAAC,OAAKC,KAAE,GAAGD,EAAC,GAAEC,KAAE,IAAI,WAAWA,GAAE,IAAK,CAAAD,OAAG,MAAIA,EAAE,CAAC,GAAEA,GAAE,EAAE,KAAKC,EAAC,IAAGA;AAAE,QAAID,IAAEC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMD,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,GAAE;AAAC,UAAIC;AAAE,UAAGD,cAAa;AAAW,QAAAC,KAAE,IAAI,WAAWD,EAAC;AAAA,eAAUA,cAAa;AAAa,QAAAC,KAAE,IAAI,aAAaD,EAAC;AAAA,WAAM;AAAC,YAAG,EAAEA,cAAa;AAAc,gBAAM,MAAM,0BAA0BA,IAAG;AAAE;AAAC,gBAAMD,KAAE,GAAG,IAAI,GAAEC,KAAE,GAAG,IAAI;AAAE,UAAAD,GAAE,cAAcA,GAAE,QAAQ,GAAEE,KAAE,GAAGD,IAAED,IAAE,KAAK,IAAEA,GAAE,SAAOA,GAAE,OAAO,GAAEA,GAAE,YAAYA,GAAE,YAAWE,EAAC;AAAE,gBAAMC,KAAE,GAAG,IAAI;AAAE,UAAAH,GAAE,WAAWA,GAAE,YAAW,GAAEG,IAAE,KAAK,OAAM,KAAK,QAAO,GAAEH,GAAE,KAAIA,GAAE,OAAM,IAAI,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAE,GAAGC,IAAED,IAAEE,EAAC,GAAE,GAAGD,IAAED,IAAE,OAAI,MAAI;AAAC,eAAG,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,GAAG,IAAI;AAAA,UAAC,CAAE,GAAE,GAAGC,EAAC,GAAE,GAAG,IAAI;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAD,GAAE,KAAKE,EAAC;AAAA,IAAC;AAAC,WAAO,IAAI,GAAGF,IAAE,KAAK,GAAE,KAAK,EAAE,GAAE,KAAK,QAAO,KAAK,GAAE,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,GAAG,IAAI,EAAE,cAAc,GAAG,MAAK,CAAC,CAAC,GAAE,KAAG;AAAA,EAAE;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,oBAAkB,GAAG,UAAU,GAAE,GAAG,UAAU,oBAAkB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,GAAE,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,gBAAc,GAAG,UAAU;AAAG,IAAI,KAAG;AAAI,IAAM,KAAG,EAAC,OAAM,SAAQ,WAAU,GAAE,QAAO,EAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAC,GAAG,IAAG,YAAWA,KAAEA,MAAG,CAAC,GAAG,OAAM,GAAGA,GAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAOD,cAAa,WAASA,GAAEC,EAAC,IAAED;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAO,KAAK,IAAI,KAAK,IAAID,IAAEC,EAAC,GAAE,KAAK,IAAI,KAAK,IAAID,IAAEC,EAAC,GAAEF,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,CAACA,GAAE;AAAE,UAAM,MAAM,oEAAoE;AAAE,SAAOA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,CAACA,GAAE;AAAE,UAAM,MAAM,kEAAkE;AAAE,SAAOA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,MAAGD,GAAE,EAAE;AAAE,IAAAC,GAAED,GAAE,EAAE,CAAC;AAAA,OAAM;AAAC,UAAME,KAAEF,GAAE,GAAG,IAAEA,GAAE,GAAG,IAAEA,GAAE,GAAG;AAAE,IAAAD,GAAE,IAAEA,GAAE,KAAG,IAAI;AAAG,UAAMI,KAAE,GAAGJ,EAAC;AAAE,IAAAE,IAAGF,KAAE,IAAI,GAAG,CAACG,EAAC,GAAEF,GAAE,GAAE,OAAGG,GAAE,QAAOJ,GAAE,GAAEC,GAAE,OAAMA,GAAE,MAAM,GAAG,EAAE,CAAC,GAAED,GAAE,MAAM;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,SAASJ,IAAE;AAAC,WAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAA,EAAC,EAAEA,EAAC,GAAEK,KAAE,GAAGL,EAAC,GAAEQ,KAAE,MAAM,QAAQN,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA;AAAE,KAAGE,IAAEC,IAAE,MAAI,MAAI;AAAC,KAAC,SAASL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAEJ,GAAE;AAAE,UAAGI,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWH,EAAC,GAAEG,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,GAAE,CAAC,GAAEI,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcF,EAAC,GAAEF,GAAE,KAAG,SAASA,IAAEC,IAAE;AAAC,YAAGD,OAAIC;AAAE,iBAAM;AAAG,QAAAD,KAAEA,GAAE,QAAQ,GAAEC,KAAEA,GAAE,QAAQ;AAAE,mBAAS,CAACE,IAAEC,EAAC,KAAIJ,IAAE;AAAC,UAAAA,KAAEG;AAAE,gBAAME,KAAED;AAAE,cAAIF,KAAED,GAAE,KAAK;AAAE,cAAGC,GAAE;AAAK,mBAAM;AAAG,gBAAK,CAACM,IAAEC,EAAC,IAAEP,GAAE;AAAM,cAAGA,KAAEO,IAAET,OAAIQ,MAAGH,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC;AAAE,mBAAM;AAAA,QAAE;AAAC,eAAM,CAAC,CAACD,GAAE,KAAK,EAAE;AAAA,MAAI,EAAED,GAAE,GAAEG,EAAC;AAAE,QAAAC,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,GAAE,CAAC;AAAA,WAAM;AAAC,QAAAA,GAAE,IAAEG;AAAE,cAAMF,KAAE,MAAM,IAAI,EAAE,KAAK,CAAC;AAAE,QAAAE,GAAE,QAAS,CAACH,IAAEE,OAAI;AAAC,cAAG,MAAIF,GAAE;AAAO,kBAAM,MAAM,kBAAkBE,iCAAgC;AAAE,UAAAD,GAAE,IAAEC,EAAC,IAAEF,GAAE,CAAC,GAAEC,GAAE,IAAEC,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEC,GAAE,IAAEC,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEC,GAAE,IAAEC,KAAE,CAAC,IAAEF,GAAE,CAAC;AAAA,QAAC,CAAE,GAAEI,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,GAAE,CAAC,GAAEI,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAK,KAAI,GAAE,GAAEA,GAAE,MAAKA,GAAE,eAAc,IAAI,WAAWH,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,EAAEG,IAAEH,IAAEO,IAAEL,EAAC,GAAEE,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC;AAAE,UAAML,KAAEI,GAAE;AAAE,IAAAJ,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGJ,EAAC,GAAEK,KAAE,SAASL,IAAE;AAAC,WAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAA,EAAC,EAAEA,EAAC,GAAEQ,KAAE,MAAM,QAAQN,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA,IAAEO,KAAE,MAAM,QAAQN,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA;AAAE,KAAGE,IAAED,IAAE,MAAI,MAAI;AAAC,QAAIJ,KAAEK,GAAE;AAAE,IAAAL,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAED,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWK,GAAE,CAAC,GAAEL,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcQ,EAAC,GAAER,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWK,GAAE,CAAC,GAAEL,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcS,EAAC,GAAEL,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,IAAGJ,KAAEK,GAAE,GAAG,cAAcL,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,IAAAD,cAAa,4BAA0BA,cAAa,qCAAmC,KAAK,IAAEA,IAAE,KAAK,IAAEC,MAAG,KAAK,IAAED;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,QAAGD,IAAE;AAAC,UAAIE,KAAE,GAAG,IAAI;AAAE,MAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK;AAAE,UAAIC,KAAED,GAAE,QAAOE,KAAE;AAAE,iBAAUC,MAAKL;AAAE,QAAAE,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAEH,GAAE,cAAY,GAAGD,GAAE,OAAM,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAEH,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,IAAGL,KAAE,IAAI,UAAQ,IAAIK,GAAE,IAAEF,GAAE,OAAME,GAAE,IAAEF,GAAE,QAAO,GAAGF,GAAE,QAAO,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAE,GAAE,IAAE,KAAK,EAAE,GAAEH,GAAE,KAAKF,EAAC,GAAEE,GAAE,OAAOF,EAAC,GAAE,EAAEI;AAAE,MAAAF,GAAE,QAAQ;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGF,IAAEC,IAAEC,IAAE;AAAC,QAAGF,MAAGC,IAAE;AAAC,UAAIE,KAAE,GAAG,IAAI;AAAE,MAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK;AAAE,UAAIC,KAAED,GAAE,QAAOE,KAAE;AAAE,iBAAUG,MAAKP,IAAE;AAAC,QAAAE,GAAE,UAAU,GAAEF,KAAED,GAAEQ,GAAE,KAAK;AAAE,cAAMC,KAAET,GAAEQ,GAAE,GAAG;AAAE,QAAAP,MAAGQ,OAAIN,GAAE,cAAY,GAAGD,GAAE,OAAM,EAAC,OAAMG,IAAE,MAAKJ,IAAE,IAAGQ,GAAC,CAAC,GAAEN,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKJ,IAAE,IAAGQ,GAAC,CAAC,GAAEN,GAAE,OAAOF,GAAE,IAAEG,GAAE,OAAMH,GAAE,IAAEG,GAAE,MAAM,GAAED,GAAE,OAAOM,GAAE,IAAEL,GAAE,OAAMK,GAAE,IAAEL,GAAE,MAAM,IAAG,EAAEC,IAAEF,GAAE,OAAO;AAAA,MAAC;AAAC,MAAAA,GAAE,QAAQ;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGH,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAG,IAAI;AAAE,IAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK,GAAEA,GAAE,UAAU,GAAEA,GAAE,YAAU,GAAGD,GAAE,WAAU,CAAC,CAAC,GAAEC,GAAE,cAAY,GAAGD,GAAE,OAAM,CAAC,CAAC,GAAEC,GAAE,YAAU,GAAGD,GAAE,WAAU,CAAC,CAAC,GAAEC,GAAE,OAAOF,GAAE,SAAQA,GAAE,OAAO,GAAEE,GAAE,OAAOF,GAAE,UAAQA,GAAE,OAAMA,GAAE,OAAO,GAAEE,GAAE,OAAOF,GAAE,UAAQA,GAAE,OAAMA,GAAE,UAAQA,GAAE,MAAM,GAAEE,GAAE,OAAOF,GAAE,SAAQA,GAAE,UAAQA,GAAE,MAAM,GAAEE,GAAE,OAAOF,GAAE,SAAQA,GAAE,OAAO,GAAEE,GAAE,OAAO,GAAEA,GAAE,KAAK,GAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,GAAGF,IAAEC,IAAEC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAG,GAAE;AAAC,SAAK,IAAE,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,GAAGJ,EAAC;AAAE,SAAGA,IAAEC,IAAG,CAAAA,OAAG;AAAC,WAAGD,IAAEC,IAAEC,IAAEC,EAAC,IAAGF,KAAE,GAAGD,EAAC,GAAG,UAAUI,GAAE,QAAO,GAAE,GAAEH,GAAE,OAAO,OAAMA,GAAE,OAAO,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE,MAAKD,IAAEE,IAAED,EAAC,IAAE,GAAG,MAAKD,GAAE,EAAE,GAAEE,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAE,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,GAAGJ,EAAC;AAAE,SAAGA,IAAEC,IAAG,CAAAA,OAAG;AAAC,WAAGD,IAAEC,IAAEC,IAAEC,EAAC,IAAGF,KAAE,GAAGD,EAAC,GAAG,UAAUI,GAAE,QAAO,GAAE,GAAEH,GAAE,OAAO,OAAMA,GAAE,OAAO,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE,MAAKD,IAAEC,IAAEC,EAAC,IAAE,GAAG,MAAKF,GAAE,EAAE,GAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAru6D,QAAAS,KAAA,IAAAC;AAAsu6D,KAAAD,MAAA,KAAK,MAAL,gBAAAA,IAAQ,SAAQ,KAAK,IAAE,SAAO,UAAK,MAAL,mBAAQ,SAAQ,KAAK,IAAE,SAAOC,MAAA,KAAK,MAAL,gBAAAA,IAAQ,SAAQ,KAAK,IAAE;AAAA,EAAM;AAAC;AAAE,SAAS,GAAGZ,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAE,aAAOD,GAAE,EAAE,KAAM,CAAAA,OAAGA,cAAa,SAAU;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAG,eAAa,OAAO,eAAaA,cAAa,WAAY;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAG,eAAa,OAAO,gBAAcA,cAAa,YAAa;AAAA,IAAE;AAAQ,YAAM,MAAM,0BAA0BC,IAAG;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,IAAAA,KAAE,GAAGD,EAAC;AAAE,UAAME,KAAE,GAAGF,EAAC,GAAEG,KAAE,IAAI,WAAWH,GAAE,QAAMA,GAAE,SAAO,CAAC;AAAE,OAAGE,IAAED,IAAE,GAAGD,EAAC,CAAC,GAAEC,GAAE,WAAW,GAAE,GAAED,GAAE,OAAMA,GAAE,QAAOC,GAAE,MAAKA,GAAE,eAAcE,EAAC,GAAE,GAAGD,EAAC,GAAED,KAAE,IAAI,UAAU,IAAI,kBAAkBE,GAAE,MAAM,GAAEH,GAAE,OAAMA,GAAE,MAAM,GAAEA,GAAE,EAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,UAAMC,KAAE,GAAGF,EAAC;AAAE,IAAAC,KAAE,GAAGD,EAAC;AAAE,UAAMG,KAAE,GAAGH,IAAE,CAAC,KAAG,GAAGA,EAAC;AAAE,IAAAE,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcC,EAAC,GAAE,GAAGH,EAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,UAAM,MAAM,oGAAoG;AAAE,SAAOA,GAAE,MAAIA,GAAE,IAAE,GAAGA,GAAE,OAAO,WAAW,QAAQ,GAAE,yFAAyF,IAAGA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,GAAGD,EAAC;AAAE,EAAAC,GAAE,SAAS,GAAE,GAAED,GAAE,OAAMA,GAAE,MAAM,GAAEC,GAAE,cAAcA,GAAE,QAAQ;AAAE,MAAIC,KAAE,GAAGF,IAAE,CAAC;AAAE,SAAOE,OAAIA,KAAE,GAAG,GAAGF,EAAC,GAAEC,EAAC,GAAED,GAAE,EAAE,KAAKE,EAAC,GAAEF,GAAE,IAAE,OAAIC,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,EAAAA,GAAE,EAAE,YAAYA,GAAE,EAAE,YAAW,IAAI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,GAAGD,EAAC;AAAE,SAAO,GAAG,GAAGA,EAAC,GAAEC,IAAE,MAAI,MAAI,SAASD,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE;AAAO,QAAGE,GAAE,UAAQF,GAAE,SAAOE,GAAE,WAASF,GAAE;AAAO,aAAOC,GAAE;AAAE,UAAME,KAAED,GAAE,OAAME,KAAEF,GAAE;AAAO,WAAOA,GAAE,QAAMF,GAAE,OAAME,GAAE,SAAOF,GAAE,QAAOA,KAAEC,GAAE,GAAEC,GAAE,QAAMC,IAAED,GAAE,SAAOE,IAAEJ;AAAA,EAAC,EAAEA,IAAG,MAAI;AAAC,QAAGC,GAAE,gBAAgBA,GAAE,aAAY,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,EAAED,GAAE,kBAAkB;AAAiB,YAAM,MAAM,oGAAoG;AAAE,WAAOA,GAAE,OAAO,sBAAsB;AAAA,EAAC,CAAE,CAAE;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,qBAAmB,GAAG,UAAU,IAAG,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,gBAAc,GAAG,UAAU,IAAG,GAAG,OAAK,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGD,MAAG,KAAGH,KAAEC,OAAIC,KAAED,OAAIG,MAAG,KAAGF,KAAEF,OAAIE,KAAED,MAAIE,IAAEC,EAAC;AAAC,GAAE,GAAG,QAAM;AAAG,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEG,IAAE;AAAC,SAAK,IAAER,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,SAAOC,IAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAOG,KAAG,KAAK,KAAG,KAAK,OAAK,MAAI,EAAE,MAAI,QAAQ,MAAM,4FAA4F;AAAA,EAAE;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,QAAIR,KAAE,GAAG,MAAK,CAAC;AAAE,WAAOA,OAAI,GAAG,IAAI,GAAE,GAAG,IAAI,GAAEA,KAAE,GAAG,IAAI,GAAE,GAAG,IAAI,GAAE,KAAK,EAAE,KAAKA,EAAC,GAAE,KAAK,IAAE,OAAIA;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,GAAE;AAAC,UAAIC;AAAE,UAAGD,cAAa;AAAU,QAAAC,KAAE,IAAI,UAAUD,GAAE,MAAK,KAAK,OAAM,KAAK,MAAM;AAAA,eAAUA,cAAa,cAAa;AAAC,cAAMD,KAAE,GAAG,IAAI,GAAEC,KAAE,GAAG,IAAI;AAAE,QAAAD,GAAE,cAAcA,GAAE,QAAQ,GAAEE,KAAE,GAAGD,IAAED,EAAC,GAAEA,GAAE,YAAYA,GAAE,YAAWE,EAAC,GAAEF,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAK,KAAK,OAAM,KAAK,QAAO,GAAEA,GAAE,MAAKA,GAAE,eAAc,IAAI,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAE,GAAGC,IAAED,IAAEE,EAAC,GAAE,GAAGD,IAAED,IAAE,OAAI,MAAI;AAAC,aAAG,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,GAAG,IAAI;AAAA,QAAC,CAAE,GAAE,GAAGC,EAAC,GAAE,GAAG,IAAI;AAAA,MAAC,OAAK;AAAC,YAAG,EAAEA,cAAa;AAAa,gBAAM,MAAM,0BAA0BA,IAAG;AAAE,WAAG,IAAI,GAAE,GAAG,IAAI,GAAEC,KAAE,GAAG,IAAI,GAAE,GAAG,IAAI;AAAA,MAAC;AAAC,MAAAF,GAAE,KAAKE,EAAC;AAAA,IAAC;AAAC,WAAO,IAAI,GAAGF,IAAE,KAAK,GAAG,GAAE,KAAK,EAAE,GAAE,KAAK,QAAO,KAAK,GAAE,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,GAAG,MAAK,CAAC,EAAE,MAAM,GAAE,KAAK,KAAG,GAAG,IAAI,EAAE,cAAc,GAAG,MAAK,CAAC,CAAC,GAAE,KAAG;AAAA,EAAE;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,oBAAkB,GAAG,UAAU,GAAE,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,GAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,eAAa,GAAG,UAAU;AAAG,IAAI,KAAG;AAAI,SAAS,MAAMA,IAAE;AAAC,SAAOA,GAAE,IAAK,CAAC,CAACA,IAAEC,EAAC,OAAK,EAAC,OAAMD,IAAE,KAAIC,GAAC,EAAG;AAAC;AAAC,IAAM,KAAG,SAASD,IAAE;AAAC,SAAO,cAAcA,GAAC;AAAA,IAAC,KAAI;AAAC,WAAK,EAAE,oCAAoC;AAAA,IAAC;AAAA,EAAC;AAAC,GAAG,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,IAAE,MAAG,KAAK,IAAED,IAAE,KAAK,IAAE,MAAK,KAAK,IAAE,GAAE,KAAK,IAAE,cAAY,OAAO,KAAK,EAAE,sBAAqB,WAASC,KAAE,KAAK,EAAE,SAAOA,KAAE,GAAG,IAAE,KAAK,EAAE,SAAO,IAAI,gBAAgB,GAAE,CAAC,KAAG,QAAQ,KAAK,oHAAoH,GAAE,KAAK,EAAE,SAAO,SAAS,cAAc,QAAQ;AAAA,EAAE;AAAA,EAAC,MAAM,gBAAgBD,IAAE;AAAC,UAAMC,KAAE,OAAM,MAAM,MAAMD,EAAC,GAAG,YAAY;AAAE,IAAAA,KAAE,EAAEA,GAAE,SAAS,QAAQ,KAAGA,GAAE,SAAS,YAAY,IAAG,KAAK,SAAS,IAAI,WAAWC,EAAC,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,SAAU,IAAI,cAAa,OAAOA,EAAC,GAAE,KAAE;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE,QAAOG,KAAE,KAAK,EAAE,QAAQD,EAAC;AAAE,SAAK,EAAE,OAAO,IAAIF,IAAEG,EAAC,GAAEF,KAAE,KAAK,EAAE,mBAAmBC,IAAEC,EAAC,IAAE,KAAK,EAAE,iBAAiBD,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,EAAE,mBAAiB,QAAQ,KAAK,kHAAkH,GAAE,GAAG,MAAKD,MAAG,eAAe,CAAAA,OAAG;AAAC,SAAG,MAAKC,KAAEA,MAAG,gBAAgB,CAAAA,OAAG;AAAC,aAAK,EAAE,gBAAgBD,IAAEC,IAAEJ,IAAEC,MAAG,GAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAE;AAAC,SAAK,IAAEA;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,SAAK,EAAE,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAAC,SAAK,EAAE,sCAAoCA;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAE;AAAC,OAAG,MAAK,oBAAoB,CAAAC,OAAG;AAAC,MAAAD,GAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,GAAG,MAAK,oBAAoB,CAAAD,OAAG;AAAC,WAAK,EAAE,gBAAgBA,IAAE,MAAM;AAAA,IAAC,CAAE,GAAE,OAAO,KAAK,EAAE,gBAAgB;AAAA,EAAgB;AAAA,EAAC,oBAAoBA,IAAE;AAAC,SAAK,EAAE,gBAAcA;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEC,IAAE;AAAC,SAAK,EAAE,uBAAqB,KAAK,EAAE,wBAAsB,CAAC,GAAE,KAAK,EAAE,qBAAqBD,EAAC,IAAEC;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAEC,IAAEC,IAAE;AAAC,SAAK,0BAA0BF,IAAE,GAAE,GAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAEL,GAAE;AAAO,SAAK,MAAIK,OAAI,KAAK,KAAG,KAAK,EAAE,MAAM,KAAK,CAAC,GAAE,KAAK,IAAE,KAAK,EAAE,QAAQA,EAAC,GAAE,KAAK,IAAEA,KAAG,KAAK,EAAE,QAAQ,IAAIL,IAAE,KAAK,IAAE,CAAC,GAAE,GAAG,MAAKG,IAAG,CAAAH,OAAG;AAAC,WAAK,EAAE,uBAAuB,KAAK,GAAEC,IAAEC,IAAEF,IAAEI,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBJ,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAK,CAACE,IAAEC,EAAC,IAAE,GAAG,MAAKJ,IAAEC,EAAC;AAAE,WAAK,EAAE,yBAAyBA,IAAEE,IAAEC,IAAEF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,wBAAwBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,uBAAuBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,aAAK,EAAE,wBAAwBA,IAAEC,IAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAK,OAAO,KAAKD,EAAC,GAAG,CAAAG,OAAG;AAAC,WAAG,MAAK,OAAO,OAAOH,EAAC,GAAG,CAAAI,OAAG;AAAC,eAAK,EAAE,6BAA6BD,IAAEC,IAAE,OAAO,KAAKJ,EAAC,EAAE,QAAOC,IAAEC,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,cAAMG,KAAE,KAAK,EAAE,QAAQJ,GAAE,MAAM;AAAE,aAAK,EAAE,OAAO,IAAIA,IAAEI,EAAC,GAAE,KAAK,EAAE,uBAAuBA,IAAEJ,GAAE,QAAOC,IAAEC,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBJ,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,6BAA6BA,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,oBAAoBH,GAAE,MAAM;AAAE,UAAG,CAACG;AAAE,cAAM,MAAM,6CAA6C;AAAE,iBAAUF,MAAKD;AAAE,aAAK,EAAE,oBAAoBG,IAAEF,EAAC;AAAE,WAAK,EAAE,4BAA4BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,sBAAsBH,GAAE,MAAM;AAAE,UAAG,CAACG;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUF,MAAKD;AAAE,aAAK,EAAE,sBAAsBG,IAAEF,EAAC;AAAE,WAAK,EAAE,8BAA8BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,qBAAqBH,GAAE,MAAM;AAAE,UAAG,CAACG;AAAE,cAAM,MAAM,8CAA8C;AAAE,iBAAUF,MAAKD;AAAE,aAAK,EAAE,qBAAqBG,IAAEF,EAAC;AAAE,WAAK,EAAE,6BAA6BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,mBAAmBH,GAAE,MAAM;AAAE,UAAG,CAACG;AAAE,cAAM,MAAM,4CAA4C;AAAE,iBAAUF,MAAKD;AAAE,aAAK,EAAE,mBAAmBG,IAAEF,EAAC;AAAE,WAAK,EAAE,2BAA2BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,oBAAoBH,GAAE,MAAM;AAAE,UAAG,CAACG;AAAE,cAAM,MAAM,qDAAqD;AAAE,iBAAUF,MAAKD;AAAE,aAAK,EAAE,oBAAoBG,IAAEF,EAAC;AAAE,WAAK,EAAE,4BAA4BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,sBAAsBH,GAAE,MAAM;AAAE,UAAG,CAACG;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUF,MAAKD;AAAE,WAAG,MAAKC,IAAG,CAAAD,OAAG;AAAC,eAAK,EAAE,sBAAsBG,IAAEH,EAAC;AAAA,QAAC,CAAE;AAAE,WAAK,EAAE,8BAA8BG,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBF,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BD,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BD,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BD,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,yBAAyBD,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BD,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,aAAK,EAAE,4BAA4BA,IAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,cAAME,KAAE,KAAK,EAAE,QAAQH,GAAE,MAAM;AAAE,aAAK,EAAE,OAAO,IAAIA,IAAEG,EAAC,GAAE,KAAK,EAAE,2BAA2BA,IAAEH,GAAE,QAAOC,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,+BAA+BH,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,oBAAoBF,GAAE,MAAM;AAAE,UAAG,CAACE;AAAE,cAAM,MAAM,6CAA6C;AAAE,iBAAUD,MAAKD;AAAE,aAAK,EAAE,oBAAoBE,IAAED,EAAC;AAAE,WAAK,EAAE,gCAAgCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iCAAiCD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,sBAAsBF,GAAE,MAAM;AAAE,UAAG,CAACE;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUD,MAAKD;AAAE,aAAK,EAAE,sBAAsBE,IAAED,EAAC;AAAE,WAAK,EAAE,kCAAkCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gCAAgCD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,qBAAqBF,GAAE,MAAM;AAAE,UAAG,CAACE;AAAE,cAAM,MAAM,8CAA8C;AAAE,iBAAUD,MAAKD;AAAE,aAAK,EAAE,qBAAqBE,IAAED,EAAC;AAAE,WAAK,EAAE,iCAAiCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,8BAA8BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,mBAAmBF,GAAE,MAAM;AAAE,UAAG,CAACE;AAAE,cAAM,MAAM,4CAA4C;AAAE,iBAAUD,MAAKD;AAAE,aAAK,EAAE,mBAAmBE,IAAED,EAAC;AAAE,WAAK,EAAE,+BAA+BC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,+BAA+BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,oBAAoBF,GAAE,MAAM;AAAE,UAAG,CAACE;AAAE,cAAM,MAAM,qDAAqD;AAAE,iBAAUD,MAAKD;AAAE,aAAK,EAAE,oBAAoBE,IAAED,EAAC;AAAE,WAAK,EAAE,gCAAgCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iCAAiCD,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,sBAAsBF,GAAE,MAAM;AAAE,UAAG,CAACE;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUD,MAAKD;AAAE,WAAG,MAAKC,IAAG,CAAAD,OAAG;AAAC,eAAK,EAAE,sBAAsBE,IAAEF,EAAC;AAAA,QAAC,CAAE;AAAE,WAAK,EAAE,kCAAkCE,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,oBAAoBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,mBAAmBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,yBAAyBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,oBAAoBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKF,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,IAAEE,MAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKF,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BA,IAAEE,MAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAEC,IAAEC,IAAE;AAAC,SAAK,EAAE,wBAAsB,QAAQ,KAAK,4HAA4H,GAAE,GAAG,MAAKF,IAAG,CAACA,IAAEE,OAAI;AAAC,MAAAF,KAAE,IAAI,aAAaA,GAAE,QAAOA,GAAE,YAAWA,GAAE,SAAO,CAAC,GAAEC,GAAED,IAAEE,EAAC;AAAA,IAAC,CAAE,GAAE,GAAG,MAAKF,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,IAAEE,MAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,EAAE,eAAe;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,EAAE,YAAY,GAAE,KAAK,EAAE,kBAAgB,QAAO,KAAK,EAAE,uBAAqB;AAAA,EAAM;AAAC,GAAE,cAAc,GAAE;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,GAAGF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAK,CAACE,IAAEC,EAAC,IAAE,GAAG,MAAKJ,IAAEC,EAAC;AAAE,WAAK,GAAG,gCAAgCA,IAAEE,IAAEC,IAAEF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,EAAEF,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,GAAG,qBAAqBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,GAAG,2BAA2BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,EAAE;AAAE,IAAI;AAAJ,IAAO,KAAG,cAAc,GAAE;AAAC;AAAE,eAAe,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAO,eAAeF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAGH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,EAAC,EAAEH,IAAEE,GAAE,WAAS,GAAG,IAAE,SAAO,SAAS,cAAc,QAAQ,IAAGD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAGH,GAAE,GAAE;AAAC,UAAMK,KAAE,IAAI;AAAG,QAAGH,MAAA,gBAAAA,GAAG,kBAAiB;AAAC,UAAG,CAACF,GAAE;AAAG,cAAM,MAAM,+CAA+C;AAAE,UAAII,KAAEF,GAAE;AAAiB,UAAGE,GAAE,QAAMA,GAAE,SAAOA,GAAE,OAAKA,GAAE;AAAO,cAAM,MAAM,oDAAoD;AAAE,UAAGA,GAAE,OAAK,KAAGA,GAAE,MAAI,KAAGA,GAAE,QAAM,KAAGA,GAAE,SAAO;AAAE,cAAM,MAAM,uCAAuC;AAAE,SAAGC,IAAE,IAAGD,GAAE,OAAKA,GAAE,SAAO,CAAC,GAAE,GAAGC,IAAE,IAAGD,GAAE,MAAIA,GAAE,UAAQ,CAAC,GAAE,GAAGC,IAAE,GAAED,GAAE,QAAMA,GAAE,IAAI,GAAE,GAAGC,IAAE,GAAED,GAAE,SAAOA,GAAE,GAAG;AAAA,IAAC;AAAM,SAAGC,IAAE,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAE,CAAC,GAAE,GAAGA,IAAE,GAAE,CAAC;AAAE,QAAGH,MAAA,gBAAAA,GAAG,iBAAgB;AAAC,WAAGA,MAAA,gBAAAA,GAAG,mBAAgB,MAAI;AAAE,cAAM,MAAM,4CAA4C;AAAE,UAAG,GAAGG,IAAE,GAAE,CAAC,KAAK,KAAGH,GAAE,kBAAgB,GAAG,IAAEA,MAAA,gBAAAA,GAAG,mBAAgB,OAAK,GAAE;AAAC,cAAK,CAACF,IAAEG,EAAC,IAAE,GAAGF,EAAC;AAAE,QAAAC,KAAE,GAAGG,IAAE,CAAC,IAAEF,KAAEH,IAAEI,KAAE,GAAGC,IAAE,CAAC,IAAEL,KAAEG,IAAE,GAAGE,IAAE,GAAEH,EAAC,GAAE,GAAGG,IAAE,GAAED,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAJ,GAAE,EAAE,iBAAiBK,GAAE,EAAE,GAAE,4BAA2BL,GAAE,GAAEG,EAAC;AAAA,EAAC;AAAC,EAAAH,GAAE,EAAE,GAAGC,IAAED,GAAE,IAAGG,MAAG,YAAY,IAAI,CAAC,GAAEH,GAAE,iBAAiB;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAv21E,MAAAS;AAAw21E,OAAGA,MAAAX,GAAE,gBAAF,gBAAAW,IAAe;AAAI,UAAM,MAAM,gFAAgF;AAAE,KAAGX,IAAEC,IAAEC,IAAEF,GAAE,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAhg2E,MAAAQ;AAAig2E,MAAG,GAACA,MAAAX,GAAE,gBAAF,gBAAAW,IAAe;AAAI,UAAM,MAAM,gFAAgF;AAAE,KAAGX,IAAEC,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEH,GAAE;AAAK,QAAMI,KAAEJ,GAAE,OAAMO,KAAEH,MAAGJ,KAAEA,GAAE;AAAQ,OAAIG,cAAa,cAAYA,cAAa,iBAAeA,GAAE,WAASI;AAAE,UAAM,MAAM,gCAA8BJ,GAAE,SAAOI,EAAC;AAAE,SAAOR,KAAE,IAAI,GAAG,CAACI,EAAC,GAAEF,IAAE,OAAGF,GAAE,EAAE,EAAE,QAAOA,GAAE,GAAEK,IAAEJ,EAAC,GAAEE,KAAEH,GAAE,MAAM,IAAEA;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMH,EAAC,GAAE,KAAK,IAAEA,IAAE,KAAK,KAAGC,IAAE,KAAK,IAAEC,IAAE,KAAK,KAAGC,IAAE,KAAK,IAAE,IAAI;AAAA,EAAE;AAAA,EAAC,EAAEH,IAAEC,KAAE,MAAG;AAAC,QAAG,iBAAgBD,MAAG,GAAG,KAAK,aAAY,GAAE,CAAC,CAACA,GAAE,eAAa,YAAUA,GAAE,WAAW,GAAE,WAASA,GAAE,UAAQ,KAAK,EAAE,EAAE,WAASA,GAAE;AAAO,YAAM,MAAM,iDAAiD;AAAE,WAAO,MAAM,EAAEA,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,MAAM,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,gBAAe,KAAE,GAAE,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,4BAA2BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,0BAAwB,GAAE,GAAE,6BAA4BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,2BAAyB,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,cAAc,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wDAAwD,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,wBAAwB,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD;AAAE,QAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,WAAW,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,eAAeA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,GAAG,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAArX,IAAuX,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAA5hB,IAA8hB,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAnnB,IAAqnB,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAlqB,IAAoqB,KAAG,GAAG,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAn0B,IAAq0B,KAAG,GAAG,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,CAAC;AAA74B,IAA+4B,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAA57B,IAA87B,KAAG,GAAG,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,CAAC;AAA/xC,IAAiyC,KAAG,CAAC,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,EAAE;AAAx0C,IAA00C,KAAG,GAAG,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,8BAA6B,CAAC,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,8BAA6B,CAAC,EAAC,GAAE,KAAK,qCAAmC,KAAK,wBAAsB,OAAG,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,2BAA0BA,OAAI,KAAK,wBAAsB,CAAC,CAACA,GAAE,wBAAuB,wCAAuCA,OAAI,KAAK,qCAAmC,CAAC,CAACA,GAAE,qCAAoC,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,+BAA+B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD;AAAE,QAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,cAAc,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,0BAAwB,GAAGA,IAAE,aAAa,GAAE,GAAGE,IAAE,yBAAyB,GAAE,KAAK,EAAE,0BAA0B,eAAe,CAACF,IAAEC,OAAI;AAAC,UAAG,KAAK;AAAsB,mBAAUA,MAAKD;AAAE,UAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,gBAAgB,KAAK,GAAGD,GAAE,EAAE,KAAG,CAAC,CAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,eAAe,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,uCAAqC,GAAGA,IAAE,eAAe,GAAE,GAAGE,IAAE,6BAA6B,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAACF,IAAEC,OAAI;AAAC,UAAG,KAAK;AAAmC,mBAAUA,MAAKD;AAAE,WAACA,KAAE,GAAG,GAAGC,EAAC,GAAE,IAAG,CAAC,MAAI,KAAK,EAAE,6BAA6B,KAAK,EAAC,MAAK,GAAGD,IAAE,CAAC,KAAG,KAAG,GAAE,SAAQ,GAAGA,IAAE,CAAC,KAAG,KAAG,GAAE,MAAK,GAAGA,IAAE,GAAE,IAAG,GAAG,CAAC,EAAE,MAAM,KAAG,CAAC,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAGA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,sBAAoB,IAAG,GAAG,0BAAwB,IAAG,GAAG,8BAA4B,IAAG,GAAG,2BAAyB,IAAG,GAAG,2BAAyB,IAAG,GAAG,+BAA6B,IAAG,GAAG,4BAA0B,IAAG,GAAG,2BAAyB,IAAG,GAAG,0BAAwB,IAAG,GAAG,6BAA2B;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,IAAE,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,QAAG,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,MAAKF,IAAEG,MAAG,CAAC,CAAC,GAAE,CAAC,KAAK;AAAE,aAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIH,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wDAAwD,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,+BAA+B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,EAAE,kBAAkB,CAACF,IAAEC,OAAI;AAAC,UAAIC,KAAE,CAAC,KAAK,GAAEC,KAAEH,GAAE,MAAKI,KAAEJ,GAAE;AAAM,YAAMK,KAAED,MAAGJ,KAAEA,GAAE;AAAQ,UAAGG,cAAa;AAAW,YAAGA,GAAE,WAAS,IAAEE,IAAE;AAAC,gBAAMJ,KAAE,IAAI,kBAAkB,IAAEI,EAAC;AAAE,mBAAQL,KAAE,GAAEA,KAAEK,IAAE,EAAEL;AAAE,YAAAC,GAAE,IAAED,EAAC,IAAEG,GAAE,IAAEH,EAAC,GAAEC,GAAE,IAAED,KAAE,CAAC,IAAEG,GAAE,IAAEH,KAAE,CAAC,GAAEC,GAAE,IAAED,KAAE,CAAC,IAAEG,GAAE,IAAEH,KAAE,CAAC,GAAEC,GAAE,IAAED,KAAE,CAAC,IAAE;AAAI,UAAAG,KAAE,IAAI,UAAUF,IAAEG,IAAEJ,EAAC;AAAA,QAAC,OAAK;AAAC,cAAGG,GAAE,WAAS,IAAEE;AAAE,kBAAM,MAAM,gCAA8BF,GAAE,SAAOE,EAAC;AAAE,UAAAF,KAAE,IAAI,UAAU,IAAI,kBAAkBA,GAAE,QAAOA,GAAE,YAAWA,GAAE,MAAM,GAAEC,IAAEJ,EAAC;AAAA,QAAC;AAAA,eAAS,EAAEG,cAAa;AAAc,cAAM,MAAM,uBAAuBA,GAAE,YAAY,MAAM;AAAE,MAAAC,KAAE,IAAI,GAAG,CAACD,EAAC,GAAE,OAAG,OAAG,KAAK,EAAE,EAAE,QAAO,KAAK,GAAEC,IAAEJ,EAAC,GAAE,KAAK,IAAEE,KAAEA,KAAEE,GAAE,MAAM,IAAEA,IAAE,KAAK,KAAG,KAAK,EAAEF,EAAC,GAAE,GAAG,MAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,WAAK,IAAE,MAAK,KAAK,KAAG,KAAK,EAAE,IAAI,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,UAAQ,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,GAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,WAAS,CAAC,GAAEA,GAAE,YAAU,CAAC,GAAEA,GAAE,iBAAe,CAAC,GAAEA,GAAE,aAAW,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,MAAIA,GAAE,SAAS,SAAO,EAAC,UAAS,CAAC,GAAE,WAAU,CAAC,GAAE,gBAAe,CAAC,GAAE,YAAW,CAAC,GAAE,cAAa,CAAC,EAAC,IAAE,EAAC,UAASA,GAAE,UAAS,WAAUA,GAAE,WAAU,gBAAeA,GAAE,gBAAe,YAAWA,GAAE,YAAW,cAAaA,GAAE,WAAU;AAAC;AAAC,SAAS,GAAGA,IAAEC,KAAE,MAAG;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUE,MAAKJ,IAAE;AAAC,QAAIG,KAAE,GAAGC,EAAC;AAAE,IAAAJ,KAAE,CAAC;AAAE,eAAUE,MAAKC,GAAE,EAAE;AAAE,MAAAA,KAAEF,MAAG,QAAM,GAAGC,IAAE,CAAC,IAAE,GAAGA,IAAE,CAAC,KAAG,IAAE,IAAGF,GAAE,KAAK,EAAC,OAAM,GAAGE,IAAE,CAAC,KAAG,GAAE,OAAMC,IAAE,cAAa,GAAGD,IAAE,CAAC,KAAG,MAAI,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE,CAAC;AAAE,IAAAA,GAAE,KAAKF,EAAC;AAAA,EAAC;AAAC,SAAOE;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAjn4G,QAAAW,KAAA,IAAAC,KAAA;AAAkn4G,QAAG,GAAG,KAAK,GAAE,GAAEZ,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAEA,GAAE,iCAAgC;AAAC,UAAIC,KAAE,IAAI,MAAGC,KAAED,IAAEE,KAAE,GAAGH,GAAE,kCAAgCW,MAAA,GAAG,KAAK,GAAE,IAAG,CAAC,MAAd,gBAAAA,IAAiB,GAAG;AAAE,SAAGT,IAAE,GAAE,GAAEC,EAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAEF,EAAC;AAAA,IAAC;AAAM,iBAASD,GAAE,qCAAiC,QAAG,KAAK,GAAE,IAAG,CAAC,MAAd,mBAAiB;AAAI,WAAOA,GAAE,mCAAiC,GAAGE,KAAED,KAAE,IAAI,MAAG,GAAE,GAAEE,KAAE,GAAGH,GAAE,kCAAgCY,MAAA,GAAG,KAAK,GAAE,IAAG,CAAC,MAAd,gBAAAA,IAAiB,GAAG,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAEX,EAAC,KAAG,WAASD,GAAE,qCAAiC,QAAG,KAAK,GAAE,IAAG,CAAC,MAAd,mBAAiB,MAAI,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,eAAe,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,kEAAkE,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,6BAA6B,GAAE,GAAGA,IAAE,0BAA0B,GAAE,GAAGA,IAAE,sCAAsC,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,IAAE;AAAC,QAAAA,KAAE,GAAGC,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAK,GAAGD,IAAE,IAAG,CAAC;AAAE,UAAAE,GAAE,KAAK,EAAC,GAAE,GAAGD,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,aAAK,UAAU,KAAKC,EAAC;AAAA,MAAC;AAAC,SAAG,MAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAACA,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,IAAE;AAAC,QAAAA,KAAE,GAAGC,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAK,GAAGD,IAAE,IAAG,CAAC;AAAE,UAAAE,GAAE,KAAK,EAAC,GAAE,GAAGD,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,aAAK,eAAe,KAAKC,EAAC;AAAA,MAAC;AAAC,SAAG,MAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAACA,IAAEC,OAAI;AAAC,WAAK,SAAS,KAAK,GAAG,GAAGD,IAAE,KAAE,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACA,IAAEC,OAAI;AAAC,WAAK,WAAW,KAAK,GAAG,GAAGD,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAC,WAAUA,GAAE,WAAU,gBAAeA,GAAE,gBAAe,cAAaA,GAAE,YAAW,YAAWA,GAAE,WAAU;AAAC;AAAC,GAAG,UAAU,oBAAkB,GAAG,UAAU,IAAG,GAAG,UAAU,YAAU,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,0BAA0B,GAAE,GAAGA,IAAE,sCAAsC,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD;AAAE,QAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,UAAU,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAACA,IAAEC,OAAI;AAAC,iBAAUA,MAAKD;AAAE,QAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,eAAe,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,YAAWC,KAAED,GAAE;AAAK,YAAME,KAAE,CAAC;AAAE,iBAAUH,MAAKD,IAAE;AAAC,QAAAA,KAAE,GAAGC,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAKD,GAAE,EAAE;AAAE,UAAAE,GAAE,KAAK,EAAC,OAAM,GAAGD,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,KAAG,IAAG,cAAa,GAAGA,IAAE,CAAC,KAAG,MAAI,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE,CAAC;AAAE,QAAAG,GAAE,KAAKF,EAAC;AAAA,MAAC;AAAC,MAAAC,GAAE,KAAKD,IAAE,GAAGE,EAAC,GAAE,GAAG,MAAKH,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB;AAAG,IAAI,KAAG,GAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,uBAAsB,CAAC,GAAE,mBAAkB,CAAC,GAAE,wBAAuB,CAAC,GAAE,oBAAmB,CAAC,GAAE,yBAAwB,CAAC,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,QAAG,CAACA,GAAE;AAAE,aAAOA,GAAE;AAAE,IAAAA,GAAE,EAAEA,GAAE,CAAC;AAAA,EAAC,UAAC;AAAQ,OAAGA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,EAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK,GAAGD,EAAC,CAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,sBAAqB,MAAK,KAAE,GAAE,KAAK,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,uBAAsB,CAAC,GAAE,mBAAkB,CAAC,GAAE,wBAAuB,CAAC,GAAE,oBAAmB,CAAC,GAAE,yBAAwB,CAAC,EAAC,GAAE,KAAK,8BAA4B,KAAK,wBAAsB,OAAG,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,iCAAgCA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,+BAA6B,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,2BAA0BA,OAAI,KAAK,wBAAsB,CAAC,CAACA,GAAE,wBAAuB,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,iCAAgCA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,+BAA6B,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,iCAAgCA,OAAI,KAAK,8BAA4B,CAAC,CAACA,GAAE,8BAA6B,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEG,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKH,IAAEI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,oBAAoB,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,2BAA2B,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,4BAA4B;AAAE,UAAMC,KAAE,IAAI,MAAGC,KAAE,IAAI;AAAG,OAAGA,IAAE,GAAE,GAAG,qGAAqG,GAAE,EAAE,GAAE,SAASF,IAAEC,IAAE;AAAC,UAAG,QAAMA;AAAE,YAAG,MAAM,QAAQA,EAAC;AAAE,aAAGD,IAAE,GAAE,GAAGC,EAAC,CAAC;AAAA,aAAM;AAAC,cAAG,EAAE,YAAU,OAAOA,MAAGA,cAAa,KAAG,EAAEA,EAAC;AAAG,kBAAM,MAAM,uCAAqCA,KAAE,+EAA+E;AAAE,aAAGD,IAAE,GAAE,GAAGC,IAAE,KAAE,GAAE,EAAE,CAAC;AAAA,QAAC;AAAA,IAAC,EAAEC,IAAE,KAAK,EAAE,EAAE,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,oEAAoE,GAAE,GAAGA,IAAE,GAAE,IAAGD,EAAC,GAAE,GAAGC,IAAE,0BAA0B,GAAE,GAAGA,IAAE,+BAA+B,GAAE,GAAGA,IAAE,2CAA2C,GAAE,GAAGA,IAAE,+BAA+B,GAAE,GAAGA,IAAE,yCAAyC,GAAE,GAAGA,IAAE,qDAAqD,GAAE,GAAGA,IAAE,2CAA2C,GAAE,GAAGA,IAAE,uDAAuD,GAAEA,GAAE,EAAEF,EAAC,GAAE,GAAGD,IAAEG,EAAC,GAAE,GAAG,MAAKH,EAAC,GAAE,KAAK,EAAE,oBAAoB,kBAAkB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,aAAa,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,wBAAwB,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAmB,MAAAF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,gCAA8B,GAAGG,IAAE,+CAA+C,GAAE,GAAG,MAAK,wBAAwB,GAAE,KAAK,EAAE,EAAE,0BAA0B,CAACH,IAAEC,OAAI;AAAC,WAAK,EAAE,wBAAsB,CAAC,GAAG,MAAKD,IAAE,MAAG,CAAC,KAAK,CAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,0BAA0B,CAAAD,OAAG;AAAC,WAAK,EAAE,wBAAsB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,EAAE,oBAAoB,kBAAkB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,aAAa,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,0BAAwB,GAAGA,IAAE,mBAAmB,GAAE,GAAGG,IAAE,oCAAoC,GAAE,KAAK,EAAE,oBAAoB,qBAAqB,CAACH,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAgB,WAAK,0BAAwBF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,GAAE,EAAE,KAAG,CAAC,CAAC,CAAC,IAAG,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,qBAAqB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,EAAE,oBAAoB,uBAAuB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,iBAAiB,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,uBAAuB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,6BAA6B,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAuB,MAAAF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,6BAA6B,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,wBAAwB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,kBAAkB,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,8BAA8B,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAwB,MAAAF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,8BAA8B,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB,IAAG,GAAG,mBAAiB,IAAG,GAAG,sBAAoB,IAAG,GAAG,0BAAwB,IAAG,GAAG,8BAA4B,IAAG,GAAG,2BAAyB,IAAG,GAAG,2BAAyB,IAAG,GAAG,+BAA6B,IAAG,GAAG,4BAA0B,IAAG,GAAG,2BAAyB,IAAG,GAAG,0BAAwB,IAAG,GAAG,6BAA2B;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,eAAc,aAAY,IAAE,GAAE,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,KAAK,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAG,KAAK,GAAE,IAAG,CAAC,CAAC,CAAC,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,aAAa,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,iBAAiB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,8DAA8D,GAAE,GAAGA,IAAE,mBAAmB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,iCAAiC,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,oBAAoB,mBAAmB,CAACF,IAAEC,OAAI;AAAC,WAAK,IAAE,SAASD,IAAE;AAAC,cAAMC,KAAE,EAAC,iBAAgB,GAAGD,IAAE,IAAG,CAAC,EAAE,IAAK,CAAAA,OAAC;AAAzswH,cAAAW;AAA2swH,sBAAGA,MAAA,GAAGX,IAAE,IAAG,CAAC,MAAT,gBAAAW,IAAY,QAAK,CAAC,GAAE,GAAGX,IAAE,CAAC,KAAG,GAAE,GAAGA,IAAE,CAAC,KAAG,EAAE;AAAA,SAAE,EAAC;AAAE,eAAO,QAAM,GAAG,GAAGA,IAAE,CAAC,CAAC,MAAIC,GAAE,cAAY,GAAG,GAAGD,IAAE,CAAC,CAAC,KAAG,IAAGC;AAAA,MAAC,EAAE,GAAGD,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAmB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,WAAS,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,KAAK,aAAW,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,GAAE,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,QAAIC,KAAE,KAAK,GAAEC,KAAE,GAAG,KAAK,GAAE,IAAG,CAAC;AAAE,WAAOA,KAAEA,KAAEA,GAAE,MAAM,IAAE,IAAI,MAAG,WAASF,GAAE,cAAY,GAAGE,IAAE,GAAEF,GAAE,WAAW,IAAE,iBAAgBA,MAAG,GAAGE,IAAE,CAAC,GAAE,WAASF,GAAE,WAAS,GAAGE,IAAE,GAAEF,GAAE,QAAQ,IAAE,cAAaA,MAAG,GAAGE,IAAE,CAAC,GAAE,GAAGD,IAAE,GAAE,GAAEC,EAAC,GAAE,KAAK,EAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,WAAO,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,0DAA0D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,2BAA2B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,oBAAoB,kBAAkB,CAACF,IAAEC,OAAI;AAAC,MAAAD,KAAE,GAAGA,EAAC,GAAE,KAAK,aAAW,SAASA,IAAE;AAAC,eAAM,EAAC,YAAW,GAAGA,IAAE,IAAG,CAAC,EAAE,IAAK,CAAAA,OAAG;AAAruzH,cAAAW,KAAA;AAAsuzH,gBAAMV,KAAE,EAAC,WAAU,GAAGD,IAAE,CAAC,KAAG,KAAG,IAAG,UAAS,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE;AAAE,cAAG,WAAS,GAAGA,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC;AAAE,YAAAA,KAAE,GAAGA,KAAE,GAAGA,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC,GAAE,GAAE,IAAG,GAAG,CAAC,GAAEC,GAAE,iBAAeD,GAAE,MAAM;AAAA,eAAM;AAAC,kBAAME,KAAE,IAAI,WAAW,CAAC;AAAE,YAAAD,GAAE,uBAAmB,MAAAU,MAAA,GAAGX,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC,MAAf,gBAAAW,IAAkB,SAAlB,mBAAwB,QAAKT;AAAA,UAAC;AAAC,iBAAOD;AAAA,QAAC,CAAE,GAAE,aAAY,GAAG,GAAGD,IAAE,CAAC,CAAC,KAAG,EAAC;AAAA,MAAC,EAAEA,EAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,mBAAiB,SAASA,IAAEC,IAAE;AAAC,MAAGD,GAAE,kBAAgBC,GAAE;AAAe,IAAAD,KAAE,GAAGA,GAAE,gBAAeC,GAAE,cAAc;AAAA,OAAM;AAAC,QAAG,CAACD,GAAE,sBAAoB,CAACC,GAAE;AAAmB,YAAM,MAAM,0EAA0E;AAAE,IAAAD,KAAE,GAAG,GAAGA,GAAE,kBAAkB,GAAE,GAAGC,GAAE,kBAAkB,CAAC;AAAA,EAAC;AAAC,SAAOD;AAAC,GAAE,GAAG,UAAU,gBAAc,GAAG,UAAU,IAAG,GAAG,UAAU,QAAM,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,SAAK,kBAAgBF,IAAE,KAAK,eAAaC,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAh41H,QAAAS,KAAA;AAAi41H,KAAAA,MAAA,KAAK,oBAAL,gBAAAA,IAAsB,QAAS,CAAAX,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC,KAAI,UAAK,iBAAL,mBAAmB;AAAA,EAAO;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,EAAAA,GAAE,eAAa,QAAOA,GAAE,kBAAgB,QAAOA,GAAE,gBAAc;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,UAAMC,KAAE,IAAI,GAAGD,GAAE,iBAAgBA,GAAE,cAAaA,GAAE,aAAa;AAAE,QAAG,CAACA,GAAE;AAAE,aAAOC;AAAE,IAAAD,GAAE,EAAEC,EAAC;AAAA,EAAC,UAAC;AAAQ,OAAGD,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,IAAE,CAAC,GAAE,KAAK,qBAAmB,OAAG,KAAK,wBAAsB,MAAG,KAAK,IAAE,IAAI,MAAG,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAGD,KAAE,KAAK,GAAE,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,WAASA,GAAE,qBAAmB,GAAG,KAAK,GAAE,GAAE,GAAGA,GAAE,kBAAkB,CAAC,IAAE,wBAAuBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,wBAAuBA,OAAI,KAAK,qBAAmBA,GAAE,sBAAoB,QAAI,2BAA0BA,OAAI,KAAK,wBAAsBA,GAAE,yBAAuB,OAAI,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,KAAC,SAASA,IAAE;AAA3y3H,UAAAW,KAAA;AAA4y3H,YAAMV,KAAE,GAAGD,GAAE,GAAG,GAAE,IAAG,CAAC,EAAE,OAAQ,CAAAA,QAAI,GAAGA,IAAE,CAAC,KAAG,IAAI,SAAS,iDAAiD,CAAE;AAAE,UAAGA,GAAE,IAAE,CAAC,GAAEC,GAAE,SAAO;AAAE,cAAM,MAAM,8EAA8E;AAAE,YAAIA,GAAE,aAAS,MAAAU,MAAA,GAAGV,GAAE,CAAC,GAAE,IAAG,CAAC,MAAZ,gBAAAU,IAAe,QAAf,mBAAoB,QAAK,oBAAI,OAAK,QAAS,CAACV,IAAEC,OAAI;AAAC,QAAAF,GAAE,EAAE,OAAOE,EAAC,CAAC,IAAE,GAAGD,IAAE,CAAC,KAAG;AAAA,MAAE,CAAE;AAAA,IAAC,EAAE,IAAI;AAAA,EAAC;AAAA,EAAC,QAAQD,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEG,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKH,IAAEI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,GAAG,MAAKF,EAAC,GAAE,KAAK,0BAAwB,GAAGA,IAAE,kBAAkB,GAAE,GAAGE,IAAE,mCAAmC,GAAE,GAAG,MAAK,kBAAkB,GAAE,KAAK,EAAE,GAAG,oBAAoB,CAACF,IAAEC,OAAI;AAAC,WAAK,kBAAgBD,GAAE,IAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,CAAE,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,oBAAoB,CAAAD,OAAG;AAAC,WAAK,kBAAgB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,uBAAqB,GAAGA,IAAE,eAAe,GAAE,GAAGE,IAAE,6BAA6B,GAAE,GAAG,MAAK,eAAe,GAAE,KAAK,EAAE,EAAE,iBAAiB,CAACF,IAAEC,OAAI;AAAC,WAAK,eAAa,GAAG,MAAKD,IAAE,OAAG,CAAC,KAAK,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,GAAGA,IAAE,gBAAgB,GAAE,GAAGE,IAAE,+BAA+B,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACF,IAAEC,OAAI;AAAC,WAAK,gBAAcD,IAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,YAAU,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,UAAQ,GAAG,UAAU,SAAQ,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,SAAK,kBAAgBF,IAAE,KAAK,eAAaC,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,QAAO;AAA1+7H,QAAAS,KAAA;AAA2+7H,KAAAA,MAAA,KAAK,oBAAL,gBAAAA,IAAsB,QAAS,CAAAX,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC,KAAI,UAAK,iBAAL,mBAAmB;AAAA,EAAO;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,CAAC,GAAE,IAAG,EAAE;AAA7D,IAA+D,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAAnF,IAAqF,KAAG,CAAC,GAAE,EAAE;AAA7F,IAA+F,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA7G,IAA+G,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA3J,IAA6J,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA3K,IAA6K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzN,IAA2N,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvQ,IAAyQ,KAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,EAAE;AAAvS,IAAyS,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,gBAAe,KAAE,GAAE,KAAK,qBAAmB,OAAG,KAAK,wBAAsB,MAAG,KAAK,IAAE,IAAI,MAAG,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAGD,KAAE,KAAK,GAAE,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,wBAAuBA,OAAI,KAAK,qBAAmBA,GAAE,sBAAoB,QAAI,2BAA0BA,OAAI,KAAK,wBAAsBA,GAAE,yBAAuB,OAAI,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,SAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,KAAK,gBAAc,KAAK,eAAa,KAAK,kBAAgB,QAAOD,KAAE,KAAK,IAAE,GAAEC,KAAE,IAAI;AAAG,UAAME,KAAE,IAAI;AAAG,QAAIG,KAAE,IAAI;AAAG,QAAG,GAAGA,IAAE,GAAE,GAAG,GAAE,GAAGH,IAAE,GAAE,IAAGG,EAAC,GAAEP,GAAE,YAAUA,GAAE;AAAS,YAAM,MAAM,4CAA4C;AAAE,QAAGA,GAAE,UAAS;AAAC,UAAIQ,KAAE,IAAI;AAAG,SAAGA,IAAE,GAAE,IAAE,GAAE,GAAGA,IAAE,GAAER,GAAE,SAAS,CAAC,GAAE,GAAGQ,IAAE,GAAER,GAAE,SAAS,CAAC,GAAE,GAAGI,IAAE,GAAE,IAAGI,EAAC;AAAA,IAAC,OAAK;AAAC,UAAG,CAACR,GAAE;AAAS,cAAM,MAAM,+CAA+C;AAAE,WAAIQ,OAAKD,KAAE,IAAI,MAAGP,GAAE;AAAU,WAAGA,KAAE,IAAI,MAAG,GAAE,IAAE,GAAE,GAAGA,IAAE,GAAEQ,GAAE,CAAC,GAAE,GAAGR,IAAE,GAAEQ,GAAE,CAAC,GAAE,GAAGD,IAAE,GAAE,IAAGP,EAAC;AAAE,SAAGI,IAAE,IAAG,IAAGG,EAAC;AAAA,IAAC;AAAC,OAAGL,IAAE,GAAE,IAAGE,EAAC,GAAE,KAAK,EAAE,iBAAiBF,GAAE,EAAE,GAAE,sBAAqB,UAASD,EAAC,GAAE,GAAG,MAAKF,IAAEI,EAAC;AAAE,OAAE;AAAC,UAAG;AAAC,cAAMJ,KAAE,IAAI,GAAG,KAAK,iBAAgB,KAAK,cAAa,KAAK,aAAa;AAAE,YAAG,CAAC,KAAK,GAAE;AAAC,cAAIM,KAAEN;AAAE,gBAAM;AAAA,QAAC;AAAC,aAAK,EAAEA,EAAC;AAAA,MAAC,UAAC;AAAQ,WAAG,IAAI;AAAA,MAAC;AAAC,MAAAM,KAAE;AAAA,IAAM;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIN,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,QAAQ,GAAE,GAAGA,IAAE,cAAc;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wEAAwE,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,YAAY,GAAE,GAAGA,IAAE,wBAAwB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,GAAG,MAAKF,EAAC,GAAE,KAAK,0BAAwB,GAAGA,IAAE,kBAAkB,GAAE,GAAGE,IAAE,mCAAmC,GAAE,GAAG,MAAK,kBAAkB,GAAE,KAAK,EAAE,GAAG,oBAAoB,CAACF,IAAEC,OAAI;AAAC,WAAK,kBAAgBD,GAAE,IAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,CAAE,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,oBAAoB,CAAAD,OAAG;AAAC,WAAK,kBAAgB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,uBAAqB,GAAGA,IAAE,eAAe,GAAE,GAAGE,IAAE,6BAA6B,GAAE,GAAG,MAAK,eAAe,GAAE,KAAK,EAAE,EAAE,iBAAiB,CAACF,IAAEC,OAAI;AAAC,WAAK,eAAa,GAAG,MAAKD,IAAE,OAAG,CAAC,KAAK,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,GAAGA,IAAE,gBAAgB,GAAE,GAAGE,IAAE,+BAA+B,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACF,IAAEC,OAAI;AAAC,WAAK,gBAAcD,IAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,UAAQ,GAAG,UAAU,SAAQ,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,mBAAkB,aAAY,KAAE,GAAE,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,WAASA,GAAE,qBAAmB,GAAG,KAAK,GAAE,GAAE,GAAGA,GAAE,kBAAkB,CAAC,IAAE,wBAAuBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,aAAW,GAAG,KAAK,GAAE,GAAEA,GAAE,UAAU,IAAE,gBAAeA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,iBAAe,GAAG,KAAK,GAAE,GAAEA,GAAE,cAAc,IAAE,oBAAmBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,oBAAkB,GAAG,KAAK,GAAE,GAAEA,GAAE,iBAAiB,IAAE,uBAAsBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,mBAAiB,GAAG,KAAK,GAAE,GAAEA,GAAE,gBAAgB,IAAE,sBAAqBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,iBAAiB,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4CAA4C,GAAE,GAAGA,IAAE,uBAAuB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD;AAAE,QAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,WAAW,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,eAAeA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,SAAK,YAAUF,IAAE,KAAK,iBAAeC,IAAE,KAAK,oBAAkBC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAxrmI,QAAAS;AAAyrmI,KAAAA,MAAA,KAAK,sBAAL,gBAAAA,IAAwB,QAAS,CAAAX,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC;AAAA,EAAG;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,EAAAA,GAAE,YAAU,CAAC,GAAEA,GAAE,iBAAe,CAAC,GAAEA,GAAE,oBAAkB;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,UAAMC,KAAE,IAAI,GAAGD,GAAE,WAAUA,GAAE,gBAAeA,GAAE,iBAAiB;AAAE,QAAG,CAACA,GAAE;AAAE,aAAOC;AAAE,IAAAD,GAAE,EAAEC,EAAC;AAAA,EAAC,UAAC;AAAQ,OAAGD,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,0BAAwB,OAAG,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,6BAA4BA,OAAI,KAAK,0BAAwBA,GAAE,2BAAyB,QAAI,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEG,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKH,IAAEI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,iBAAiB,GAAE,GAAGA,IAAE,oBAAoB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,qCAAqC,GAAE,GAAGA,IAAE,iCAAiC,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,GAAG,MAAKF,EAAC,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAACA,IAAEC,OAAI;AAAC,WAAK,YAAU,CAAC;AAAE,iBAAUA,MAAKD;AAAE,QAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,UAAU,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAD,OAAG;AAAC,WAAK,YAAU,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAmB,CAACA,IAAEC,OAAI;AAAC,WAAK,iBAAe,CAAC;AAAE,iBAAUA,MAAKD;AAAE,QAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,eAAe,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAmB,CAAAD,OAAG;AAAC,WAAK,iBAAe,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,4BAA0B,GAAGE,IAAE,sCAAsC,GAAE,GAAG,MAAK,oBAAoB,GAAE,KAAK,EAAE,GAAG,sBAAsB,CAACF,IAAEC,OAAI;AAAC,WAAK,oBAAkBD,GAAE,IAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,CAAE,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,sBAAsB,CAAAD,OAAG;AAAC,WAAK,oBAAkB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAGA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB;", "names": ["t", "e", "n", "r", "i", "s", "c", "h", "o", "a", "u", "_a", "_c", "_e"]}