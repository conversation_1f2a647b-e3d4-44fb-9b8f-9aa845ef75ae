{"name": "file-selector", "version": "0.5.0", "description": "Convert DataTransfer object to a list of File objects", "main": "./dist/index.js", "module": "./dist/es5/index.js", "es2015": "./dist/es2015/index.js", "typings": "./dist/index.d.ts", "files": ["dist/**/*", "src/*", "!*.spec.*", "LICENSE"], "keywords": ["drag-and-drop", "html5", "file-api", "DataTransfer", "File"], "homepage": "https://github.com/react-dropzone/file-selector", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/react-dropzone/file-selector.git"}, "scripts": {"prebuild": "yarn run clean", "build": "npm-run-all -s compile build:umd", "build:umd": "rollup -c ./rollup.config.js", "compile": "npm-run-all -p compile:es2015 compile:es5 compile:cjs compile:types", "compile:es2015": "tsc -p ./tsconfig.es2015.json", "compile:es5": "tsc -p ./tsconfig.es5.json", "compile:cjs": "tsc -p ./tsconfig.cjs.json", "compile:types": "tsc -p ./tsconfig.types.json", "clean": "rm -rf dist/*", "lint": "tslint -c tslint.json -p ./tsconfig.spec.json -t stylish", "lint:fix": "yarn run lint -- --fix", "pretest:cov": "yarn run lint", "test:cov": "jest --coverage", "test": "jest --watch"}, "dependencies": {"tslib": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.12.3", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "babel-jest": "^26.6.0", "camelcase": "^6.1.0", "jest": "^26.6.0", "jest-environment-jsdom": "^26.6.0", "npm-run-all": "^4.1.5", "rollup": "^2.32.1", "rollup-plugin-sourcemaps": "^0.6.3", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^26.4.1", "tslint": "^6.1.3", "typescript": "^4.0.3"}, "engines": {"node": ">= 10"}}