# Image-to-Avatar Integration

This project now includes image-to-avatar functionality that allows users to upload a photo and convert it into a custom 3D avatar while maintaining all existing lip-sync and animation capabilities.

## Features Added

### 🖼️ Image Upload & Processing
- **Drag & Drop Interface**: Easy-to-use image upload with drag and drop support
- **Human Detection**: Automatic detection of humans in uploaded images using MediaPipe
- **Smart Cropping**: Intelligent cropping based on shoulder width and pose landmarks
- **Fallback Processing**: Center-crop fallback when MediaPipe is unavailable

### 🎭 Avatar Customization
- **Dynamic Texture Loading**: Uploaded images are converted to 3D avatar textures
- **Seamless Integration**: Custom avatars work with existing lip-sync and animations
- **Avatar Switching**: Easy switching between default and custom avatars
- **Persistent Storage**: Custom avatars are saved in localStorage

### 🎬 Maintained Functionality
- **Full Lip-Sync**: Custom avatars maintain complete lip-sync capabilities
- **All Animations**: Idle, Greeting, and Angry animations work with custom avatars
- **Chatbot Integration**: All existing chatbot features remain unchanged
- **Voice Synthesis**: Text-to-speech functionality works with custom avatars

## How to Use

### 1. Upload Your Photo
1. Click on the "Upload Photo" option in the avatar selection panel
2. Drag and drop an image or click to select from your computer
3. Wait for the processing to complete

### 2. Best Results Tips
- Use clear photos with good lighting
- Person should be facing the camera
- Arms should be at the sides
- Full upper body should be visible
- Avoid busy backgrounds when possible

### 3. Avatar Selection
- **Default**: Use the original 3D avatar model
- **Custom**: Use your uploaded photo as the avatar texture

## Technical Implementation

### Components Added
- `ImageUpload.jsx`: Handles file upload and user interface
- `AvatarSelection.jsx`: Manages avatar type selection
- `avatarProcessor.js`: Processes images and creates avatar textures

### Key Technologies
- **MediaPipe**: Human pose detection and landmark extraction
- **Canvas API**: Image processing and texture generation
- **React Dropzone**: File upload interface
- **Three.js**: 3D texture application

### Processing Pipeline
1. **Image Upload**: User selects an image file
2. **Human Detection**: MediaPipe detects pose landmarks
3. **Smart Cropping**: Calculate optimal crop area based on shoulders
4. **Texture Creation**: Generate texture suitable for 3D avatar
5. **Material Application**: Apply texture to 3D model head

### Fallback Mode
If MediaPipe fails to load:
- Uses simple center-crop processing
- Still creates functional avatar textures
- Maintains all other functionality

## File Structure

```
src/
├── components/
│   ├── Avatar.jsx          # Modified to support custom textures
│   ├── AvatarSelection.jsx # New: Avatar type selection
│   ├── ImageUpload.jsx     # New: Image upload interface
│   ├── Experience.jsx      # Modified to pass custom avatar data
│   └── Chatbot.jsx         # Unchanged
├── services/
│   └── avatarProcessor.js  # New: Image processing service
└── App.jsx                 # Modified to integrate avatar selection
```

## Dependencies Added
- `@mediapipe/tasks-vision`: Human pose detection
- `react-dropzone`: File upload interface

## Storage
- Custom avatars are automatically saved to localStorage
- Avatar selection persists between sessions
- No server-side storage required

## Error Handling
- Graceful fallback when MediaPipe is unavailable
- User-friendly error messages for invalid images
- Automatic retry mechanisms for failed processing

## Performance
- Client-side processing (no server required)
- Efficient texture loading and caching
- Minimal impact on existing functionality

## Browser Compatibility
- Works in all modern browsers
- Progressive enhancement (fallback for older browsers)
- No additional plugins required
