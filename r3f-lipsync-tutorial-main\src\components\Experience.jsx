import { Environment, OrbitControls, useTexture } from "@react-three/drei";
import { useThree } from "@react-three/fiber";
import React, { useRef, useEffect } from "react";
import { Avatar } from "./Avatar";
import "../chatbot.css";

// This component only contains the 3D scene elements
export const Experience = ({ avatarRef, customAvatar }) => {
  const texture = useTexture("textures/youtubeBackground.jpg");
  const viewport = useThree((state) => state.viewport);

  return (
    <>
      <OrbitControls />
      <Avatar
        position={[0, -3, 5]}
        scale={2}
        avatarRef={avatarRef}
        customAvatar={customAvatar}
      />
      <Environment preset="sunset" />
      <mesh>
        <planeGeometry args={[viewport.width, viewport.height]} />
        <meshBasicMaterial map={texture} />
      </mesh>
    </>
  );
};
