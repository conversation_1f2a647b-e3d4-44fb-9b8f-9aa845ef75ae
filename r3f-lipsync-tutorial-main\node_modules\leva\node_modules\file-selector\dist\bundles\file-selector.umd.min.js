!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).fileSelector={})}(this,(function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */function e(t,e,n,i){return new(n||(n=Promise))((function(o,r){function a(t){try{p(i.next(t))}catch(t){r(t)}}function c(t){try{p(i.throw(t))}catch(t){r(t)}}function p(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}p((i=i.apply(t,e||[])).next())}))}function n(t,e){var n,i,o,r,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function c(r){return function(c){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(o=2&r[0]?i.return:r[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,r[1])).done)return o;switch(i=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,i=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){a.label=r[1];break}if(6===r[0]&&a.label<o[1]){a.label=o[1],o=r;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(r);break}o[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(t){r=[6,t],i=0}finally{n=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,c])}}}function i(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var i,o,r=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(t){o={error:t}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return a}var o=new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]);function r(t,e){var n=function(t){var e=t.name;if(e&&-1!==e.lastIndexOf(".")&&!t.type){var n=e.split(".").pop().toLowerCase(),i=o.get(n);i&&Object.defineProperty(t,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return t}(t);if("string"!=typeof n.path){var i=t.webkitRelativePath;Object.defineProperty(n,"path",{value:"string"==typeof e?e:"string"==typeof i&&i.length>0?i:t.name,writable:!1,configurable:!1,enumerable:!0})}return n}var a=[".DS_Store","Thumbs.db"];function c(t){return"object"==typeof t&&null!==t}function p(t){return s(t.target.files).map((function(t){return r(t)}))}function u(t){return e(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return[4,Promise.all(t.map((function(t){return t.getFile()})))];case 1:return[2,e.sent().map((function(t){return r(t)}))]}}))}))}function l(t,i){return e(this,void 0,void 0,(function(){var e;return n(this,(function(n){switch(n.label){case 0:return null===t?[2,[]]:t.items?(e=s(t.items).filter((function(t){return"file"===t.kind})),"drop"!==i?[2,e]:[4,Promise.all(e.map(d))]):[3,2];case 1:return[2,f(m(n.sent()))];case 2:return[2,f(s(t.files).map((function(t){return r(t)})))]}}))}))}function f(t){return t.filter((function(t){return-1===a.indexOf(t.name)}))}function s(t){if(null===t)return[];for(var e=[],n=0;n<t.length;n++){var i=t[n];e.push(i)}return e}function d(t){if("function"!=typeof t.webkitGetAsEntry)return v(t);var e=t.webkitGetAsEntry();return e&&e.isDirectory?g(e):v(t)}function m(t){return t.reduce((function(t,e){return function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t}(t,Array.isArray(e)?m(e):[e])}),[])}function v(t){var e=t.getAsFile();if(!e)return Promise.reject(t+" is not a File");var n=r(e);return Promise.resolve(n)}function h(t){return e(this,void 0,void 0,(function(){return n(this,(function(e){return[2,t.isDirectory?g(t):b(t)]}))}))}function g(t){var i=t.createReader();return new Promise((function(t,o){var r=[];!function a(){var c=this;i.readEntries((function(i){return e(c,void 0,void 0,(function(){var e,c,p;return n(this,(function(n){switch(n.label){case 0:if(i.length)return[3,5];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,Promise.all(r)];case 2:return e=n.sent(),t(e),[3,4];case 3:return c=n.sent(),o(c),[3,4];case 4:return[3,6];case 5:p=Promise.all(i.map(h)),r.push(p),a(),n.label=6;case 6:return[2]}}))}))}),(function(t){o(t)}))}()}))}function b(t){return e(this,void 0,void 0,(function(){return n(this,(function(e){return[2,new Promise((function(e,n){t.file((function(n){var i=r(n,t.fullPath);e(i)}),(function(t){n(t)}))}))]}))}))}t.fromEvent=function(t){return e(this,void 0,void 0,(function(){return n(this,(function(e){return c(t)&&c(t.dataTransfer)?[2,l(t.dataTransfer,t.type)]:function(t){return c(t)&&c(t.target)}(t)?[2,p(t)]:Array.isArray(t)&&t.every((function(t){return"getFile"in t&&"function"==typeof t.getFile}))?[2,u(t)]:[2,[]]}))}))},Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=file-selector.umd.min.js.map
