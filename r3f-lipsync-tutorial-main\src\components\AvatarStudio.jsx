import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment, OrbitControls } from '@react-three/drei';
import { Avatar } from './Avatar';
import Face3DReconstructor from './Face3DReconstructor';
import Chatbot from './Chatbot';

/**
 * Avatar Studio - Main component that combines all avatar features
 * Including 3D face reconstruction, custom avatars, and chatbot integration
 */
const AvatarStudio = () => {
  const [reconstructed3DFace, setReconstructed3DFace] = useState(null);
  const [customAvatar, setCustomAvatar] = useState(null);
  const [activeTab, setActiveTab] = useState('avatar');

  const handleFaceGenerated = (faceMesh, reconstructionResult) => {
    console.log('3D face generated:', faceMesh);
    setReconstructed3DFace(faceMesh);
  };

  const handleCustomAvatarGenerated = (avatarData) => {
    console.log('Custom avatar generated:', avatarData);
    setCustomAvatar(avatarData);
  };

  return (
    <div className="avatar-studio">
      <div className="studio-header">
        <h1>Avatar Studio</h1>
        <p>Create and customize your 3D avatar with advanced AI features</p>
      </div>

      <div className="studio-tabs">
        <button 
          className={`tab ${activeTab === 'avatar' ? 'active' : ''}`}
          onClick={() => setActiveTab('avatar')}
        >
          Avatar View
        </button>
        <button 
          className={`tab ${activeTab === 'reconstruction' ? 'active' : ''}`}
          onClick={() => setActiveTab('reconstruction')}
        >
          3D Face Reconstruction
        </button>
        <button 
          className={`tab ${activeTab === 'chat' ? 'active' : ''}`}
          onClick={() => setActiveTab('chat')}
        >
          AI Chat
        </button>
      </div>

      <div className="studio-content">
        {activeTab === 'avatar' && (
          <div className="avatar-section">
            <div className="avatar-canvas">
              <Canvas camera={{ position: [0, 0, 5], fov: 30 }}>
                <color attach="background" args={["#ececec"]} />
                <Environment preset="sunset" />
                <OrbitControls 
                  enablePan={false}
                  enableZoom={true}
                  enableRotate={true}
                  minDistance={3}
                  maxDistance={8}
                  minPolarAngle={Math.PI / 6}
                  maxPolarAngle={Math.PI - Math.PI / 6}
                />
                <Avatar 
                  customAvatar={customAvatar}
                  reconstructed3DFace={reconstructed3DFace}
                  position={[0, -3, 0]}
                />
              </Canvas>
            </div>
            
            <div className="avatar-info">
              <h3>Avatar Status</h3>
              <div className="status-item">
                <span className="status-label">Custom Avatar:</span>
                <span className={`status-value ${customAvatar ? 'active' : 'inactive'}`}>
                  {customAvatar ? 'Loaded' : 'Not loaded'}
                </span>
              </div>
              <div className="status-item">
                <span className="status-label">3D Face:</span>
                <span className={`status-value ${reconstructed3DFace ? 'active' : 'inactive'}`}>
                  {reconstructed3DFace ? 'Generated' : 'Not generated'}
                </span>
              </div>
              
              {reconstructed3DFace && (
                <div className="face-info">
                  <h4>3D Face Details</h4>
                  <p>✓ Face mesh generated</p>
                  <p>✓ Texture applied</p>
                  <p>✓ Ready for animation</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'reconstruction' && (
          <div className="reconstruction-section">
            <Face3DReconstructor onFaceGenerated={handleFaceGenerated} />
            
            <div className="reconstruction-info">
              <h3>How 3D Face Reconstruction Works</h3>
              <div className="info-steps">
                <div className="step">
                  <div className="step-number">1</div>
                  <div className="step-content">
                    <h4>Face Detection</h4>
                    <p>MediaPipe detects 468 facial landmarks in your photo</p>
                  </div>
                </div>
                <div className="step">
                  <div className="step-number">2</div>
                  <div className="step-content">
                    <h4>3D Mapping</h4>
                    <p>Landmarks are mapped to 3D space using depth estimation</p>
                  </div>
                </div>
                <div className="step">
                  <div className="step-number">3</div>
                  <div className="step-content">
                    <h4>Mesh Generation</h4>
                    <p>A 3D face mesh is created with proper topology</p>
                  </div>
                </div>
                <div className="step">
                  <div className="step-number">4</div>
                  <div className="step-content">
                    <h4>Texture Mapping</h4>
                    <p>Your photo is mapped onto the 3D mesh as texture</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'chat' && (
          <div className="chat-section">
            <Chatbot />
            <div className="chat-info">
              <h3>AI Chat Features</h3>
              <ul>
                <li>Real-time lip sync with speech</li>
                <li>Emotion-based animations</li>
                <li>Natural conversation flow</li>
                <li>Voice synthesis integration</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .avatar-studio {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }

        .studio-header {
          text-align: center;
          color: white;
          margin-bottom: 30px;
        }

        .studio-header h1 {
          font-size: 2.5rem;
          margin-bottom: 10px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .studio-header p {
          font-size: 1.1rem;
          opacity: 0.9;
        }

        .studio-tabs {
          display: flex;
          justify-content: center;
          gap: 10px;
          margin-bottom: 30px;
        }

        .tab {
          padding: 12px 24px;
          border: none;
          border-radius: 25px;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;
          font-weight: 500;
        }

        .tab:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        .tab.active {
          background: white;
          color: #667eea;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .studio-content {
          background: white;
          border-radius: 20px;
          padding: 30px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          min-height: 600px;
        }

        .avatar-section {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 30px;
          height: 600px;
        }

        .avatar-canvas {
          border-radius: 15px;
          overflow: hidden;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .avatar-info {
          padding: 20px;
          background: #f8f9fa;
          border-radius: 15px;
        }

        .avatar-info h3 {
          color: #333;
          margin-bottom: 20px;
          font-size: 1.3rem;
        }

        .status-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #e0e0e0;
        }

        .status-label {
          font-weight: 500;
          color: #666;
        }

        .status-value {
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 0.9rem;
          font-weight: 500;
        }

        .status-value.active {
          background: #e8f5e8;
          color: #2e7d32;
        }

        .status-value.inactive {
          background: #ffebee;
          color: #c62828;
        }

        .face-info {
          margin-top: 20px;
          padding: 15px;
          background: #e3f2fd;
          border-radius: 10px;
        }

        .face-info h4 {
          color: #1976d2;
          margin-bottom: 10px;
        }

        .face-info p {
          color: #1976d2;
          margin: 5px 0;
          font-size: 0.9rem;
        }

        .reconstruction-section {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
        }

        .reconstruction-info h3 {
          color: #333;
          margin-bottom: 20px;
        }

        .info-steps {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .step {
          display: flex;
          align-items: flex-start;
          gap: 15px;
        }

        .step-number {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background: #667eea;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          flex-shrink: 0;
        }

        .step-content h4 {
          color: #333;
          margin-bottom: 5px;
        }

        .step-content p {
          color: #666;
          font-size: 0.9rem;
        }

        .chat-section {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 30px;
        }

        .chat-info {
          padding: 20px;
          background: #f8f9fa;
          border-radius: 15px;
        }

        .chat-info h3 {
          color: #333;
          margin-bottom: 15px;
        }

        .chat-info ul {
          list-style: none;
          padding: 0;
        }

        .chat-info li {
          padding: 8px 0;
          color: #666;
          position: relative;
          padding-left: 20px;
        }

        .chat-info li::before {
          content: "✓";
          position: absolute;
          left: 0;
          color: #4caf50;
          font-weight: bold;
        }

        @media (max-width: 768px) {
          .avatar-section,
          .reconstruction-section,
          .chat-section {
            grid-template-columns: 1fr;
          }
          
          .studio-tabs {
            flex-direction: column;
            align-items: center;
          }
        }
      `}</style>
    </div>
  );
};

export default AvatarStudio;
