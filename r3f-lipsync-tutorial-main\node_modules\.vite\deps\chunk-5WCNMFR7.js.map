{"version": 3, "sources": ["../../@use-gesture/core/dist/maths-0ab39ae9.esm.js", "../../@use-gesture/core/dist/actions-76b8683e.esm.js", "../../@use-gesture/react/dist/use-gesture-react.esm.js", "../../@use-gesture/core/dist/use-gesture-core.esm.js", "../../zustand/esm/middleware.js", "../../zustand/esm/shallow.js"], "sourcesContent": ["function clamp(v, min, max) {\n  return Math.max(min, Math.min(v, max));\n}\nconst V = {\n  toVector(v, fallback) {\n    if (v === undefined) v = fallback;\n    return Array.isArray(v) ? v : [v, v];\n  },\n  add(v1, v2) {\n    return [v1[0] + v2[0], v1[1] + v2[1]];\n  },\n  sub(v1, v2) {\n    return [v1[0] - v2[0], v1[1] - v2[1]];\n  },\n  addTo(v1, v2) {\n    v1[0] += v2[0];\n    v1[1] += v2[1];\n  },\n  subTo(v1, v2) {\n    v1[0] -= v2[0];\n    v1[1] -= v2[1];\n  }\n};\nfunction rubberband(distance, dimension, constant) {\n  if (dimension === 0 || Math.abs(dimension) === Infinity) return Math.pow(distance, constant * 5);\n  return distance * dimension * constant / (dimension + constant * distance);\n}\nfunction rubberbandIfOutOfBounds(position, min, max, constant = 0.15) {\n  if (constant === 0) return clamp(position, min, max);\n  if (position < min) return -rubberband(min - position, max - min, constant) + min;\n  if (position > max) return +rubberband(position - max, max - min, constant) + max;\n  return position;\n}\nfunction computeRubberband(bounds, [Vx, Vy], [Rx, Ry]) {\n  const [[X0, X1], [Y0, Y1]] = bounds;\n  return [rubberbandIfOutOfBounds(Vx, X0, X1, Rx), rubberbandIfOutOfBounds(Vy, Y0, Y1, Ry)];\n}\n\nexport { V, computeRubberband as c, rubberbandIfOutOfBounds as r };\n", "import { V, c as computeRubberband } from './maths-0ab39ae9.esm.js';\n\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\n\nconst EVENT_TYPE_MAP = {\n  pointer: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  mouse: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  touch: {\n    start: 'start',\n    change: 'move',\n    end: 'end'\n  },\n  gesture: {\n    start: 'start',\n    change: 'change',\n    end: 'end'\n  }\n};\nfunction capitalize(string) {\n  if (!string) return '';\n  return string[0].toUpperCase() + string.slice(1);\n}\nconst actionsWithoutCaptureSupported = ['enter', 'leave'];\nfunction hasCapture(capture = false, actionKey) {\n  return capture && !actionsWithoutCaptureSupported.includes(actionKey);\n}\nfunction toHandlerProp(device, action = '', capture = false) {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return 'on' + capitalize(device) + capitalize(actionKey) + (hasCapture(capture, actionKey) ? 'Capture' : '');\n}\nconst pointerCaptureEvents = ['gotpointercapture', 'lostpointercapture'];\nfunction parseProp(prop) {\n  let eventKey = prop.substring(2).toLowerCase();\n  const passive = !!~eventKey.indexOf('passive');\n  if (passive) eventKey = eventKey.replace('passive', '');\n  const captureKey = pointerCaptureEvents.includes(eventKey) ? 'capturecapture' : 'capture';\n  const capture = !!~eventKey.indexOf(captureKey);\n  if (capture) eventKey = eventKey.replace('capture', '');\n  return {\n    device: eventKey,\n    capture,\n    passive\n  };\n}\nfunction toDomEventType(device, action = '') {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return device + actionKey;\n}\nfunction isTouch(event) {\n  return 'touches' in event;\n}\nfunction getPointerType(event) {\n  if (isTouch(event)) return 'touch';\n  if ('pointerType' in event) return event.pointerType;\n  return 'mouse';\n}\nfunction getCurrentTargetTouchList(event) {\n  return Array.from(event.touches).filter(e => {\n    var _event$currentTarget, _event$currentTarget$;\n    return e.target === event.currentTarget || ((_event$currentTarget = event.currentTarget) === null || _event$currentTarget === void 0 ? void 0 : (_event$currentTarget$ = _event$currentTarget.contains) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.call(_event$currentTarget, e.target));\n  });\n}\nfunction getTouchList(event) {\n  return event.type === 'touchend' || event.type === 'touchcancel' ? event.changedTouches : event.targetTouches;\n}\nfunction getValueEvent(event) {\n  return isTouch(event) ? getTouchList(event)[0] : event;\n}\nfunction distanceAngle(P1, P2) {\n  try {\n    const dx = P2.clientX - P1.clientX;\n    const dy = P2.clientY - P1.clientY;\n    const cx = (P2.clientX + P1.clientX) / 2;\n    const cy = (P2.clientY + P1.clientY) / 2;\n    const distance = Math.hypot(dx, dy);\n    const angle = -(Math.atan2(dx, dy) * 180) / Math.PI;\n    const origin = [cx, cy];\n    return {\n      angle,\n      distance,\n      origin\n    };\n  } catch (_unused) {}\n  return null;\n}\nfunction touchIds(event) {\n  return getCurrentTargetTouchList(event).map(touch => touch.identifier);\n}\nfunction touchDistanceAngle(event, ids) {\n  const [P1, P2] = Array.from(event.touches).filter(touch => ids.includes(touch.identifier));\n  return distanceAngle(P1, P2);\n}\nfunction pointerId(event) {\n  const valueEvent = getValueEvent(event);\n  return isTouch(event) ? valueEvent.identifier : valueEvent.pointerId;\n}\nfunction pointerValues(event) {\n  const valueEvent = getValueEvent(event);\n  return [valueEvent.clientX, valueEvent.clientY];\n}\nconst LINE_HEIGHT = 40;\nconst PAGE_HEIGHT = 800;\nfunction wheelValues(event) {\n  let {\n    deltaX,\n    deltaY,\n    deltaMode\n  } = event;\n  if (deltaMode === 1) {\n    deltaX *= LINE_HEIGHT;\n    deltaY *= LINE_HEIGHT;\n  } else if (deltaMode === 2) {\n    deltaX *= PAGE_HEIGHT;\n    deltaY *= PAGE_HEIGHT;\n  }\n  return [deltaX, deltaY];\n}\nfunction scrollValues(event) {\n  var _ref, _ref2;\n  const {\n    scrollX,\n    scrollY,\n    scrollLeft,\n    scrollTop\n  } = event.currentTarget;\n  return [(_ref = scrollX !== null && scrollX !== void 0 ? scrollX : scrollLeft) !== null && _ref !== void 0 ? _ref : 0, (_ref2 = scrollY !== null && scrollY !== void 0 ? scrollY : scrollTop) !== null && _ref2 !== void 0 ? _ref2 : 0];\n}\nfunction getEventDetails(event) {\n  const payload = {};\n  if ('buttons' in event) payload.buttons = event.buttons;\n  if ('shiftKey' in event) {\n    const {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    } = event;\n    Object.assign(payload, {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    });\n  }\n  return payload;\n}\n\nfunction call(v, ...args) {\n  if (typeof v === 'function') {\n    return v(...args);\n  } else {\n    return v;\n  }\n}\nfunction noop() {}\nfunction chain(...fns) {\n  if (fns.length === 0) return noop;\n  if (fns.length === 1) return fns[0];\n  return function () {\n    let result;\n    for (const fn of fns) {\n      result = fn.apply(this, arguments) || result;\n    }\n    return result;\n  };\n}\nfunction assignDefault(value, fallback) {\n  return Object.assign({}, fallback, value || {});\n}\n\nconst BEFORE_LAST_KINEMATICS_DELAY = 32;\nclass Engine {\n  constructor(ctrl, args, key) {\n    this.ctrl = ctrl;\n    this.args = args;\n    this.key = key;\n    if (!this.state) {\n      this.state = {};\n      this.computeValues([0, 0]);\n      this.computeInitial();\n      if (this.init) this.init();\n      this.reset();\n    }\n  }\n  get state() {\n    return this.ctrl.state[this.key];\n  }\n  set state(state) {\n    this.ctrl.state[this.key] = state;\n  }\n  get shared() {\n    return this.ctrl.state.shared;\n  }\n  get eventStore() {\n    return this.ctrl.gestureEventStores[this.key];\n  }\n  get timeoutStore() {\n    return this.ctrl.gestureTimeoutStores[this.key];\n  }\n  get config() {\n    return this.ctrl.config[this.key];\n  }\n  get sharedConfig() {\n    return this.ctrl.config.shared;\n  }\n  get handler() {\n    return this.ctrl.handlers[this.key];\n  }\n  reset() {\n    const {\n      state,\n      shared,\n      ingKey,\n      args\n    } = this;\n    shared[ingKey] = state._active = state.active = state._blocked = state._force = false;\n    state._step = [false, false];\n    state.intentional = false;\n    state._movement = [0, 0];\n    state._distance = [0, 0];\n    state._direction = [0, 0];\n    state._delta = [0, 0];\n    state._bounds = [[-Infinity, Infinity], [-Infinity, Infinity]];\n    state.args = args;\n    state.axis = undefined;\n    state.memo = undefined;\n    state.elapsedTime = state.timeDelta = 0;\n    state.direction = [0, 0];\n    state.distance = [0, 0];\n    state.overflow = [0, 0];\n    state._movementBound = [false, false];\n    state.velocity = [0, 0];\n    state.movement = [0, 0];\n    state.delta = [0, 0];\n    state.timeStamp = 0;\n  }\n  start(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._active) {\n      this.reset();\n      this.computeInitial();\n      state._active = true;\n      state.target = event.target;\n      state.currentTarget = event.currentTarget;\n      state.lastOffset = config.from ? call(config.from, state) : state.offset;\n      state.offset = state.lastOffset;\n      state.startTime = state.timeStamp = event.timeStamp;\n    }\n  }\n  computeValues(values) {\n    const state = this.state;\n    state._values = values;\n    state.values = this.config.transform(values);\n  }\n  computeInitial() {\n    const state = this.state;\n    state._initial = state._values;\n    state.initial = state.values;\n  }\n  compute(event) {\n    const {\n      state,\n      config,\n      shared\n    } = this;\n    state.args = this.args;\n    let dt = 0;\n    if (event) {\n      state.event = event;\n      if (config.preventDefault && event.cancelable) state.event.preventDefault();\n      state.type = event.type;\n      shared.touches = this.ctrl.pointerIds.size || this.ctrl.touchIds.size;\n      shared.locked = !!document.pointerLockElement;\n      Object.assign(shared, getEventDetails(event));\n      shared.down = shared.pressed = shared.buttons % 2 === 1 || shared.touches > 0;\n      dt = event.timeStamp - state.timeStamp;\n      state.timeStamp = event.timeStamp;\n      state.elapsedTime = state.timeStamp - state.startTime;\n    }\n    if (state._active) {\n      const _absoluteDelta = state._delta.map(Math.abs);\n      V.addTo(state._distance, _absoluteDelta);\n    }\n    if (this.axisIntent) this.axisIntent(event);\n    const [_m0, _m1] = state._movement;\n    const [t0, t1] = config.threshold;\n    const {\n      _step,\n      values\n    } = state;\n    if (config.hasCustomTransform) {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && values[0];\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && values[1];\n    } else {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && Math.sign(_m0) * t0;\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && Math.sign(_m1) * t1;\n    }\n    state.intentional = _step[0] !== false || _step[1] !== false;\n    if (!state.intentional) return;\n    const movement = [0, 0];\n    if (config.hasCustomTransform) {\n      const [v0, v1] = values;\n      movement[0] = _step[0] !== false ? v0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? v1 - _step[1] : 0;\n    } else {\n      movement[0] = _step[0] !== false ? _m0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? _m1 - _step[1] : 0;\n    }\n    if (this.restrictToAxis && !state._blocked) this.restrictToAxis(movement);\n    const previousOffset = state.offset;\n    const gestureIsActive = state._active && !state._blocked || state.active;\n    if (gestureIsActive) {\n      state.first = state._active && !state.active;\n      state.last = !state._active && state.active;\n      state.active = shared[this.ingKey] = state._active;\n      if (event) {\n        if (state.first) {\n          if ('bounds' in config) state._bounds = call(config.bounds, state);\n          if (this.setup) this.setup();\n        }\n        state.movement = movement;\n        this.computeOffset();\n      }\n    }\n    const [ox, oy] = state.offset;\n    const [[x0, x1], [y0, y1]] = state._bounds;\n    state.overflow = [ox < x0 ? -1 : ox > x1 ? 1 : 0, oy < y0 ? -1 : oy > y1 ? 1 : 0];\n    state._movementBound[0] = state.overflow[0] ? state._movementBound[0] === false ? state._movement[0] : state._movementBound[0] : false;\n    state._movementBound[1] = state.overflow[1] ? state._movementBound[1] === false ? state._movement[1] : state._movementBound[1] : false;\n    const rubberband = state._active ? config.rubberband || [0, 0] : [0, 0];\n    state.offset = computeRubberband(state._bounds, state.offset, rubberband);\n    state.delta = V.sub(state.offset, previousOffset);\n    this.computeMovement();\n    if (gestureIsActive && (!state.last || dt > BEFORE_LAST_KINEMATICS_DELAY)) {\n      state.delta = V.sub(state.offset, previousOffset);\n      const absoluteDelta = state.delta.map(Math.abs);\n      V.addTo(state.distance, absoluteDelta);\n      state.direction = state.delta.map(Math.sign);\n      state._direction = state._delta.map(Math.sign);\n      if (!state.first && dt > 0) {\n        state.velocity = [absoluteDelta[0] / dt, absoluteDelta[1] / dt];\n        state.timeDelta = dt;\n      }\n    }\n  }\n  emit() {\n    const state = this.state;\n    const shared = this.shared;\n    const config = this.config;\n    if (!state._active) this.clean();\n    if ((state._blocked || !state.intentional) && !state._force && !config.triggerAllEvents) return;\n    const memo = this.handler(_objectSpread2(_objectSpread2(_objectSpread2({}, shared), state), {}, {\n      [this.aliasKey]: state.values\n    }));\n    if (memo !== undefined) state.memo = memo;\n  }\n  clean() {\n    this.eventStore.clean();\n    this.timeoutStore.clean();\n  }\n}\n\nfunction selectAxis([dx, dy], threshold) {\n  const absDx = Math.abs(dx);\n  const absDy = Math.abs(dy);\n  if (absDx > absDy && absDx > threshold) {\n    return 'x';\n  }\n  if (absDy > absDx && absDy > threshold) {\n    return 'y';\n  }\n  return undefined;\n}\nclass CoordinatesEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"aliasKey\", 'xy');\n  }\n  reset() {\n    super.reset();\n    this.state.axis = undefined;\n  }\n  init() {\n    this.state.offset = [0, 0];\n    this.state.lastOffset = [0, 0];\n  }\n  computeOffset() {\n    this.state.offset = V.add(this.state.lastOffset, this.state.movement);\n  }\n  computeMovement() {\n    this.state.movement = V.sub(this.state.offset, this.state.lastOffset);\n  }\n  axisIntent(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state.axis && event) {\n      const threshold = typeof config.axisThreshold === 'object' ? config.axisThreshold[getPointerType(event)] : config.axisThreshold;\n      state.axis = selectAxis(state._movement, threshold);\n    }\n    state._blocked = (config.lockDirection || !!config.axis) && !state.axis || !!config.axis && config.axis !== state.axis;\n  }\n  restrictToAxis(v) {\n    if (this.config.axis || this.config.lockDirection) {\n      switch (this.state.axis) {\n        case 'x':\n          v[1] = 0;\n          break;\n        case 'y':\n          v[0] = 0;\n          break;\n      }\n    }\n  }\n}\n\nconst identity = v => v;\nconst DEFAULT_RUBBERBAND = 0.15;\nconst commonConfigResolver = {\n  enabled(value = true) {\n    return value;\n  },\n  eventOptions(value, _k, config) {\n    return _objectSpread2(_objectSpread2({}, config.shared.eventOptions), value);\n  },\n  preventDefault(value = false) {\n    return value;\n  },\n  triggerAllEvents(value = false) {\n    return value;\n  },\n  rubberband(value = 0) {\n    switch (value) {\n      case true:\n        return [DEFAULT_RUBBERBAND, DEFAULT_RUBBERBAND];\n      case false:\n        return [0, 0];\n      default:\n        return V.toVector(value);\n    }\n  },\n  from(value) {\n    if (typeof value === 'function') return value;\n    if (value != null) return V.toVector(value);\n  },\n  transform(value, _k, config) {\n    const transform = value || config.shared.transform;\n    this.hasCustomTransform = !!transform;\n    if (process.env.NODE_ENV === 'development') {\n      const originalTransform = transform || identity;\n      return v => {\n        const r = originalTransform(v);\n        if (!isFinite(r[0]) || !isFinite(r[1])) {\n          console.warn(`[@use-gesture]: config.transform() must produce a valid result, but it was: [${r[0]},${[1]}]`);\n        }\n        return r;\n      };\n    }\n    return transform || identity;\n  },\n  threshold(value) {\n    return V.toVector(value, 0);\n  }\n};\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(commonConfigResolver, {\n    domTarget(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`domTarget\\` option has been renamed to \\`target\\`.`);\n      }\n      return NaN;\n    },\n    lockDirection(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`lockDirection\\` option has been merged with \\`axis\\`. Use it as in \\`{ axis: 'lock' }\\``);\n      }\n      return NaN;\n    },\n    initial(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`initial\\` option has been renamed to \\`from\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\n\nconst DEFAULT_AXIS_THRESHOLD = 0;\nconst coordinatesConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  axis(_v, _k, {\n    axis\n  }) {\n    this.lockDirection = axis === 'lock';\n    if (!this.lockDirection) return axis;\n  },\n  axisThreshold(value = DEFAULT_AXIS_THRESHOLD) {\n    return value;\n  },\n  bounds(value = {}) {\n    if (typeof value === 'function') {\n      return state => coordinatesConfigResolver.bounds(value(state));\n    }\n    if ('current' in value) {\n      return () => value.current;\n    }\n    if (typeof HTMLElement === 'function' && value instanceof HTMLElement) {\n      return value;\n    }\n    const {\n      left = -Infinity,\n      right = Infinity,\n      top = -Infinity,\n      bottom = Infinity\n    } = value;\n    return [[left, right], [top, bottom]];\n  }\n});\n\nconst KEYS_DELTA_MAP = {\n  ArrowRight: (displacement, factor = 1) => [displacement * factor, 0],\n  ArrowLeft: (displacement, factor = 1) => [-1 * displacement * factor, 0],\n  ArrowUp: (displacement, factor = 1) => [0, -1 * displacement * factor],\n  ArrowDown: (displacement, factor = 1) => [0, displacement * factor]\n};\nclass DragEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'dragging');\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._pointerId = undefined;\n    state._pointerActive = false;\n    state._keyboardActive = false;\n    state._preventScroll = false;\n    state._delayed = false;\n    state.swipe = [0, 0];\n    state.tap = false;\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n  }\n  setup() {\n    const state = this.state;\n    if (state._bounds instanceof HTMLElement) {\n      const boundRect = state._bounds.getBoundingClientRect();\n      const targetRect = state.currentTarget.getBoundingClientRect();\n      const _bounds = {\n        left: boundRect.left - targetRect.left + state.offset[0],\n        right: boundRect.right - targetRect.right + state.offset[0],\n        top: boundRect.top - targetRect.top + state.offset[1],\n        bottom: boundRect.bottom - targetRect.bottom + state.offset[1]\n      };\n      state._bounds = coordinatesConfigResolver.bounds(_bounds);\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    state.canceled = true;\n    state._active = false;\n    setTimeout(() => {\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  setActive() {\n    this.state._active = this.state._pointerActive || this.state._keyboardActive;\n  }\n  clean() {\n    this.pointerClean();\n    this.state._pointerActive = false;\n    this.state._keyboardActive = false;\n    super.clean();\n  }\n  pointerDown(event) {\n    const config = this.config;\n    const state = this.state;\n    if (event.buttons != null && (Array.isArray(config.pointerButtons) ? !config.pointerButtons.includes(event.buttons) : config.pointerButtons !== -1 && config.pointerButtons !== event.buttons)) return;\n    const ctrlIds = this.ctrl.setEventIds(event);\n    if (config.pointerCapture) {\n      event.target.setPointerCapture(event.pointerId);\n    }\n    if (ctrlIds && ctrlIds.size > 1 && state._pointerActive) return;\n    this.start(event);\n    this.setupPointer(event);\n    state._pointerId = pointerId(event);\n    state._pointerActive = true;\n    this.computeValues(pointerValues(event));\n    this.computeInitial();\n    if (config.preventScrollAxis && getPointerType(event) !== 'mouse') {\n      state._active = false;\n      this.setupScrollPrevention(event);\n    } else if (config.delay > 0) {\n      this.setupDelayTrigger(event);\n      if (config.triggerAllEvents) {\n        this.compute(event);\n        this.emit();\n      }\n    } else {\n      this.startPointerDrag(event);\n    }\n  }\n  startPointerDrag(event) {\n    const state = this.state;\n    state._active = true;\n    state._preventScroll = true;\n    state._delayed = false;\n    this.compute(event);\n    this.emit();\n  }\n  pointerMove(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    const _values = pointerValues(event);\n    if (document.pointerLockElement === event.target) {\n      state._delta = [event.movementX, event.movementY];\n    } else {\n      state._delta = V.sub(_values, state._values);\n      this.computeValues(_values);\n    }\n    V.addTo(state._movement, state._delta);\n    this.compute(event);\n    if (state._delayed && state.intentional) {\n      this.timeoutStore.remove('dragDelay');\n      state.active = false;\n      this.startPointerDrag(event);\n      return;\n    }\n    if (config.preventScrollAxis && !state._preventScroll) {\n      if (state.axis) {\n        if (state.axis === config.preventScrollAxis || config.preventScrollAxis === 'xy') {\n          state._active = false;\n          this.clean();\n          return;\n        } else {\n          this.timeoutStore.remove('startPointerDrag');\n          this.startPointerDrag(event);\n          return;\n        }\n      } else {\n        return;\n      }\n    }\n    this.emit();\n  }\n  pointerUp(event) {\n    this.ctrl.setEventIds(event);\n    try {\n      if (this.config.pointerCapture && event.target.hasPointerCapture(event.pointerId)) {\n        ;\n        event.target.releasePointerCapture(event.pointerId);\n      }\n    } catch (_unused) {\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(`[@use-gesture]: If you see this message, it's likely that you're using an outdated version of \\`@react-three/fiber\\`. \\n\\nPlease upgrade to the latest version.`);\n      }\n    }\n    const state = this.state;\n    const config = this.config;\n    if (!state._active || !state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    this.state._pointerActive = false;\n    this.setActive();\n    this.compute(event);\n    const [dx, dy] = state._distance;\n    state.tap = dx <= config.tapsThreshold && dy <= config.tapsThreshold;\n    if (state.tap && config.filterTaps) {\n      state._force = true;\n    } else {\n      const [_dx, _dy] = state._delta;\n      const [_mx, _my] = state._movement;\n      const [svx, svy] = config.swipe.velocity;\n      const [sx, sy] = config.swipe.distance;\n      const sdt = config.swipe.duration;\n      if (state.elapsedTime < sdt) {\n        const _vx = Math.abs(_dx / state.timeDelta);\n        const _vy = Math.abs(_dy / state.timeDelta);\n        if (_vx > svx && Math.abs(_mx) > sx) state.swipe[0] = Math.sign(_dx);\n        if (_vy > svy && Math.abs(_my) > sy) state.swipe[1] = Math.sign(_dy);\n      }\n    }\n    this.emit();\n  }\n  pointerClick(event) {\n    if (!this.state.tap && event.detail > 0) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  setupPointer(event) {\n    const config = this.config;\n    const device = config.device;\n    if (process.env.NODE_ENV === 'development') {\n      try {\n        if (device === 'pointer' && config.preventScrollDelay === undefined) {\n          const currentTarget = 'uv' in event ? event.sourceEvent.currentTarget : event.currentTarget;\n          const style = window.getComputedStyle(currentTarget);\n          if (style.touchAction === 'auto') {\n            console.warn(`[@use-gesture]: The drag target has its \\`touch-action\\` style property set to \\`auto\\`. It is recommended to add \\`touch-action: 'none'\\` so that the drag gesture behaves correctly on touch-enabled devices. For more information read this: https://use-gesture.netlify.app/docs/extras/#touch-action.\\n\\nThis message will only show in development mode. It won't appear in production. If this is intended, you can ignore it.`, currentTarget);\n          }\n        }\n      } catch (_unused2) {}\n    }\n    if (config.pointerLock) {\n      event.currentTarget.requestPointerLock();\n    }\n    if (!config.pointerCapture) {\n      this.eventStore.add(this.sharedConfig.window, device, 'change', this.pointerMove.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'end', this.pointerUp.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'cancel', this.pointerUp.bind(this));\n    }\n  }\n  pointerClean() {\n    if (this.config.pointerLock && document.pointerLockElement === this.state.currentTarget) {\n      document.exitPointerLock();\n    }\n  }\n  preventScroll(event) {\n    if (this.state._preventScroll && event.cancelable) {\n      event.preventDefault();\n    }\n  }\n  setupScrollPrevention(event) {\n    this.state._preventScroll = false;\n    persistEvent(event);\n    const remove = this.eventStore.add(this.sharedConfig.window, 'touch', 'change', this.preventScroll.bind(this), {\n      passive: false\n    });\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'end', remove);\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'cancel', remove);\n    this.timeoutStore.add('startPointerDrag', this.startPointerDrag.bind(this), this.config.preventScrollDelay, event);\n  }\n  setupDelayTrigger(event) {\n    this.state._delayed = true;\n    this.timeoutStore.add('dragDelay', () => {\n      this.state._step = [0, 0];\n      this.startPointerDrag(event);\n    }, this.config.delay);\n  }\n  keyDown(event) {\n    const deltaFn = KEYS_DELTA_MAP[event.key];\n    if (deltaFn) {\n      const state = this.state;\n      const factor = event.shiftKey ? 10 : event.altKey ? 0.1 : 1;\n      this.start(event);\n      state._delta = deltaFn(this.config.keyboardDisplacement, factor);\n      state._keyboardActive = true;\n      V.addTo(state._movement, state._delta);\n      this.compute(event);\n      this.emit();\n    }\n  }\n  keyUp(event) {\n    if (!(event.key in KEYS_DELTA_MAP)) return;\n    this.state._keyboardActive = false;\n    this.setActive();\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    bindFunction(device, 'start', this.pointerDown.bind(this));\n    if (this.config.pointerCapture) {\n      bindFunction(device, 'change', this.pointerMove.bind(this));\n      bindFunction(device, 'end', this.pointerUp.bind(this));\n      bindFunction(device, 'cancel', this.pointerUp.bind(this));\n      bindFunction('lostPointerCapture', '', this.pointerUp.bind(this));\n    }\n    if (this.config.keys) {\n      bindFunction('key', 'down', this.keyDown.bind(this));\n      bindFunction('key', 'up', this.keyUp.bind(this));\n    }\n    if (this.config.filterTaps) {\n      bindFunction('click', '', this.pointerClick.bind(this), {\n        capture: true,\n        passive: false\n      });\n    }\n  }\n}\nfunction persistEvent(event) {\n  'persist' in event && typeof event.persist === 'function' && event.persist();\n}\n\nconst isBrowser = typeof window !== 'undefined' && window.document && window.document.createElement;\nfunction supportsTouchEvents() {\n  return isBrowser && 'ontouchstart' in window;\n}\nfunction isTouchScreen() {\n  return supportsTouchEvents() || isBrowser && window.navigator.maxTouchPoints > 1;\n}\nfunction supportsPointerEvents() {\n  return isBrowser && 'onpointerdown' in window;\n}\nfunction supportsPointerLock() {\n  return isBrowser && 'exitPointerLock' in window.document;\n}\nfunction supportsGestureEvents() {\n  try {\n    return 'constructor' in GestureEvent;\n  } catch (e) {\n    return false;\n  }\n}\nconst SUPPORT = {\n  isBrowser,\n  gesture: supportsGestureEvents(),\n  touch: isTouchScreen(),\n  touchscreen: isTouchScreen(),\n  pointer: supportsPointerEvents(),\n  pointerLock: supportsPointerLock()\n};\n\nconst DEFAULT_PREVENT_SCROLL_DELAY = 250;\nconst DEFAULT_DRAG_DELAY = 180;\nconst DEFAULT_SWIPE_VELOCITY = 0.5;\nconst DEFAULT_SWIPE_DISTANCE = 50;\nconst DEFAULT_SWIPE_DURATION = 250;\nconst DEFAULT_KEYBOARD_DISPLACEMENT = 10;\nconst DEFAULT_DRAG_AXIS_THRESHOLD = {\n  mouse: 0,\n  touch: 0,\n  pen: 8\n};\nconst dragConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  device(_v, _k, {\n    pointer: {\n      touch = false,\n      lock = false,\n      mouse = false\n    } = {}\n  }) {\n    this.pointerLock = lock && SUPPORT.pointerLock;\n    if (SUPPORT.touch && touch) return 'touch';\n    if (this.pointerLock) return 'mouse';\n    if (SUPPORT.pointer && !mouse) return 'pointer';\n    if (SUPPORT.touch) return 'touch';\n    return 'mouse';\n  },\n  preventScrollAxis(value, _k, {\n    preventScroll\n  }) {\n    this.preventScrollDelay = typeof preventScroll === 'number' ? preventScroll : preventScroll || preventScroll === undefined && value ? DEFAULT_PREVENT_SCROLL_DELAY : undefined;\n    if (!SUPPORT.touchscreen || preventScroll === false) return undefined;\n    return value ? value : preventScroll !== undefined ? 'y' : undefined;\n  },\n  pointerCapture(_v, _k, {\n    pointer: {\n      capture = true,\n      buttons = 1,\n      keys = true\n    } = {}\n  }) {\n    this.pointerButtons = buttons;\n    this.keys = keys;\n    return !this.pointerLock && this.device === 'pointer' && capture;\n  },\n  threshold(value, _k, {\n    filterTaps = false,\n    tapsThreshold = 3,\n    axis = undefined\n  }) {\n    const threshold = V.toVector(value, filterTaps ? tapsThreshold : axis ? 1 : 0);\n    this.filterTaps = filterTaps;\n    this.tapsThreshold = tapsThreshold;\n    return threshold;\n  },\n  swipe({\n    velocity = DEFAULT_SWIPE_VELOCITY,\n    distance = DEFAULT_SWIPE_DISTANCE,\n    duration = DEFAULT_SWIPE_DURATION\n  } = {}) {\n    return {\n      velocity: this.transform(V.toVector(velocity)),\n      distance: this.transform(V.toVector(distance)),\n      duration\n    };\n  },\n  delay(value = 0) {\n    switch (value) {\n      case true:\n        return DEFAULT_DRAG_DELAY;\n      case false:\n        return 0;\n      default:\n        return value;\n    }\n  },\n  axisThreshold(value) {\n    if (!value) return DEFAULT_DRAG_AXIS_THRESHOLD;\n    return _objectSpread2(_objectSpread2({}, DEFAULT_DRAG_AXIS_THRESHOLD), value);\n  },\n  keyboardDisplacement(value = DEFAULT_KEYBOARD_DISPLACEMENT) {\n    return value;\n  }\n});\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(dragConfigResolver, {\n    useTouch(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`useTouch\\` option has been renamed to \\`pointer.touch\\`. Use it as in \\`{ pointer: { touch: true } }\\`.`);\n      }\n      return NaN;\n    },\n    experimental_preventWindowScrollY(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`experimental_preventWindowScrollY\\` option has been renamed to \\`preventScroll\\`.`);\n      }\n      return NaN;\n    },\n    swipeVelocity(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeVelocity\\` option has been renamed to \\`swipe.velocity\\`. Use it as in \\`{ swipe: { velocity: 0.5 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDistance(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDistance\\` option has been renamed to \\`swipe.distance\\`. Use it as in \\`{ swipe: { distance: 50 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDuration(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDuration\\` option has been renamed to \\`swipe.duration\\`. Use it as in \\`{ swipe: { duration: 250 } }\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\n\nfunction clampStateInternalMovementToBounds(state) {\n  const [ox, oy] = state.overflow;\n  const [dx, dy] = state._delta;\n  const [dirx, diry] = state._direction;\n  if (ox < 0 && dx > 0 && dirx < 0 || ox > 0 && dx < 0 && dirx > 0) {\n    state._movement[0] = state._movementBound[0];\n  }\n  if (oy < 0 && dy > 0 && diry < 0 || oy > 0 && dy < 0 && diry > 0) {\n    state._movement[1] = state._movementBound[1];\n  }\n}\n\nconst SCALE_ANGLE_RATIO_INTENT_DEG = 30;\nconst PINCH_WHEEL_RATIO = 100;\nclass PinchEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'pinching');\n    _defineProperty(this, \"aliasKey\", 'da');\n  }\n  init() {\n    this.state.offset = [1, 0];\n    this.state.lastOffset = [1, 0];\n    this.state._pointerEvents = new Map();\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._touchIds = [];\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n    state.turns = 0;\n  }\n  computeOffset() {\n    const {\n      type,\n      movement,\n      lastOffset\n    } = this.state;\n    if (type === 'wheel') {\n      this.state.offset = V.add(movement, lastOffset);\n    } else {\n      this.state.offset = [(1 + movement[0]) * lastOffset[0], movement[1] + lastOffset[1]];\n    }\n  }\n  computeMovement() {\n    const {\n      offset,\n      lastOffset\n    } = this.state;\n    this.state.movement = [offset[0] / lastOffset[0], offset[1] - lastOffset[1]];\n  }\n  axisIntent() {\n    const state = this.state;\n    const [_m0, _m1] = state._movement;\n    if (!state.axis) {\n      const axisMovementDifference = Math.abs(_m0) * SCALE_ANGLE_RATIO_INTENT_DEG - Math.abs(_m1);\n      if (axisMovementDifference < 0) state.axis = 'angle';else if (axisMovementDifference > 0) state.axis = 'scale';\n    }\n  }\n  restrictToAxis(v) {\n    if (this.config.lockDirection) {\n      if (this.state.axis === 'scale') v[1] = 0;else if (this.state.axis === 'angle') v[0] = 0;\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    setTimeout(() => {\n      state.canceled = true;\n      state._active = false;\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  touchStart(event) {\n    this.ctrl.setEventIds(event);\n    const state = this.state;\n    const ctrlTouchIds = this.ctrl.touchIds;\n    if (state._active) {\n      if (state._touchIds.every(id => ctrlTouchIds.has(id))) return;\n    }\n    if (ctrlTouchIds.size < 2) return;\n    this.start(event);\n    state._touchIds = Array.from(ctrlTouchIds).slice(0, 2);\n    const payload = touchDistanceAngle(event, state._touchIds);\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pointerStart(event) {\n    if (event.buttons != null && event.buttons % 2 !== 1) return;\n    this.ctrl.setEventIds(event);\n    event.target.setPointerCapture(event.pointerId);\n    const state = this.state;\n    const _pointerEvents = state._pointerEvents;\n    const ctrlPointerIds = this.ctrl.pointerIds;\n    if (state._active) {\n      if (Array.from(_pointerEvents.keys()).every(id => ctrlPointerIds.has(id))) return;\n    }\n    if (_pointerEvents.size < 2) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (state._pointerEvents.size < 2) return;\n    this.start(event);\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pinchStart(event, payload) {\n    const state = this.state;\n    state.origin = payload.origin;\n    this.computeValues([payload.distance, payload.angle]);\n    this.computeInitial();\n    this.compute(event);\n    this.emit();\n  }\n  touchMove(event) {\n    if (!this.state._active) return;\n    const payload = touchDistanceAngle(event, this.state._touchIds);\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pointerMove(event) {\n    const _pointerEvents = this.state._pointerEvents;\n    if (_pointerEvents.has(event.pointerId)) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (!this.state._active) return;\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pinchMove(event, payload) {\n    const state = this.state;\n    const prev_a = state._values[1];\n    const delta_a = payload.angle - prev_a;\n    let delta_turns = 0;\n    if (Math.abs(delta_a) > 270) delta_turns += Math.sign(delta_a);\n    this.computeValues([payload.distance, payload.angle - 360 * delta_turns]);\n    state.origin = payload.origin;\n    state.turns = delta_turns;\n    state._movement = [state._values[0] / state._initial[0] - 1, state._values[1] - state._initial[1]];\n    this.compute(event);\n    this.emit();\n  }\n  touchEnd(event) {\n    this.ctrl.setEventIds(event);\n    if (!this.state._active) return;\n    if (this.state._touchIds.some(id => !this.ctrl.touchIds.has(id))) {\n      this.state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  pointerEnd(event) {\n    const state = this.state;\n    this.ctrl.setEventIds(event);\n    try {\n      event.target.releasePointerCapture(event.pointerId);\n    } catch (_unused) {}\n    if (state._pointerEvents.has(event.pointerId)) {\n      state._pointerEvents.delete(event.pointerId);\n    }\n    if (!state._active) return;\n    if (state._pointerEvents.size < 2) {\n      state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  gestureStart(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    if (state._active) return;\n    this.start(event);\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  gestureMove(event) {\n    if (event.cancelable) event.preventDefault();\n    if (!this.state._active) return;\n    const state = this.state;\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    const _previousMovement = state._movement;\n    state._movement = [event.scale - 1, event.rotation];\n    state._delta = V.sub(state._movement, _previousMovement);\n    this.compute(event);\n    this.emit();\n  }\n  gestureEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  wheel(event) {\n    const modifierKey = this.config.modifierKey;\n    if (modifierKey && !event[modifierKey]) return;\n    if (!this.state._active) this.wheelStart(event);else this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelStart(event) {\n    this.start(event);\n    this.wheelChange(event);\n  }\n  wheelChange(event) {\n    const isR3f = ('uv' in event);\n    if (!isR3f) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      if (process.env.NODE_ENV === 'development' && !event.defaultPrevented) {\n        console.warn(`[@use-gesture]: To properly support zoom on trackpads, try using the \\`target\\` option.\\n\\nThis message will only appear in development mode.`);\n      }\n    }\n    const state = this.state;\n    state._delta = [-wheelValues(event)[1] / PINCH_WHEEL_RATIO * state.offset[0], 0];\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    if (!!device) {\n      bindFunction(device, 'start', this[device + 'Start'].bind(this));\n      bindFunction(device, 'change', this[device + 'Move'].bind(this));\n      bindFunction(device, 'end', this[device + 'End'].bind(this));\n      bindFunction(device, 'cancel', this[device + 'End'].bind(this));\n      bindFunction('lostPointerCapture', '', this[device + 'End'].bind(this));\n    }\n    if (this.config.pinchOnWheel) {\n      bindFunction('wheel', '', this.wheel.bind(this), {\n        passive: false\n      });\n    }\n  }\n}\n\nconst pinchConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  device(_v, _k, {\n    shared,\n    pointer: {\n      touch = false\n    } = {}\n  }) {\n    const sharedConfig = shared;\n    if (sharedConfig.target && !SUPPORT.touch && SUPPORT.gesture) return 'gesture';\n    if (SUPPORT.touch && touch) return 'touch';\n    if (SUPPORT.touchscreen) {\n      if (SUPPORT.pointer) return 'pointer';\n      if (SUPPORT.touch) return 'touch';\n    }\n  },\n  bounds(_v, _k, {\n    scaleBounds = {},\n    angleBounds = {}\n  }) {\n    const _scaleBounds = state => {\n      const D = assignDefault(call(scaleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [D.min, D.max];\n    };\n    const _angleBounds = state => {\n      const A = assignDefault(call(angleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [A.min, A.max];\n    };\n    if (typeof scaleBounds !== 'function' && typeof angleBounds !== 'function') return [_scaleBounds(), _angleBounds()];\n    return state => [_scaleBounds(state), _angleBounds(state)];\n  },\n  threshold(value, _k, config) {\n    this.lockDirection = config.axis === 'lock';\n    const threshold = V.toVector(value, this.lockDirection ? [0.1, 3] : 0);\n    return threshold;\n  },\n  modifierKey(value) {\n    if (value === undefined) return 'ctrlKey';\n    return value;\n  },\n  pinchOnWheel(value = true) {\n    return value;\n  }\n});\n\nclass MoveEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'moving');\n  }\n  move(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    if (!this.state._active) this.moveStart(event);else this.moveChange(event);\n    this.timeoutStore.add('moveEnd', this.moveEnd.bind(this));\n  }\n  moveStart(event) {\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.computeInitial();\n    this.emit();\n  }\n  moveChange(event) {\n    if (!this.state._active) return;\n    const values = pointerValues(event);\n    const state = this.state;\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  moveEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'change', this.move.bind(this));\n    bindFunction('pointer', 'leave', this.moveEnd.bind(this));\n  }\n}\n\nconst moveConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\n\nclass ScrollEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'scrolling');\n  }\n  scroll(event) {\n    if (!this.state._active) this.start(event);\n    this.scrollChange(event);\n    this.timeoutStore.add('scrollEnd', this.scrollEnd.bind(this));\n  }\n  scrollChange(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    const values = scrollValues(event);\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  scrollEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('scroll', '', this.scroll.bind(this));\n  }\n}\n\nconst scrollConfigResolver = coordinatesConfigResolver;\n\nclass WheelEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'wheeling');\n  }\n  wheel(event) {\n    if (!this.state._active) this.start(event);\n    this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelChange(event) {\n    const state = this.state;\n    state._delta = wheelValues(event);\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('wheel', '', this.wheel.bind(this));\n  }\n}\n\nconst wheelConfigResolver = coordinatesConfigResolver;\n\nclass HoverEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'hovering');\n  }\n  enter(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.emit();\n  }\n  leave(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    const state = this.state;\n    if (!state._active) return;\n    state._active = false;\n    const values = pointerValues(event);\n    state._movement = state._delta = V.sub(values, state._values);\n    this.computeValues(values);\n    this.compute(event);\n    state.delta = state.movement;\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'enter', this.enter.bind(this));\n    bindFunction('pointer', 'leave', this.leave.bind(this));\n  }\n}\n\nconst hoverConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\n\nconst EngineMap = new Map();\nconst ConfigResolverMap = new Map();\nfunction registerAction(action) {\n  EngineMap.set(action.key, action.engine);\n  ConfigResolverMap.set(action.key, action.resolver);\n}\nconst dragAction = {\n  key: 'drag',\n  engine: DragEngine,\n  resolver: dragConfigResolver\n};\nconst hoverAction = {\n  key: 'hover',\n  engine: HoverEngine,\n  resolver: hoverConfigResolver\n};\nconst moveAction = {\n  key: 'move',\n  engine: MoveEngine,\n  resolver: moveConfigResolver\n};\nconst pinchAction = {\n  key: 'pinch',\n  engine: PinchEngine,\n  resolver: pinchConfigResolver\n};\nconst scrollAction = {\n  key: 'scroll',\n  engine: ScrollEngine,\n  resolver: scrollConfigResolver\n};\nconst wheelAction = {\n  key: 'wheel',\n  engine: WheelEngine,\n  resolver: wheelConfigResolver\n};\n\nexport { ConfigResolverMap as C, EngineMap as E, SUPPORT as S, _objectSpread2 as _, _defineProperty as a, touchIds as b, chain as c, toHandlerProp as d, dragAction as e, pinchAction as f, hoverAction as h, isTouch as i, moveAction as m, parseProp as p, registerAction as r, scrollAction as s, toDomEventType as t, wheelAction as w };\n", "import { registerAction, dragAction, pinchAction, wheelAction, scrollAction, moveAction, hoverAction } from '@use-gesture/core/actions';\nexport * from '@use-gesture/core/actions';\nimport React from 'react';\nimport { Controller, parseMergedHandlers } from '@use-gesture/core';\nexport * from '@use-gesture/core/utils';\nexport * from '@use-gesture/core/types';\n\nfunction useRecognizers(handlers, config = {}, gestureKey, nativeHandlers) {\n  const ctrl = React.useMemo(() => new Controller(handlers), []);\n  ctrl.applyHandlers(handlers, nativeHandlers);\n  ctrl.applyConfig(config, gestureKey);\n  React.useEffect(ctrl.effect.bind(ctrl));\n  React.useEffect(() => {\n    return ctrl.clean.bind(ctrl);\n  }, []);\n  if (config.target === undefined) {\n    return ctrl.bind.bind(ctrl);\n  }\n  return undefined;\n}\n\nfunction useDrag(handler, config) {\n  registerAction(dragAction);\n  return useRecognizers({\n    drag: handler\n  }, config || {}, 'drag');\n}\n\nfunction usePinch(handler, config) {\n  registerAction(pinchAction);\n  return useRecognizers({\n    pinch: handler\n  }, config || {}, 'pinch');\n}\n\nfunction useWheel(handler, config) {\n  registerAction(wheelAction);\n  return useRecognizers({\n    wheel: handler\n  }, config || {}, 'wheel');\n}\n\nfunction useScroll(handler, config) {\n  registerAction(scrollAction);\n  return useRecognizers({\n    scroll: handler\n  }, config || {}, 'scroll');\n}\n\nfunction useMove(handler, config) {\n  registerAction(moveAction);\n  return useRecognizers({\n    move: handler\n  }, config || {}, 'move');\n}\n\nfunction useHover(handler, config) {\n  registerAction(hoverAction);\n  return useRecognizers({\n    hover: handler\n  }, config || {}, 'hover');\n}\n\nfunction createUseGesture(actions) {\n  actions.forEach(registerAction);\n  return function useGesture(_handlers, _config) {\n    const {\n      handlers,\n      nativeHandlers,\n      config\n    } = parseMergedHandlers(_handlers, _config || {});\n    return useRecognizers(handlers, config, undefined, nativeHandlers);\n  };\n}\n\nfunction useGesture(handlers, config) {\n  const hook = createUseGesture([dragAction, pinchAction, scrollAction, wheelAction, moveAction, hoverAction]);\n  return hook(handlers, config || {});\n}\n\nexport { createUseGesture, useDrag, useGesture, useHover, useMove, usePinch, useScroll, useWheel };\n", "import { S as SUPPORT, C as ConfigResolverMap, _ as _objectSpread2, a as _defineProperty, t as toDomEventType, i as isTouch, b as touchIds, E as EngineMap, c as chain, p as parseProp, d as toHandlerProp } from './actions-76b8683e.esm.js';\nimport './maths-0ab39ae9.esm.js';\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n\nconst sharedConfigResolver = {\n  target(value) {\n    if (value) {\n      return () => 'current' in value ? value.current : value;\n    }\n    return undefined;\n  },\n  enabled(value = true) {\n    return value;\n  },\n  window(value = SUPPORT.isBrowser ? window : undefined) {\n    return value;\n  },\n  eventOptions({\n    passive = true,\n    capture = false\n  } = {}) {\n    return {\n      passive,\n      capture\n    };\n  },\n  transform(value) {\n    return value;\n  }\n};\n\nconst _excluded = [\"target\", \"eventOptions\", \"window\", \"enabled\", \"transform\"];\nfunction resolveWith(config = {}, resolvers) {\n  const result = {};\n  for (const [key, resolver] of Object.entries(resolvers)) {\n    switch (typeof resolver) {\n      case 'function':\n        if (process.env.NODE_ENV === 'development') {\n          const r = resolver.call(result, config[key], key, config);\n          if (!Number.isNaN(r)) result[key] = r;\n        } else {\n          result[key] = resolver.call(result, config[key], key, config);\n        }\n        break;\n      case 'object':\n        result[key] = resolveWith(config[key], resolver);\n        break;\n      case 'boolean':\n        if (resolver) result[key] = config[key];\n        break;\n    }\n  }\n  return result;\n}\nfunction parse(newConfig, gestureKey, _config = {}) {\n  const _ref = newConfig,\n    {\n      target,\n      eventOptions,\n      window,\n      enabled,\n      transform\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  _config.shared = resolveWith({\n    target,\n    eventOptions,\n    window,\n    enabled,\n    transform\n  }, sharedConfigResolver);\n  if (gestureKey) {\n    const resolver = ConfigResolverMap.get(gestureKey);\n    _config[gestureKey] = resolveWith(_objectSpread2({\n      shared: _config.shared\n    }, rest), resolver);\n  } else {\n    for (const key in rest) {\n      const resolver = ConfigResolverMap.get(key);\n      if (resolver) {\n        _config[key] = resolveWith(_objectSpread2({\n          shared: _config.shared\n        }, rest[key]), resolver);\n      } else if (process.env.NODE_ENV === 'development') {\n        if (!['drag', 'pinch', 'scroll', 'wheel', 'move', 'hover'].includes(key)) {\n          if (key === 'domTarget') {\n            throw Error(`[@use-gesture]: \\`domTarget\\` option has been renamed to \\`target\\`.`);\n          }\n          console.warn(`[@use-gesture]: Unknown config key \\`${key}\\` was used. Please read the documentation for further information.`);\n        }\n      }\n    }\n  }\n  return _config;\n}\n\nclass EventStore {\n  constructor(ctrl, gestureKey) {\n    _defineProperty(this, \"_listeners\", new Set());\n    this._ctrl = ctrl;\n    this._gestureKey = gestureKey;\n  }\n  add(element, device, action, handler, options) {\n    const listeners = this._listeners;\n    const type = toDomEventType(device, action);\n    const _options = this._gestureKey ? this._ctrl.config[this._gestureKey].eventOptions : {};\n    const eventOptions = _objectSpread2(_objectSpread2({}, _options), options);\n    element.addEventListener(type, handler, eventOptions);\n    const remove = () => {\n      element.removeEventListener(type, handler, eventOptions);\n      listeners.delete(remove);\n    };\n    listeners.add(remove);\n    return remove;\n  }\n  clean() {\n    this._listeners.forEach(remove => remove());\n    this._listeners.clear();\n  }\n}\n\nclass TimeoutStore {\n  constructor() {\n    _defineProperty(this, \"_timeouts\", new Map());\n  }\n  add(key, callback, ms = 140, ...args) {\n    this.remove(key);\n    this._timeouts.set(key, window.setTimeout(callback, ms, ...args));\n  }\n  remove(key) {\n    const timeout = this._timeouts.get(key);\n    if (timeout) window.clearTimeout(timeout);\n  }\n  clean() {\n    this._timeouts.forEach(timeout => void window.clearTimeout(timeout));\n    this._timeouts.clear();\n  }\n}\n\nclass Controller {\n  constructor(handlers) {\n    _defineProperty(this, \"gestures\", new Set());\n    _defineProperty(this, \"_targetEventStore\", new EventStore(this));\n    _defineProperty(this, \"gestureEventStores\", {});\n    _defineProperty(this, \"gestureTimeoutStores\", {});\n    _defineProperty(this, \"handlers\", {});\n    _defineProperty(this, \"config\", {});\n    _defineProperty(this, \"pointerIds\", new Set());\n    _defineProperty(this, \"touchIds\", new Set());\n    _defineProperty(this, \"state\", {\n      shared: {\n        shiftKey: false,\n        metaKey: false,\n        ctrlKey: false,\n        altKey: false\n      }\n    });\n    resolveGestures(this, handlers);\n  }\n  setEventIds(event) {\n    if (isTouch(event)) {\n      this.touchIds = new Set(touchIds(event));\n      return this.touchIds;\n    } else if ('pointerId' in event) {\n      if (event.type === 'pointerup' || event.type === 'pointercancel') this.pointerIds.delete(event.pointerId);else if (event.type === 'pointerdown') this.pointerIds.add(event.pointerId);\n      return this.pointerIds;\n    }\n  }\n  applyHandlers(handlers, nativeHandlers) {\n    this.handlers = handlers;\n    this.nativeHandlers = nativeHandlers;\n  }\n  applyConfig(config, gestureKey) {\n    this.config = parse(config, gestureKey, this.config);\n  }\n  clean() {\n    this._targetEventStore.clean();\n    for (const key of this.gestures) {\n      this.gestureEventStores[key].clean();\n      this.gestureTimeoutStores[key].clean();\n    }\n  }\n  effect() {\n    if (this.config.shared.target) this.bind();\n    return () => this._targetEventStore.clean();\n  }\n  bind(...args) {\n    const sharedConfig = this.config.shared;\n    const props = {};\n    let target;\n    if (sharedConfig.target) {\n      target = sharedConfig.target();\n      if (!target) return;\n    }\n    if (sharedConfig.enabled) {\n      for (const gestureKey of this.gestures) {\n        const gestureConfig = this.config[gestureKey];\n        const bindFunction = bindToProps(props, gestureConfig.eventOptions, !!target);\n        if (gestureConfig.enabled) {\n          const Engine = EngineMap.get(gestureKey);\n          new Engine(this, args, gestureKey).bind(bindFunction);\n        }\n      }\n      const nativeBindFunction = bindToProps(props, sharedConfig.eventOptions, !!target);\n      for (const eventKey in this.nativeHandlers) {\n        nativeBindFunction(eventKey, '', event => this.nativeHandlers[eventKey](_objectSpread2(_objectSpread2({}, this.state.shared), {}, {\n          event,\n          args\n        })), undefined, true);\n      }\n    }\n    for (const handlerProp in props) {\n      props[handlerProp] = chain(...props[handlerProp]);\n    }\n    if (!target) return props;\n    for (const handlerProp in props) {\n      const {\n        device,\n        capture,\n        passive\n      } = parseProp(handlerProp);\n      this._targetEventStore.add(target, device, '', props[handlerProp], {\n        capture,\n        passive\n      });\n    }\n  }\n}\nfunction setupGesture(ctrl, gestureKey) {\n  ctrl.gestures.add(gestureKey);\n  ctrl.gestureEventStores[gestureKey] = new EventStore(ctrl, gestureKey);\n  ctrl.gestureTimeoutStores[gestureKey] = new TimeoutStore();\n}\nfunction resolveGestures(ctrl, internalHandlers) {\n  if (internalHandlers.drag) setupGesture(ctrl, 'drag');\n  if (internalHandlers.wheel) setupGesture(ctrl, 'wheel');\n  if (internalHandlers.scroll) setupGesture(ctrl, 'scroll');\n  if (internalHandlers.move) setupGesture(ctrl, 'move');\n  if (internalHandlers.pinch) setupGesture(ctrl, 'pinch');\n  if (internalHandlers.hover) setupGesture(ctrl, 'hover');\n}\nconst bindToProps = (props, eventOptions, withPassiveOption) => (device, action, handler, options = {}, isNative = false) => {\n  var _options$capture, _options$passive;\n  const capture = (_options$capture = options.capture) !== null && _options$capture !== void 0 ? _options$capture : eventOptions.capture;\n  const passive = (_options$passive = options.passive) !== null && _options$passive !== void 0 ? _options$passive : eventOptions.passive;\n  let handlerProp = isNative ? device : toHandlerProp(device, action, capture);\n  if (withPassiveOption && passive) handlerProp += 'Passive';\n  props[handlerProp] = props[handlerProp] || [];\n  props[handlerProp].push(handler);\n};\n\nconst RE_NOT_NATIVE = /^on(Drag|Wheel|Scroll|Move|Pinch|Hover)/;\nfunction sortHandlers(_handlers) {\n  const native = {};\n  const handlers = {};\n  const actions = new Set();\n  for (let key in _handlers) {\n    if (RE_NOT_NATIVE.test(key)) {\n      actions.add(RegExp.lastMatch);\n      handlers[key] = _handlers[key];\n    } else {\n      native[key] = _handlers[key];\n    }\n  }\n  return [handlers, native, actions];\n}\nfunction registerGesture(actions, handlers, handlerKey, key, internalHandlers, config) {\n  if (!actions.has(handlerKey)) return;\n  if (!EngineMap.has(key)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(`[@use-gesture]: You've created a custom handler that that uses the \\`${key}\\` gesture but isn't properly configured.\\n\\nPlease add \\`${key}Action\\` when creating your handler.`);\n    }\n    return;\n  }\n  const startKey = handlerKey + 'Start';\n  const endKey = handlerKey + 'End';\n  const fn = state => {\n    let memo = undefined;\n    if (state.first && startKey in handlers) handlers[startKey](state);\n    if (handlerKey in handlers) memo = handlers[handlerKey](state);\n    if (state.last && endKey in handlers) handlers[endKey](state);\n    return memo;\n  };\n  internalHandlers[key] = fn;\n  config[key] = config[key] || {};\n}\nfunction parseMergedHandlers(mergedHandlers, mergedConfig) {\n  const [handlers, nativeHandlers, actions] = sortHandlers(mergedHandlers);\n  const internalHandlers = {};\n  registerGesture(actions, handlers, 'onDrag', 'drag', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onWheel', 'wheel', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onScroll', 'scroll', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onPinch', 'pinch', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onMove', 'move', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onHover', 'hover', internalHandlers, mergedConfig);\n  return {\n    handlers: internalHandlers,\n    config: mergedConfig,\n    nativeHandlers\n  };\n}\n\nexport { Controller, parseMergedHandlers };\n", "var __defProp$1 = Object.defineProperty;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nconst redux = (reducer, initial) => (set, get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return __spreadValues$1({ dispatch: (...a) => api.dispatch(...a) }, initial);\n};\n\nfunction devtools(fn, options) {\n  return (set, get, api) => {\n    var _a;\n    let didWarnAboutNameDeprecation = false;\n    if (typeof options === \"string\" && !didWarnAboutNameDeprecation) {\n      console.warn(\"[zustand devtools middleware]: passing `name` as directly will be not allowed in next majorpass the `name` in an object `{ name: ... }` instead\");\n      didWarnAboutNameDeprecation = true;\n    }\n    const devtoolsOptions = options === void 0 ? { name: void 0, anonymousActionType: void 0 } : typeof options === \"string\" ? { name: options } : options;\n    if (typeof ((_a = devtoolsOptions == null ? void 0 : devtoolsOptions.serialize) == null ? void 0 : _a.options) !== \"undefined\") {\n      console.warn(\"[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`\");\n    }\n    let extensionConnector;\n    try {\n      extensionConnector = window.__REDUX_DEVTOOLS_EXTENSION__ || window.top.__REDUX_DEVTOOLS_EXTENSION__;\n    } catch {\n    }\n    if (!extensionConnector) {\n      if ((import.meta.env && import.meta.env.MODE) !== \"production\" && typeof window !== \"undefined\") {\n        console.warn(\"[zustand devtools middleware] Please install/enable Redux devtools extension\");\n      }\n      return fn(set, get, api);\n    }\n    let extension = Object.create(extensionConnector.connect(devtoolsOptions));\n    let didWarnAboutDevtools = false;\n    Object.defineProperty(api, \"devtools\", {\n      get: () => {\n        if (!didWarnAboutDevtools) {\n          console.warn(\"[zustand devtools middleware] `devtools` property on the store is deprecated it will be removed in the next major.\\nYou shouldn't interact with the extension directly. But in case you still want to you can patch `window.__REDUX_DEVTOOLS_EXTENSION__` directly\");\n          didWarnAboutDevtools = true;\n        }\n        return extension;\n      },\n      set: (value) => {\n        if (!didWarnAboutDevtools) {\n          console.warn(\"[zustand devtools middleware] `api.devtools` is deprecated, it will be removed in the next major.\\nYou shouldn't interact with the extension directly. But in case you still want to you can patch `window.__REDUX_DEVTOOLS_EXTENSION__` directly\");\n          didWarnAboutDevtools = true;\n        }\n        extension = value;\n      }\n    });\n    let didWarnAboutPrefix = false;\n    Object.defineProperty(extension, \"prefix\", {\n      get: () => {\n        if (!didWarnAboutPrefix) {\n          console.warn(\"[zustand devtools middleware] along with `api.devtools`, `api.devtools.prefix` is deprecated.\\nWe no longer prefix the actions/names\" + devtoolsOptions.name === void 0 ? \", pass the `name` option to create a separate instance of devtools for each store.\" : \", because the `name` option already creates a separate instance of devtools for each store.\");\n          didWarnAboutPrefix = true;\n        }\n        return \"\";\n      },\n      set: () => {\n        if (!didWarnAboutPrefix) {\n          console.warn(\"[zustand devtools middleware] along with `api.devtools`, `api.devtools.prefix` is deprecated.\\nWe no longer prefix the actions/names\" + devtoolsOptions.name === void 0 ? \", pass the `name` option to create a separate instance of devtools for each store.\" : \", because the `name` option already creates a separate instance of devtools for each store.\");\n          didWarnAboutPrefix = true;\n        }\n      }\n    });\n    let isRecording = true;\n    api.setState = (state, replace, nameOrAction) => {\n      set(state, replace);\n      if (!isRecording)\n        return;\n      extension.send(nameOrAction === void 0 ? { type: devtoolsOptions.anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction, get());\n    };\n    const setStateFromDevtools = (...a) => {\n      const originalIsRecording = isRecording;\n      isRecording = false;\n      set(...a);\n      isRecording = originalIsRecording;\n    };\n    const initialState = fn(api.setState, get, api);\n    extension.init(initialState);\n    if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n      let didWarnAboutReservedActionType = false;\n      const originalDispatch = api.dispatch;\n      api.dispatch = (...a) => {\n        if (a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n          console.warn('[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.');\n          didWarnAboutReservedActionType = true;\n        }\n        originalDispatch(...a);\n      };\n    }\n    extension.subscribe((message) => {\n      var _a2;\n      switch (message.type) {\n        case \"ACTION\":\n          if (typeof message.payload !== \"string\") {\n            console.error(\"[zustand devtools middleware] Unsupported action format\");\n            return;\n          }\n          return parseJsonThen(message.payload, (action) => {\n            if (action.type === \"__setState\") {\n              setStateFromDevtools(action.state);\n              return;\n            }\n            if (!api.dispatchFromDevtools)\n              return;\n            if (typeof api.dispatch !== \"function\")\n              return;\n            api.dispatch(action);\n          });\n        case \"DISPATCH\":\n          switch (message.payload.type) {\n            case \"RESET\":\n              setStateFromDevtools(initialState);\n              return extension.init(api.getState());\n            case \"COMMIT\":\n              return extension.init(api.getState());\n            case \"ROLLBACK\":\n              return parseJsonThen(message.state, (state) => {\n                setStateFromDevtools(state);\n                extension.init(api.getState());\n              });\n            case \"JUMP_TO_STATE\":\n            case \"JUMP_TO_ACTION\":\n              return parseJsonThen(message.state, (state) => {\n                setStateFromDevtools(state);\n              });\n            case \"IMPORT_STATE\": {\n              const { nextLiftedState } = message.payload;\n              const lastComputedState = (_a2 = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a2.state;\n              if (!lastComputedState)\n                return;\n              setStateFromDevtools(lastComputedState);\n              extension.send(null, nextLiftedState);\n              return;\n            }\n            case \"PAUSE_RECORDING\":\n              return isRecording = !isRecording;\n          }\n          return;\n      }\n    });\n    return initialState;\n  };\n}\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\"[zustand devtools middleware] Could not parse the received json\", e);\n  }\n  if (parsed !== void 0)\n    f(parsed);\n};\n\nconst subscribeWithSelector = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\n\nconst combine = (initialState, create) => (set, get, api) => Object.assign({}, initialState, create(set, get, api));\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persist = (config, baseOptions) => (set, get, api) => {\n  let options = __spreadValues({\n    getStorage: () => localStorage,\n    serialize: JSON.stringify,\n    deserialize: JSON.parse,\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => __spreadValues(__spreadValues({}, currentState), persistedState)\n  }, baseOptions);\n  if (options.blacklist || options.whitelist) {\n    console.warn(`The ${options.blacklist ? \"blacklist\" : \"whitelist\"} option is deprecated and will be removed in the next version. Please use the 'partialize' option instead.`);\n  }\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage;\n  try {\n    storage = options.getStorage();\n  } catch (e) {\n  }\n  if (!storage) {\n    return config((...args) => {\n      console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n      set(...args);\n    }, get, api);\n  } else if (!storage.removeItem) {\n    console.warn(`[zustand persist middleware] The given storage for item '${options.name}' does not contain a 'removeItem' method, which will be required in v4.`);\n  }\n  const thenableSerialize = toThenable(options.serialize);\n  const setItem = () => {\n    const state = options.partialize(__spreadValues({}, get()));\n    if (options.whitelist) {\n      Object.keys(state).forEach((key) => {\n        var _a;\n        !((_a = options.whitelist) == null ? void 0 : _a.includes(key)) && delete state[key];\n      });\n    }\n    if (options.blacklist) {\n      options.blacklist.forEach((key) => delete state[key]);\n    }\n    let errorInSync;\n    const thenable = thenableSerialize({ state, version: options.version }).then((serializedValue) => storage.setItem(options.name, serializedValue)).catch((e) => {\n      errorInSync = e;\n    });\n    if (errorInSync) {\n      throw errorInSync;\n    }\n    return thenable;\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config((...args) => {\n    set(...args);\n    void setItem();\n  }, get, api);\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a;\n    if (!storage)\n      return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => cb(get()));\n    const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue) => {\n      if (storageValue) {\n        return options.deserialize(storageValue);\n      }\n    }).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);\n          }\n          console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n        } else {\n          return deserializedStorageValue.state;\n        }\n      }\n    }).then((migratedState) => {\n      var _a2;\n      stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n      set(stateFromStorage, true);\n      return setItem();\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = __spreadValues(__spreadValues({}, options), newOptions);\n      if (newOptions.getStorage) {\n        storage = newOptions.getStorage();\n      }\n    },\n    clearStorage: () => {\n      var _a;\n      (_a = storage == null ? void 0 : storage.removeItem) == null ? void 0 : _a.call(storage, options.name);\n    },\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  hydrate();\n  return stateFromStorage || configResult;\n};\n\nexport { combine, devtools, persist, redux, subscribeWithSelector };\n", "function shallow(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (let i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !Object.is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport { shallow as default };\n"], "mappings": ";;;;;;;;AAAA,SAAS,MAAM,GAAG,KAAK,KAAK;AAC1B,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AACvC;AACA,IAAM,IAAI;AAAA,EACR,SAAS,GAAG,UAAU;AACpB,QAAI,MAAM;AAAW,UAAI;AACzB,WAAO,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC;AAAA,EACrC;AAAA,EACA,IAAI,IAAI,IAAI;AACV,WAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAAA,EACtC;AAAA,EACA,IAAI,IAAI,IAAI;AACV,WAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAAA,EACtC;AAAA,EACA,MAAM,IAAI,IAAI;AACZ,OAAG,CAAC,KAAK,GAAG,CAAC;AACb,OAAG,CAAC,KAAK,GAAG,CAAC;AAAA,EACf;AAAA,EACA,MAAM,IAAI,IAAI;AACZ,OAAG,CAAC,KAAK,GAAG,CAAC;AACb,OAAG,CAAC,KAAK,GAAG,CAAC;AAAA,EACf;AACF;AACA,SAAS,WAAW,UAAU,WAAW,UAAU;AACjD,MAAI,cAAc,KAAK,KAAK,IAAI,SAAS,MAAM;AAAU,WAAO,KAAK,IAAI,UAAU,WAAW,CAAC;AAC/F,SAAO,WAAW,YAAY,YAAY,YAAY,WAAW;AACnE;AACA,SAAS,wBAAwB,UAAU,KAAK,KAAK,WAAW,MAAM;AACpE,MAAI,aAAa;AAAG,WAAO,MAAM,UAAU,KAAK,GAAG;AACnD,MAAI,WAAW;AAAK,WAAO,CAAC,WAAW,MAAM,UAAU,MAAM,KAAK,QAAQ,IAAI;AAC9E,MAAI,WAAW;AAAK,WAAO,CAAC,WAAW,WAAW,KAAK,MAAM,KAAK,QAAQ,IAAI;AAC9E,SAAO;AACT;AACA,SAAS,kBAAkB,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG;AACrD,QAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI;AAC7B,SAAO,CAAC,wBAAwB,IAAI,IAAI,IAAI,EAAE,GAAG,wBAAwB,IAAI,IAAI,IAAI,EAAE,CAAC;AAC1F;;;AClCA,SAAS,aAAa,OAAO,MAAM;AACjC,MAAI,OAAO,UAAU,YAAY,UAAU;AAAM,WAAO;AACxD,MAAI,OAAO,MAAM,OAAO,WAAW;AACnC,MAAI,SAAS,QAAW;AACtB,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAC5C,QAAI,OAAO,QAAQ;AAAU,aAAO;AACpC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AACpD;AAEA,SAAS,eAAe,KAAK;AAC3B,MAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,SAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AACnD;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AACzD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AACzD,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AACF;AACA,SAAS,WAAW,QAAQ;AAC1B,MAAI,CAAC;AAAQ,WAAO;AACpB,SAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC;AACjD;AACA,IAAM,iCAAiC,CAAC,SAAS,OAAO;AACxD,SAAS,WAAW,UAAU,OAAO,WAAW;AAC9C,SAAO,WAAW,CAAC,+BAA+B,SAAS,SAAS;AACtE;AACA,SAAS,cAAc,QAAQ,SAAS,IAAI,UAAU,OAAO;AAC3D,QAAM,cAAc,eAAe,MAAM;AACzC,QAAM,YAAY,cAAc,YAAY,MAAM,KAAK,SAAS;AAChE,SAAO,OAAO,WAAW,MAAM,IAAI,WAAW,SAAS,KAAK,WAAW,SAAS,SAAS,IAAI,YAAY;AAC3G;AACA,IAAM,uBAAuB,CAAC,qBAAqB,oBAAoB;AACvE,SAAS,UAAU,MAAM;AACvB,MAAI,WAAW,KAAK,UAAU,CAAC,EAAE,YAAY;AAC7C,QAAM,UAAU,CAAC,CAAC,CAAC,SAAS,QAAQ,SAAS;AAC7C,MAAI;AAAS,eAAW,SAAS,QAAQ,WAAW,EAAE;AACtD,QAAM,aAAa,qBAAqB,SAAS,QAAQ,IAAI,mBAAmB;AAChF,QAAM,UAAU,CAAC,CAAC,CAAC,SAAS,QAAQ,UAAU;AAC9C,MAAI;AAAS,eAAW,SAAS,QAAQ,WAAW,EAAE;AACtD,SAAO;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,eAAe,QAAQ,SAAS,IAAI;AAC3C,QAAM,cAAc,eAAe,MAAM;AACzC,QAAM,YAAY,cAAc,YAAY,MAAM,KAAK,SAAS;AAChE,SAAO,SAAS;AAClB;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,aAAa;AACtB;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,QAAQ,KAAK;AAAG,WAAO;AAC3B,MAAI,iBAAiB;AAAO,WAAO,MAAM;AACzC,SAAO;AACT;AACA,SAAS,0BAA0B,OAAO;AACxC,SAAO,MAAM,KAAK,MAAM,OAAO,EAAE,OAAO,OAAK;AAC3C,QAAI,sBAAsB;AAC1B,WAAO,EAAE,WAAW,MAAM,mBAAmB,uBAAuB,MAAM,mBAAmB,QAAQ,yBAAyB,SAAS,UAAU,wBAAwB,qBAAqB,cAAc,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,sBAAsB,EAAE,MAAM;AAAA,EAC3T,CAAC;AACH;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,SAAS,cAAc,MAAM,SAAS,gBAAgB,MAAM,iBAAiB,MAAM;AAClG;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,QAAQ,KAAK,IAAI,aAAa,KAAK,EAAE,CAAC,IAAI;AACnD;AACA,SAAS,cAAc,IAAI,IAAI;AAC7B,MAAI;AACF,UAAM,KAAK,GAAG,UAAU,GAAG;AAC3B,UAAM,KAAK,GAAG,UAAU,GAAG;AAC3B,UAAM,MAAM,GAAG,UAAU,GAAG,WAAW;AACvC,UAAM,MAAM,GAAG,UAAU,GAAG,WAAW;AACvC,UAAM,WAAW,KAAK,MAAM,IAAI,EAAE;AAClC,UAAM,QAAQ,EAAE,KAAK,MAAM,IAAI,EAAE,IAAI,OAAO,KAAK;AACjD,UAAM,SAAS,CAAC,IAAI,EAAE;AACtB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,SAAS,SAAP;AAAA,EAAiB;AACnB,SAAO;AACT;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,0BAA0B,KAAK,EAAE,IAAI,WAAS,MAAM,UAAU;AACvE;AACA,SAAS,mBAAmB,OAAO,KAAK;AACtC,QAAM,CAAC,IAAI,EAAE,IAAI,MAAM,KAAK,MAAM,OAAO,EAAE,OAAO,WAAS,IAAI,SAAS,MAAM,UAAU,CAAC;AACzF,SAAO,cAAc,IAAI,EAAE;AAC7B;AACA,SAAS,UAAU,OAAO;AACxB,QAAM,aAAa,cAAc,KAAK;AACtC,SAAO,QAAQ,KAAK,IAAI,WAAW,aAAa,WAAW;AAC7D;AACA,SAAS,cAAc,OAAO;AAC5B,QAAM,aAAa,cAAc,KAAK;AACtC,SAAO,CAAC,WAAW,SAAS,WAAW,OAAO;AAChD;AACA,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,SAAS,YAAY,OAAO;AAC1B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,cAAc,GAAG;AACnB,cAAU;AACV,cAAU;AAAA,EACZ,WAAW,cAAc,GAAG;AAC1B,cAAU;AACV,cAAU;AAAA,EACZ;AACA,SAAO,CAAC,QAAQ,MAAM;AACxB;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,MAAM;AACV,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,MAAM;AACV,SAAO,EAAE,OAAO,YAAY,QAAQ,YAAY,SAAS,UAAU,gBAAgB,QAAQ,SAAS,SAAS,OAAO,IAAI,QAAQ,YAAY,QAAQ,YAAY,SAAS,UAAU,eAAe,QAAQ,UAAU,SAAS,QAAQ,CAAC;AACxO;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,UAAU,CAAC;AACjB,MAAI,aAAa;AAAO,YAAQ,UAAU,MAAM;AAChD,MAAI,cAAc,OAAO;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,OAAO,SAAS;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,KAAK,MAAM,MAAM;AACxB,MAAI,OAAO,MAAM,YAAY;AAC3B,WAAO,EAAE,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,OAAO;AAAC;AACjB,SAAS,SAAS,KAAK;AACrB,MAAI,IAAI,WAAW;AAAG,WAAO;AAC7B,MAAI,IAAI,WAAW;AAAG,WAAO,IAAI,CAAC;AAClC,SAAO,WAAY;AACjB,QAAI;AACJ,eAAW,MAAM,KAAK;AACpB,eAAS,GAAG,MAAM,MAAM,SAAS,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,OAAO,UAAU;AACtC,SAAO,OAAO,OAAO,CAAC,GAAG,UAAU,SAAS,CAAC,CAAC;AAChD;AAEA,IAAM,+BAA+B;AACrC,IAAM,SAAN,MAAa;AAAA,EACX,YAAY,MAAM,MAAM,KAAK;AAC3B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,QAAQ,CAAC;AACd,WAAK,cAAc,CAAC,GAAG,CAAC,CAAC;AACzB,WAAK,eAAe;AACpB,UAAI,KAAK;AAAM,aAAK,KAAK;AACzB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,KAAK,MAAM,KAAK,GAAG;AAAA,EACjC;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,KAAK,MAAM,KAAK,GAAG,IAAI;AAAA,EAC9B;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,MAAM;AAAA,EACzB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,KAAK,mBAAmB,KAAK,GAAG;AAAA,EAC9C;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,KAAK,qBAAqB,KAAK,GAAG;AAAA,EAChD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,EAClC;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,KAAK,OAAO;AAAA,EAC1B;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,KAAK,SAAS,KAAK,GAAG;AAAA,EACpC;AAAA,EACA,QAAQ;AACN,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,MAAM,IAAI,MAAM,UAAU,MAAM,SAAS,MAAM,WAAW,MAAM,SAAS;AAChF,UAAM,QAAQ,CAAC,OAAO,KAAK;AAC3B,UAAM,cAAc;AACpB,UAAM,YAAY,CAAC,GAAG,CAAC;AACvB,UAAM,YAAY,CAAC,GAAG,CAAC;AACvB,UAAM,aAAa,CAAC,GAAG,CAAC;AACxB,UAAM,SAAS,CAAC,GAAG,CAAC;AACpB,UAAM,UAAU,CAAC,CAAC,WAAW,QAAQ,GAAG,CAAC,WAAW,QAAQ,CAAC;AAC7D,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,cAAc,MAAM,YAAY;AACtC,UAAM,YAAY,CAAC,GAAG,CAAC;AACvB,UAAM,WAAW,CAAC,GAAG,CAAC;AACtB,UAAM,WAAW,CAAC,GAAG,CAAC;AACtB,UAAM,iBAAiB,CAAC,OAAO,KAAK;AACpC,UAAM,WAAW,CAAC,GAAG,CAAC;AACtB,UAAM,WAAW,CAAC,GAAG,CAAC;AACtB,UAAM,QAAQ,CAAC,GAAG,CAAC;AACnB,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,MAAM,OAAO;AACX,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,MAAM,SAAS;AAClB,WAAK,MAAM;AACX,WAAK,eAAe;AACpB,YAAM,UAAU;AAChB,YAAM,SAAS,MAAM;AACrB,YAAM,gBAAgB,MAAM;AAC5B,YAAM,aAAa,OAAO,OAAO,KAAK,OAAO,MAAM,KAAK,IAAI,MAAM;AAClE,YAAM,SAAS,MAAM;AACrB,YAAM,YAAY,MAAM,YAAY,MAAM;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,QAAQ,KAAK;AACnB,UAAM,UAAU;AAChB,UAAM,SAAS,KAAK,OAAO,UAAU,MAAM;AAAA,EAC7C;AAAA,EACA,iBAAiB;AACf,UAAM,QAAQ,KAAK;AACnB,UAAM,WAAW,MAAM;AACvB,UAAM,UAAU,MAAM;AAAA,EACxB;AAAA,EACA,QAAQ,OAAO;AACb,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,KAAK;AAClB,QAAI,KAAK;AACT,QAAI,OAAO;AACT,YAAM,QAAQ;AACd,UAAI,OAAO,kBAAkB,MAAM;AAAY,cAAM,MAAM,eAAe;AAC1E,YAAM,OAAO,MAAM;AACnB,aAAO,UAAU,KAAK,KAAK,WAAW,QAAQ,KAAK,KAAK,SAAS;AACjE,aAAO,SAAS,CAAC,CAAC,SAAS;AAC3B,aAAO,OAAO,QAAQ,gBAAgB,KAAK,CAAC;AAC5C,aAAO,OAAO,OAAO,UAAU,OAAO,UAAU,MAAM,KAAK,OAAO,UAAU;AAC5E,WAAK,MAAM,YAAY,MAAM;AAC7B,YAAM,YAAY,MAAM;AACxB,YAAM,cAAc,MAAM,YAAY,MAAM;AAAA,IAC9C;AACA,QAAI,MAAM,SAAS;AACjB,YAAM,iBAAiB,MAAM,OAAO,IAAI,KAAK,GAAG;AAChD,QAAE,MAAM,MAAM,WAAW,cAAc;AAAA,IACzC;AACA,QAAI,KAAK;AAAY,WAAK,WAAW,KAAK;AAC1C,UAAM,CAAC,KAAK,GAAG,IAAI,MAAM;AACzB,UAAM,CAAC,IAAI,EAAE,IAAI,OAAO;AACxB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,oBAAoB;AAC7B,UAAI,MAAM,CAAC,MAAM;AAAO,cAAM,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,OAAO,CAAC;AAClE,UAAI,MAAM,CAAC,MAAM;AAAO,cAAM,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,OAAO,CAAC;AAAA,IACpE,OAAO;AACL,UAAI,MAAM,CAAC,MAAM;AAAO,cAAM,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI;AAC3E,UAAI,MAAM,CAAC,MAAM;AAAO,cAAM,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI;AAAA,IAC7E;AACA,UAAM,cAAc,MAAM,CAAC,MAAM,SAAS,MAAM,CAAC,MAAM;AACvD,QAAI,CAAC,MAAM;AAAa;AACxB,UAAM,WAAW,CAAC,GAAG,CAAC;AACtB,QAAI,OAAO,oBAAoB;AAC7B,YAAM,CAAC,IAAI,EAAE,IAAI;AACjB,eAAS,CAAC,IAAI,MAAM,CAAC,MAAM,QAAQ,KAAK,MAAM,CAAC,IAAI;AACnD,eAAS,CAAC,IAAI,MAAM,CAAC,MAAM,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,IACrD,OAAO;AACL,eAAS,CAAC,IAAI,MAAM,CAAC,MAAM,QAAQ,MAAM,MAAM,CAAC,IAAI;AACpD,eAAS,CAAC,IAAI,MAAM,CAAC,MAAM,QAAQ,MAAM,MAAM,CAAC,IAAI;AAAA,IACtD;AACA,QAAI,KAAK,kBAAkB,CAAC,MAAM;AAAU,WAAK,eAAe,QAAQ;AACxE,UAAM,iBAAiB,MAAM;AAC7B,UAAM,kBAAkB,MAAM,WAAW,CAAC,MAAM,YAAY,MAAM;AAClE,QAAI,iBAAiB;AACnB,YAAM,QAAQ,MAAM,WAAW,CAAC,MAAM;AACtC,YAAM,OAAO,CAAC,MAAM,WAAW,MAAM;AACrC,YAAM,SAAS,OAAO,KAAK,MAAM,IAAI,MAAM;AAC3C,UAAI,OAAO;AACT,YAAI,MAAM,OAAO;AACf,cAAI,YAAY;AAAQ,kBAAM,UAAU,KAAK,OAAO,QAAQ,KAAK;AACjE,cAAI,KAAK;AAAO,iBAAK,MAAM;AAAA,QAC7B;AACA,cAAM,WAAW;AACjB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AACA,UAAM,CAAC,IAAI,EAAE,IAAI,MAAM;AACvB,UAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACnC,UAAM,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC;AAChF,UAAM,eAAe,CAAC,IAAI,MAAM,SAAS,CAAC,IAAI,MAAM,eAAe,CAAC,MAAM,QAAQ,MAAM,UAAU,CAAC,IAAI,MAAM,eAAe,CAAC,IAAI;AACjI,UAAM,eAAe,CAAC,IAAI,MAAM,SAAS,CAAC,IAAI,MAAM,eAAe,CAAC,MAAM,QAAQ,MAAM,UAAU,CAAC,IAAI,MAAM,eAAe,CAAC,IAAI;AACjI,UAAMA,cAAa,MAAM,UAAU,OAAO,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;AACtE,UAAM,SAAS,kBAAkB,MAAM,SAAS,MAAM,QAAQA,WAAU;AACxE,UAAM,QAAQ,EAAE,IAAI,MAAM,QAAQ,cAAc;AAChD,SAAK,gBAAgB;AACrB,QAAI,oBAAoB,CAAC,MAAM,QAAQ,KAAK,+BAA+B;AACzE,YAAM,QAAQ,EAAE,IAAI,MAAM,QAAQ,cAAc;AAChD,YAAM,gBAAgB,MAAM,MAAM,IAAI,KAAK,GAAG;AAC9C,QAAE,MAAM,MAAM,UAAU,aAAa;AACrC,YAAM,YAAY,MAAM,MAAM,IAAI,KAAK,IAAI;AAC3C,YAAM,aAAa,MAAM,OAAO,IAAI,KAAK,IAAI;AAC7C,UAAI,CAAC,MAAM,SAAS,KAAK,GAAG;AAC1B,cAAM,WAAW,CAAC,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE;AAC9D,cAAM,YAAY;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,MAAM;AAAS,WAAK,MAAM;AAC/B,SAAK,MAAM,YAAY,CAAC,MAAM,gBAAgB,CAAC,MAAM,UAAU,CAAC,OAAO;AAAkB;AACzF,UAAM,OAAO,KAAK,QAAQ,eAAe,eAAe,eAAe,CAAC,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,MAC9F,CAAC,KAAK,QAAQ,GAAG,MAAM;AAAA,IACzB,CAAC,CAAC;AACF,QAAI,SAAS;AAAW,YAAM,OAAO;AAAA,EACvC;AAAA,EACA,QAAQ;AACN,SAAK,WAAW,MAAM;AACtB,SAAK,aAAa,MAAM;AAAA,EAC1B;AACF;AAEA,SAAS,WAAW,CAAC,IAAI,EAAE,GAAG,WAAW;AACvC,QAAM,QAAQ,KAAK,IAAI,EAAE;AACzB,QAAM,QAAQ,KAAK,IAAI,EAAE;AACzB,MAAI,QAAQ,SAAS,QAAQ,WAAW;AACtC,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS,QAAQ,WAAW;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,oBAAN,cAAgC,OAAO;AAAA,EACrC,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,oBAAgB,MAAM,YAAY,IAAI;AAAA,EACxC;AAAA,EACA,QAAQ;AACN,UAAM,MAAM;AACZ,SAAK,MAAM,OAAO;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,MAAM,SAAS,CAAC,GAAG,CAAC;AACzB,SAAK,MAAM,aAAa,CAAC,GAAG,CAAC;AAAA,EAC/B;AAAA,EACA,gBAAgB;AACd,SAAK,MAAM,SAAS,EAAE,IAAI,KAAK,MAAM,YAAY,KAAK,MAAM,QAAQ;AAAA,EACtE;AAAA,EACA,kBAAkB;AAChB,SAAK,MAAM,WAAW,EAAE,IAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,UAAU;AAAA,EACtE;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,MAAM,QAAQ,OAAO;AACxB,YAAM,YAAY,OAAO,OAAO,kBAAkB,WAAW,OAAO,cAAc,eAAe,KAAK,CAAC,IAAI,OAAO;AAClH,YAAM,OAAO,WAAW,MAAM,WAAW,SAAS;AAAA,IACpD;AACA,UAAM,YAAY,OAAO,iBAAiB,CAAC,CAAC,OAAO,SAAS,CAAC,MAAM,QAAQ,CAAC,CAAC,OAAO,QAAQ,OAAO,SAAS,MAAM;AAAA,EACpH;AAAA,EACA,eAAe,GAAG;AAChB,QAAI,KAAK,OAAO,QAAQ,KAAK,OAAO,eAAe;AACjD,cAAQ,KAAK,MAAM,MAAM;AAAA,QACvB,KAAK;AACH,YAAE,CAAC,IAAI;AACP;AAAA,QACF,KAAK;AACH,YAAE,CAAC,IAAI;AACP;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,WAAW,OAAK;AACtB,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAAA,EAC3B,QAAQ,QAAQ,MAAM;AACpB,WAAO;AAAA,EACT;AAAA,EACA,aAAa,OAAO,IAAI,QAAQ;AAC9B,WAAO,eAAe,eAAe,CAAC,GAAG,OAAO,OAAO,YAAY,GAAG,KAAK;AAAA,EAC7E;AAAA,EACA,eAAe,QAAQ,OAAO;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,QAAQ,OAAO;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,WAAW,QAAQ,GAAG;AACpB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,CAAC,oBAAoB,kBAAkB;AAAA,MAChD,KAAK;AACH,eAAO,CAAC,GAAG,CAAC;AAAA,MACd;AACE,eAAO,EAAE,SAAS,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,KAAK,OAAO;AACV,QAAI,OAAO,UAAU;AAAY,aAAO;AACxC,QAAI,SAAS;AAAM,aAAO,EAAE,SAAS,KAAK;AAAA,EAC5C;AAAA,EACA,UAAU,OAAO,IAAI,QAAQ;AAC3B,UAAM,YAAY,SAAS,OAAO,OAAO;AACzC,SAAK,qBAAqB,CAAC,CAAC;AAC5B,QAAI,MAAwC;AAC1C,YAAM,oBAAoB,aAAa;AACvC,aAAO,OAAK;AACV,cAAM,IAAI,kBAAkB,CAAC;AAC7B,YAAI,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG;AACtC,kBAAQ,KAAK,gFAAgF,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI;AAAA,QAC7G;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,UAAU,OAAO;AACf,WAAO,EAAE,SAAS,OAAO,CAAC;AAAA,EAC5B;AACF;AACA,IAAI,MAAwC;AAC1C,SAAO,OAAO,sBAAsB;AAAA,IAClC,UAAU,OAAO;AACf,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,sEAAsE;AAAA,MACpF;AACA,aAAO;AAAA,IACT;AAAA,IACA,cAAc,OAAO;AACnB,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,2GAA2G;AAAA,MACzH;AACA,aAAO;AAAA,IACT;AAAA,IACA,QAAQ,OAAO;AACb,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,kEAAkE;AAAA,MAChF;AACA,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AAEA,IAAM,yBAAyB;AAC/B,IAAM,4BAA4B,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG;AAAA,EAC7F,KAAK,IAAI,IAAI;AAAA,IACX;AAAA,EACF,GAAG;AACD,SAAK,gBAAgB,SAAS;AAC9B,QAAI,CAAC,KAAK;AAAe,aAAO;AAAA,EAClC;AAAA,EACA,cAAc,QAAQ,wBAAwB;AAC5C,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,CAAC,GAAG;AACjB,QAAI,OAAO,UAAU,YAAY;AAC/B,aAAO,WAAS,0BAA0B,OAAO,MAAM,KAAK,CAAC;AAAA,IAC/D;AACA,QAAI,aAAa,OAAO;AACtB,aAAO,MAAM,MAAM;AAAA,IACrB;AACA,QAAI,OAAO,gBAAgB,cAAc,iBAAiB,aAAa;AACrE,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX,IAAI;AACJ,WAAO,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,CAAC;AAAA,EACtC;AACF,CAAC;AAED,IAAM,iBAAiB;AAAA,EACrB,YAAY,CAAC,cAAc,SAAS,MAAM,CAAC,eAAe,QAAQ,CAAC;AAAA,EACnE,WAAW,CAAC,cAAc,SAAS,MAAM,CAAC,KAAK,eAAe,QAAQ,CAAC;AAAA,EACvE,SAAS,CAAC,cAAc,SAAS,MAAM,CAAC,GAAG,KAAK,eAAe,MAAM;AAAA,EACrE,WAAW,CAAC,cAAc,SAAS,MAAM,CAAC,GAAG,eAAe,MAAM;AACpE;AACA,IAAM,aAAN,cAAyB,kBAAkB;AAAA,EACzC,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,oBAAgB,MAAM,UAAU,UAAU;AAAA,EAC5C;AAAA,EACA,QAAQ;AACN,UAAM,MAAM;AACZ,UAAM,QAAQ,KAAK;AACnB,UAAM,aAAa;AACnB,UAAM,iBAAiB;AACvB,UAAM,kBAAkB;AACxB,UAAM,iBAAiB;AACvB,UAAM,WAAW;AACjB,UAAM,QAAQ,CAAC,GAAG,CAAC;AACnB,UAAM,MAAM;AACZ,UAAM,WAAW;AACjB,UAAM,SAAS,KAAK,OAAO,KAAK,IAAI;AAAA,EACtC;AAAA,EACA,QAAQ;AACN,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,mBAAmB,aAAa;AACxC,YAAM,YAAY,MAAM,QAAQ,sBAAsB;AACtD,YAAM,aAAa,MAAM,cAAc,sBAAsB;AAC7D,YAAM,UAAU;AAAA,QACd,MAAM,UAAU,OAAO,WAAW,OAAO,MAAM,OAAO,CAAC;AAAA,QACvD,OAAO,UAAU,QAAQ,WAAW,QAAQ,MAAM,OAAO,CAAC;AAAA,QAC1D,KAAK,UAAU,MAAM,WAAW,MAAM,MAAM,OAAO,CAAC;AAAA,QACpD,QAAQ,UAAU,SAAS,WAAW,SAAS,MAAM,OAAO,CAAC;AAAA,MAC/D;AACA,YAAM,UAAU,0BAA0B,OAAO,OAAO;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM;AAAU;AACpB,UAAM,WAAW;AACjB,UAAM,UAAU;AAChB,eAAW,MAAM;AACf,WAAK,QAAQ;AACb,WAAK,KAAK;AAAA,IACZ,GAAG,CAAC;AAAA,EACN;AAAA,EACA,YAAY;AACV,SAAK,MAAM,UAAU,KAAK,MAAM,kBAAkB,KAAK,MAAM;AAAA,EAC/D;AAAA,EACA,QAAQ;AACN,SAAK,aAAa;AAClB,SAAK,MAAM,iBAAiB;AAC5B,SAAK,MAAM,kBAAkB;AAC7B,UAAM,MAAM;AAAA,EACd;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,SAAS,KAAK;AACpB,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,WAAW,SAAS,MAAM,QAAQ,OAAO,cAAc,IAAI,CAAC,OAAO,eAAe,SAAS,MAAM,OAAO,IAAI,OAAO,mBAAmB,MAAM,OAAO,mBAAmB,MAAM;AAAU;AAChM,UAAM,UAAU,KAAK,KAAK,YAAY,KAAK;AAC3C,QAAI,OAAO,gBAAgB;AACzB,YAAM,OAAO,kBAAkB,MAAM,SAAS;AAAA,IAChD;AACA,QAAI,WAAW,QAAQ,OAAO,KAAK,MAAM;AAAgB;AACzD,SAAK,MAAM,KAAK;AAChB,SAAK,aAAa,KAAK;AACvB,UAAM,aAAa,UAAU,KAAK;AAClC,UAAM,iBAAiB;AACvB,SAAK,cAAc,cAAc,KAAK,CAAC;AACvC,SAAK,eAAe;AACpB,QAAI,OAAO,qBAAqB,eAAe,KAAK,MAAM,SAAS;AACjE,YAAM,UAAU;AAChB,WAAK,sBAAsB,KAAK;AAAA,IAClC,WAAW,OAAO,QAAQ,GAAG;AAC3B,WAAK,kBAAkB,KAAK;AAC5B,UAAI,OAAO,kBAAkB;AAC3B,aAAK,QAAQ,KAAK;AAClB,aAAK,KAAK;AAAA,MACZ;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,UAAM,QAAQ,KAAK;AACnB,UAAM,UAAU;AAChB,UAAM,iBAAiB;AACvB,UAAM,WAAW;AACjB,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,MAAM;AAAgB;AAC3B,UAAM,KAAK,UAAU,KAAK;AAC1B,QAAI,MAAM,eAAe,UAAa,OAAO,MAAM;AAAY;AAC/D,UAAM,UAAU,cAAc,KAAK;AACnC,QAAI,SAAS,uBAAuB,MAAM,QAAQ;AAChD,YAAM,SAAS,CAAC,MAAM,WAAW,MAAM,SAAS;AAAA,IAClD,OAAO;AACL,YAAM,SAAS,EAAE,IAAI,SAAS,MAAM,OAAO;AAC3C,WAAK,cAAc,OAAO;AAAA,IAC5B;AACA,MAAE,MAAM,MAAM,WAAW,MAAM,MAAM;AACrC,SAAK,QAAQ,KAAK;AAClB,QAAI,MAAM,YAAY,MAAM,aAAa;AACvC,WAAK,aAAa,OAAO,WAAW;AACpC,YAAM,SAAS;AACf,WAAK,iBAAiB,KAAK;AAC3B;AAAA,IACF;AACA,QAAI,OAAO,qBAAqB,CAAC,MAAM,gBAAgB;AACrD,UAAI,MAAM,MAAM;AACd,YAAI,MAAM,SAAS,OAAO,qBAAqB,OAAO,sBAAsB,MAAM;AAChF,gBAAM,UAAU;AAChB,eAAK,MAAM;AACX;AAAA,QACF,OAAO;AACL,eAAK,aAAa,OAAO,kBAAkB;AAC3C,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAAA,IACF;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,UAAU,OAAO;AACf,SAAK,KAAK,YAAY,KAAK;AAC3B,QAAI;AACF,UAAI,KAAK,OAAO,kBAAkB,MAAM,OAAO,kBAAkB,MAAM,SAAS,GAAG;AACjF;AACA,cAAM,OAAO,sBAAsB,MAAM,SAAS;AAAA,MACpD;AAAA,IACF,SAAS,SAAP;AACA,UAAI,MAAwC;AAC1C,gBAAQ,KAAK;AAAA;AAAA,sCAAiK;AAAA,MAChL;AAAA,IACF;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,MAAM,WAAW,CAAC,MAAM;AAAgB;AAC7C,UAAM,KAAK,UAAU,KAAK;AAC1B,QAAI,MAAM,eAAe,UAAa,OAAO,MAAM;AAAY;AAC/D,SAAK,MAAM,iBAAiB;AAC5B,SAAK,UAAU;AACf,SAAK,QAAQ,KAAK;AAClB,UAAM,CAAC,IAAI,EAAE,IAAI,MAAM;AACvB,UAAM,MAAM,MAAM,OAAO,iBAAiB,MAAM,OAAO;AACvD,QAAI,MAAM,OAAO,OAAO,YAAY;AAClC,YAAM,SAAS;AAAA,IACjB,OAAO;AACL,YAAM,CAAC,KAAK,GAAG,IAAI,MAAM;AACzB,YAAM,CAAC,KAAK,GAAG,IAAI,MAAM;AACzB,YAAM,CAAC,KAAK,GAAG,IAAI,OAAO,MAAM;AAChC,YAAM,CAAC,IAAI,EAAE,IAAI,OAAO,MAAM;AAC9B,YAAM,MAAM,OAAO,MAAM;AACzB,UAAI,MAAM,cAAc,KAAK;AAC3B,cAAM,MAAM,KAAK,IAAI,MAAM,MAAM,SAAS;AAC1C,cAAM,MAAM,KAAK,IAAI,MAAM,MAAM,SAAS;AAC1C,YAAI,MAAM,OAAO,KAAK,IAAI,GAAG,IAAI;AAAI,gBAAM,MAAM,CAAC,IAAI,KAAK,KAAK,GAAG;AACnE,YAAI,MAAM,OAAO,KAAK,IAAI,GAAG,IAAI;AAAI,gBAAM,MAAM,CAAC,IAAI,KAAK,KAAK,GAAG;AAAA,MACrE;AAAA,IACF;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,MAAM,OAAO,MAAM,SAAS,GAAG;AACvC,YAAM,eAAe;AACrB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,OAAO;AACtB,QAAI,MAAwC;AAC1C,UAAI;AACF,YAAI,WAAW,aAAa,OAAO,uBAAuB,QAAW;AACnE,gBAAM,gBAAgB,QAAQ,QAAQ,MAAM,YAAY,gBAAgB,MAAM;AAC9E,gBAAM,QAAQ,OAAO,iBAAiB,aAAa;AACnD,cAAI,MAAM,gBAAgB,QAAQ;AAChC,oBAAQ,KAAK;AAAA;AAAA,0HAAya,aAAa;AAAA,UACrc;AAAA,QACF;AAAA,MACF,SAAS,UAAP;AAAA,MAAkB;AAAA,IACtB;AACA,QAAI,OAAO,aAAa;AACtB,YAAM,cAAc,mBAAmB;AAAA,IACzC;AACA,QAAI,CAAC,OAAO,gBAAgB;AAC1B,WAAK,WAAW,IAAI,KAAK,aAAa,QAAQ,QAAQ,UAAU,KAAK,YAAY,KAAK,IAAI,CAAC;AAC3F,WAAK,WAAW,IAAI,KAAK,aAAa,QAAQ,QAAQ,OAAO,KAAK,UAAU,KAAK,IAAI,CAAC;AACtF,WAAK,WAAW,IAAI,KAAK,aAAa,QAAQ,QAAQ,UAAU,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,IAC3F;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,OAAO,eAAe,SAAS,uBAAuB,KAAK,MAAM,eAAe;AACvF,eAAS,gBAAgB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,KAAK,MAAM,kBAAkB,MAAM,YAAY;AACjD,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,sBAAsB,OAAO;AAC3B,SAAK,MAAM,iBAAiB;AAC5B,iBAAa,KAAK;AAClB,UAAM,SAAS,KAAK,WAAW,IAAI,KAAK,aAAa,QAAQ,SAAS,UAAU,KAAK,cAAc,KAAK,IAAI,GAAG;AAAA,MAC7G,SAAS;AAAA,IACX,CAAC;AACD,SAAK,WAAW,IAAI,KAAK,aAAa,QAAQ,SAAS,OAAO,MAAM;AACpE,SAAK,WAAW,IAAI,KAAK,aAAa,QAAQ,SAAS,UAAU,MAAM;AACvE,SAAK,aAAa,IAAI,oBAAoB,KAAK,iBAAiB,KAAK,IAAI,GAAG,KAAK,OAAO,oBAAoB,KAAK;AAAA,EACnH;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,MAAM,WAAW;AACtB,SAAK,aAAa,IAAI,aAAa,MAAM;AACvC,WAAK,MAAM,QAAQ,CAAC,GAAG,CAAC;AACxB,WAAK,iBAAiB,KAAK;AAAA,IAC7B,GAAG,KAAK,OAAO,KAAK;AAAA,EACtB;AAAA,EACA,QAAQ,OAAO;AACb,UAAM,UAAU,eAAe,MAAM,GAAG;AACxC,QAAI,SAAS;AACX,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,MAAM,WAAW,KAAK,MAAM,SAAS,MAAM;AAC1D,WAAK,MAAM,KAAK;AAChB,YAAM,SAAS,QAAQ,KAAK,OAAO,sBAAsB,MAAM;AAC/D,YAAM,kBAAkB;AACxB,QAAE,MAAM,MAAM,WAAW,MAAM,MAAM;AACrC,WAAK,QAAQ,KAAK;AAClB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,QAAI,EAAE,MAAM,OAAO;AAAiB;AACpC,SAAK,MAAM,kBAAkB;AAC7B,SAAK,UAAU;AACf,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,KAAK,cAAc;AACjB,UAAM,SAAS,KAAK,OAAO;AAC3B,iBAAa,QAAQ,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC;AACzD,QAAI,KAAK,OAAO,gBAAgB;AAC9B,mBAAa,QAAQ,UAAU,KAAK,YAAY,KAAK,IAAI,CAAC;AAC1D,mBAAa,QAAQ,OAAO,KAAK,UAAU,KAAK,IAAI,CAAC;AACrD,mBAAa,QAAQ,UAAU,KAAK,UAAU,KAAK,IAAI,CAAC;AACxD,mBAAa,sBAAsB,IAAI,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,IAClE;AACA,QAAI,KAAK,OAAO,MAAM;AACpB,mBAAa,OAAO,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC;AACnD,mBAAa,OAAO,MAAM,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA,IACjD;AACA,QAAI,KAAK,OAAO,YAAY;AAC1B,mBAAa,SAAS,IAAI,KAAK,aAAa,KAAK,IAAI,GAAG;AAAA,QACtD,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,eAAa,SAAS,OAAO,MAAM,YAAY,cAAc,MAAM,QAAQ;AAC7E;AAEA,IAAM,YAAY,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AACtF,SAAS,sBAAsB;AAC7B,SAAO,aAAa,kBAAkB;AACxC;AACA,SAAS,gBAAgB;AACvB,SAAO,oBAAoB,KAAK,aAAa,OAAO,UAAU,iBAAiB;AACjF;AACA,SAAS,wBAAwB;AAC/B,SAAO,aAAa,mBAAmB;AACzC;AACA,SAAS,sBAAsB;AAC7B,SAAO,aAAa,qBAAqB,OAAO;AAClD;AACA,SAAS,wBAAwB;AAC/B,MAAI;AACF,WAAO,iBAAiB;AAAA,EAC1B,SAAS,GAAP;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,UAAU;AAAA,EACd;AAAA,EACA,SAAS,sBAAsB;AAAA,EAC/B,OAAO,cAAc;AAAA,EACrB,aAAa,cAAc;AAAA,EAC3B,SAAS,sBAAsB;AAAA,EAC/B,aAAa,oBAAoB;AACnC;AAEA,IAAM,+BAA+B;AACrC,IAAM,qBAAqB;AAC3B,IAAM,yBAAyB;AAC/B,IAAM,yBAAyB;AAC/B,IAAM,yBAAyB;AAC/B,IAAM,gCAAgC;AACtC,IAAM,8BAA8B;AAAA,EAClC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,KAAK;AACP;AACA,IAAM,qBAAqB,eAAe,eAAe,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG;AAAA,EAC3F,OAAO,IAAI,IAAI;AAAA,IACb,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,IAAI,CAAC;AAAA,EACP,GAAG;AACD,SAAK,cAAc,QAAQ,QAAQ;AACnC,QAAI,QAAQ,SAAS;AAAO,aAAO;AACnC,QAAI,KAAK;AAAa,aAAO;AAC7B,QAAI,QAAQ,WAAW,CAAC;AAAO,aAAO;AACtC,QAAI,QAAQ;AAAO,aAAO;AAC1B,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF,GAAG;AACD,SAAK,qBAAqB,OAAO,kBAAkB,WAAW,gBAAgB,iBAAiB,kBAAkB,UAAa,QAAQ,+BAA+B;AACrK,QAAI,CAAC,QAAQ,eAAe,kBAAkB;AAAO,aAAO;AAC5D,WAAO,QAAQ,QAAQ,kBAAkB,SAAY,MAAM;AAAA,EAC7D;AAAA,EACA,eAAe,IAAI,IAAI;AAAA,IACrB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,IACT,IAAI,CAAC;AAAA,EACP,GAAG;AACD,SAAK,iBAAiB;AACtB,SAAK,OAAO;AACZ,WAAO,CAAC,KAAK,eAAe,KAAK,WAAW,aAAa;AAAA,EAC3D;AAAA,EACA,UAAU,OAAO,IAAI;AAAA,IACnB,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,OAAO;AAAA,EACT,GAAG;AACD,UAAM,YAAY,EAAE,SAAS,OAAO,aAAa,gBAAgB,OAAO,IAAI,CAAC;AAC7E,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,WAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,EACb,IAAI,CAAC,GAAG;AACN,WAAO;AAAA,MACL,UAAU,KAAK,UAAU,EAAE,SAAS,QAAQ,CAAC;AAAA,MAC7C,UAAU,KAAK,UAAU,EAAE,SAAS,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,GAAG;AACf,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC;AAAO,aAAO;AACnB,WAAO,eAAe,eAAe,CAAC,GAAG,2BAA2B,GAAG,KAAK;AAAA,EAC9E;AAAA,EACA,qBAAqB,QAAQ,+BAA+B;AAC1D,WAAO;AAAA,EACT;AACF,CAAC;AACD,IAAI,MAAwC;AAC1C,SAAO,OAAO,oBAAoB;AAAA,IAChC,SAAS,OAAO;AACd,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,2HAA2H;AAAA,MACzI;AACA,aAAO;AAAA,IACT;AAAA,IACA,kCAAkC,OAAO;AACvC,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,qGAAqG;AAAA,MACnH;AACA,aAAO;AAAA,IACT;AAAA,IACA,cAAc,OAAO;AACnB,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,iIAAiI;AAAA,MAC/I;AACA,aAAO;AAAA,IACT;AAAA,IACA,cAAc,OAAO;AACnB,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,gIAAgI;AAAA,MAC9I;AACA,aAAO;AAAA,IACT;AAAA,IACA,cAAc,OAAO;AACnB,UAAI,UAAU,QAAW;AACvB,cAAM,MAAM,iIAAiI;AAAA,MAC/I;AACA,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AAEA,SAAS,mCAAmC,OAAO;AACjD,QAAM,CAAC,IAAI,EAAE,IAAI,MAAM;AACvB,QAAM,CAAC,IAAI,EAAE,IAAI,MAAM;AACvB,QAAM,CAAC,MAAM,IAAI,IAAI,MAAM;AAC3B,MAAI,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAChE,UAAM,UAAU,CAAC,IAAI,MAAM,eAAe,CAAC;AAAA,EAC7C;AACA,MAAI,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAChE,UAAM,UAAU,CAAC,IAAI,MAAM,eAAe,CAAC;AAAA,EAC7C;AACF;AAEA,IAAM,+BAA+B;AACrC,IAAM,oBAAoB;AAC1B,IAAM,cAAN,cAA0B,OAAO;AAAA,EAC/B,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,oBAAgB,MAAM,UAAU,UAAU;AAC1C,oBAAgB,MAAM,YAAY,IAAI;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,MAAM,SAAS,CAAC,GAAG,CAAC;AACzB,SAAK,MAAM,aAAa,CAAC,GAAG,CAAC;AAC7B,SAAK,MAAM,iBAAiB,oBAAI,IAAI;AAAA,EACtC;AAAA,EACA,QAAQ;AACN,UAAM,MAAM;AACZ,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,CAAC;AACnB,UAAM,WAAW;AACjB,UAAM,SAAS,KAAK,OAAO,KAAK,IAAI;AACpC,UAAM,QAAQ;AAAA,EAChB;AAAA,EACA,gBAAgB;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,SAAS,SAAS;AACpB,WAAK,MAAM,SAAS,EAAE,IAAI,UAAU,UAAU;AAAA,IAChD,OAAO;AACL,WAAK,MAAM,SAAS,EAAE,IAAI,SAAS,CAAC,KAAK,WAAW,CAAC,GAAG,SAAS,CAAC,IAAI,WAAW,CAAC,CAAC;AAAA,IACrF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,SAAK,MAAM,WAAW,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,GAAG,OAAO,CAAC,IAAI,WAAW,CAAC,CAAC;AAAA,EAC7E;AAAA,EACA,aAAa;AACX,UAAM,QAAQ,KAAK;AACnB,UAAM,CAAC,KAAK,GAAG,IAAI,MAAM;AACzB,QAAI,CAAC,MAAM,MAAM;AACf,YAAM,yBAAyB,KAAK,IAAI,GAAG,IAAI,+BAA+B,KAAK,IAAI,GAAG;AAC1F,UAAI,yBAAyB;AAAG,cAAM,OAAO;AAAA,eAAiB,yBAAyB;AAAG,cAAM,OAAO;AAAA,IACzG;AAAA,EACF;AAAA,EACA,eAAe,GAAG;AAChB,QAAI,KAAK,OAAO,eAAe;AAC7B,UAAI,KAAK,MAAM,SAAS;AAAS,UAAE,CAAC,IAAI;AAAA,eAAW,KAAK,MAAM,SAAS;AAAS,UAAE,CAAC,IAAI;AAAA,IACzF;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM;AAAU;AACpB,eAAW,MAAM;AACf,YAAM,WAAW;AACjB,YAAM,UAAU;AAChB,WAAK,QAAQ;AACb,WAAK,KAAK;AAAA,IACZ,GAAG,CAAC;AAAA,EACN;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,KAAK,YAAY,KAAK;AAC3B,UAAM,QAAQ,KAAK;AACnB,UAAM,eAAe,KAAK,KAAK;AAC/B,QAAI,MAAM,SAAS;AACjB,UAAI,MAAM,UAAU,MAAM,QAAM,aAAa,IAAI,EAAE,CAAC;AAAG;AAAA,IACzD;AACA,QAAI,aAAa,OAAO;AAAG;AAC3B,SAAK,MAAM,KAAK;AAChB,UAAM,YAAY,MAAM,KAAK,YAAY,EAAE,MAAM,GAAG,CAAC;AACrD,UAAM,UAAU,mBAAmB,OAAO,MAAM,SAAS;AACzD,QAAI,CAAC;AAAS;AACd,SAAK,WAAW,OAAO,OAAO;AAAA,EAChC;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,WAAW,QAAQ,MAAM,UAAU,MAAM;AAAG;AACtD,SAAK,KAAK,YAAY,KAAK;AAC3B,UAAM,OAAO,kBAAkB,MAAM,SAAS;AAC9C,UAAM,QAAQ,KAAK;AACnB,UAAM,iBAAiB,MAAM;AAC7B,UAAM,iBAAiB,KAAK,KAAK;AACjC,QAAI,MAAM,SAAS;AACjB,UAAI,MAAM,KAAK,eAAe,KAAK,CAAC,EAAE,MAAM,QAAM,eAAe,IAAI,EAAE,CAAC;AAAG;AAAA,IAC7E;AACA,QAAI,eAAe,OAAO,GAAG;AAC3B,qBAAe,IAAI,MAAM,WAAW,KAAK;AAAA,IAC3C;AACA,QAAI,MAAM,eAAe,OAAO;AAAG;AACnC,SAAK,MAAM,KAAK;AAChB,UAAM,UAAU,cAAc,GAAG,MAAM,KAAK,eAAe,OAAO,CAAC,CAAC;AACpE,QAAI,CAAC;AAAS;AACd,SAAK,WAAW,OAAO,OAAO;AAAA,EAChC;AAAA,EACA,WAAW,OAAO,SAAS;AACzB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,QAAQ;AACvB,SAAK,cAAc,CAAC,QAAQ,UAAU,QAAQ,KAAK,CAAC;AACpD,SAAK,eAAe;AACpB,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,UAAM,UAAU,mBAAmB,OAAO,KAAK,MAAM,SAAS;AAC9D,QAAI,CAAC;AAAS;AACd,SAAK,UAAU,OAAO,OAAO;AAAA,EAC/B;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,iBAAiB,KAAK,MAAM;AAClC,QAAI,eAAe,IAAI,MAAM,SAAS,GAAG;AACvC,qBAAe,IAAI,MAAM,WAAW,KAAK;AAAA,IAC3C;AACA,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,UAAM,UAAU,cAAc,GAAG,MAAM,KAAK,eAAe,OAAO,CAAC,CAAC;AACpE,QAAI,CAAC;AAAS;AACd,SAAK,UAAU,OAAO,OAAO;AAAA,EAC/B;AAAA,EACA,UAAU,OAAO,SAAS;AACxB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,MAAM,QAAQ,CAAC;AAC9B,UAAM,UAAU,QAAQ,QAAQ;AAChC,QAAI,cAAc;AAClB,QAAI,KAAK,IAAI,OAAO,IAAI;AAAK,qBAAe,KAAK,KAAK,OAAO;AAC7D,SAAK,cAAc,CAAC,QAAQ,UAAU,QAAQ,QAAQ,MAAM,WAAW,CAAC;AACxE,UAAM,SAAS,QAAQ;AACvB,UAAM,QAAQ;AACd,UAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,IAAI,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,MAAM,SAAS,CAAC,CAAC;AACjG,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,SAAS,OAAO;AACd,SAAK,KAAK,YAAY,KAAK;AAC3B,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,QAAI,KAAK,MAAM,UAAU,KAAK,QAAM,CAAC,KAAK,KAAK,SAAS,IAAI,EAAE,CAAC,GAAG;AAChE,WAAK,MAAM,UAAU;AACrB,WAAK,QAAQ,KAAK;AAClB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,QAAQ,KAAK;AACnB,SAAK,KAAK,YAAY,KAAK;AAC3B,QAAI;AACF,YAAM,OAAO,sBAAsB,MAAM,SAAS;AAAA,IACpD,SAAS,SAAP;AAAA,IAAiB;AACnB,QAAI,MAAM,eAAe,IAAI,MAAM,SAAS,GAAG;AAC7C,YAAM,eAAe,OAAO,MAAM,SAAS;AAAA,IAC7C;AACA,QAAI,CAAC,MAAM;AAAS;AACpB,QAAI,MAAM,eAAe,OAAO,GAAG;AACjC,YAAM,UAAU;AAChB,WAAK,QAAQ,KAAK;AAClB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM;AAAY,YAAM,eAAe;AAC3C,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM;AAAS;AACnB,SAAK,MAAM,KAAK;AAChB,SAAK,cAAc,CAAC,MAAM,OAAO,MAAM,QAAQ,CAAC;AAChD,UAAM,SAAS,CAAC,MAAM,SAAS,MAAM,OAAO;AAC5C,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,MAAM;AAAY,YAAM,eAAe;AAC3C,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,UAAM,QAAQ,KAAK;AACnB,SAAK,cAAc,CAAC,MAAM,OAAO,MAAM,QAAQ,CAAC;AAChD,UAAM,SAAS,CAAC,MAAM,SAAS,MAAM,OAAO;AAC5C,UAAM,oBAAoB,MAAM;AAChC,UAAM,YAAY,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ;AAClD,UAAM,SAAS,EAAE,IAAI,MAAM,WAAW,iBAAiB;AACvD,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,SAAK,MAAM,UAAU;AACrB,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,MAAM,OAAO;AACX,UAAM,cAAc,KAAK,OAAO;AAChC,QAAI,eAAe,CAAC,MAAM,WAAW;AAAG;AACxC,QAAI,CAAC,KAAK,MAAM;AAAS,WAAK,WAAW,KAAK;AAAA;AAAO,WAAK,YAAY,KAAK;AAC3E,SAAK,aAAa,IAAI,YAAY,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,EAC5D;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,QAAS,QAAQ;AACvB,QAAI,CAAC,OAAO;AACV,UAAI,MAAM,YAAY;AACpB,cAAM,eAAe;AAAA,MACvB;AACA,UAA8C,CAAC,MAAM,kBAAkB;AACrE,gBAAQ,KAAK;AAAA;AAAA,mDAA+I;AAAA,MAC9J;AAAA,IACF;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,CAAC,CAAC,YAAY,KAAK,EAAE,CAAC,IAAI,oBAAoB,MAAM,OAAO,CAAC,GAAG,CAAC;AAC/E,MAAE,MAAM,MAAM,WAAW,MAAM,MAAM;AACrC,uCAAmC,KAAK;AACxC,SAAK,MAAM,SAAS,CAAC,MAAM,SAAS,MAAM,OAAO;AACjD,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,SAAK,MAAM,UAAU;AACrB,SAAK,QAAQ;AACb,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,KAAK,cAAc;AACjB,UAAM,SAAS,KAAK,OAAO;AAC3B,QAAI,CAAC,CAAC,QAAQ;AACZ,mBAAa,QAAQ,SAAS,KAAK,SAAS,OAAO,EAAE,KAAK,IAAI,CAAC;AAC/D,mBAAa,QAAQ,UAAU,KAAK,SAAS,MAAM,EAAE,KAAK,IAAI,CAAC;AAC/D,mBAAa,QAAQ,OAAO,KAAK,SAAS,KAAK,EAAE,KAAK,IAAI,CAAC;AAC3D,mBAAa,QAAQ,UAAU,KAAK,SAAS,KAAK,EAAE,KAAK,IAAI,CAAC;AAC9D,mBAAa,sBAAsB,IAAI,KAAK,SAAS,KAAK,EAAE,KAAK,IAAI,CAAC;AAAA,IACxE;AACA,QAAI,KAAK,OAAO,cAAc;AAC5B,mBAAa,SAAS,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG;AAAA,QAC/C,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,IAAM,sBAAsB,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG;AAAA,EACvF,OAAO,IAAI,IAAI;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV,IAAI,CAAC;AAAA,EACP,GAAG;AACD,UAAM,eAAe;AACrB,QAAI,aAAa,UAAU,CAAC,QAAQ,SAAS,QAAQ;AAAS,aAAO;AACrE,QAAI,QAAQ,SAAS;AAAO,aAAO;AACnC,QAAI,QAAQ,aAAa;AACvB,UAAI,QAAQ;AAAS,eAAO;AAC5B,UAAI,QAAQ;AAAO,eAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,OAAO,IAAI,IAAI;AAAA,IACb,cAAc,CAAC;AAAA,IACf,cAAc,CAAC;AAAA,EACjB,GAAG;AACD,UAAM,eAAe,WAAS;AAC5B,YAAM,IAAI,cAAc,KAAK,aAAa,KAAK,GAAG;AAAA,QAChD,KAAK;AAAA,QACL,KAAK;AAAA,MACP,CAAC;AACD,aAAO,CAAC,EAAE,KAAK,EAAE,GAAG;AAAA,IACtB;AACA,UAAM,eAAe,WAAS;AAC5B,YAAM,IAAI,cAAc,KAAK,aAAa,KAAK,GAAG;AAAA,QAChD,KAAK;AAAA,QACL,KAAK;AAAA,MACP,CAAC;AACD,aAAO,CAAC,EAAE,KAAK,EAAE,GAAG;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,cAAc,OAAO,gBAAgB;AAAY,aAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AAClH,WAAO,WAAS,CAAC,aAAa,KAAK,GAAG,aAAa,KAAK,CAAC;AAAA,EAC3D;AAAA,EACA,UAAU,OAAO,IAAI,QAAQ;AAC3B,SAAK,gBAAgB,OAAO,SAAS;AACrC,UAAM,YAAY,EAAE,SAAS,OAAO,KAAK,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;AACrE,WAAO;AAAA,EACT;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,UAAU;AAAW,aAAO;AAChC,WAAO;AAAA,EACT;AAAA,EACA,aAAa,QAAQ,MAAM;AACzB,WAAO;AAAA,EACT;AACF,CAAC;AAED,IAAM,aAAN,cAAyB,kBAAkB;AAAA,EACzC,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,oBAAgB,MAAM,UAAU,QAAQ;AAAA,EAC1C;AAAA,EACA,KAAK,OAAO;AACV,QAAI,KAAK,OAAO,aAAa,MAAM,gBAAgB;AAAS;AAC5D,QAAI,CAAC,KAAK,MAAM;AAAS,WAAK,UAAU,KAAK;AAAA;AAAO,WAAK,WAAW,KAAK;AACzE,SAAK,aAAa,IAAI,WAAW,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC1D;AAAA,EACA,UAAU,OAAO;AACf,SAAK,MAAM,KAAK;AAChB,SAAK,cAAc,cAAc,KAAK,CAAC;AACvC,SAAK,QAAQ,KAAK;AAClB,SAAK,eAAe;AACpB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,UAAM,SAAS,cAAc,KAAK;AAClC,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,EAAE,IAAI,QAAQ,MAAM,OAAO;AAC1C,MAAE,MAAM,MAAM,WAAW,MAAM,MAAM;AACrC,SAAK,cAAc,MAAM;AACzB,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,SAAK,MAAM,UAAU;AACrB,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,KAAK,cAAc;AACjB,iBAAa,WAAW,UAAU,KAAK,KAAK,KAAK,IAAI,CAAC;AACtD,iBAAa,WAAW,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC1D;AACF;AAEA,IAAM,qBAAqB,eAAe,eAAe,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG;AAAA,EAC3F,WAAW,CAAC,QAAQ,SAAS;AAC/B,CAAC;AAED,IAAM,eAAN,cAA2B,kBAAkB;AAAA,EAC3C,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,oBAAgB,MAAM,UAAU,WAAW;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,CAAC,KAAK,MAAM;AAAS,WAAK,MAAM,KAAK;AACzC,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,IAAI,aAAa,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,EAC9D;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM;AAAY,YAAM,eAAe;AAC3C,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,aAAa,KAAK;AACjC,UAAM,SAAS,EAAE,IAAI,QAAQ,MAAM,OAAO;AAC1C,MAAE,MAAM,MAAM,WAAW,MAAM,MAAM;AACrC,SAAK,cAAc,MAAM;AACzB,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,YAAY;AACV,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,SAAK,MAAM,UAAU;AACrB,SAAK,QAAQ;AACb,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,KAAK,cAAc;AACjB,iBAAa,UAAU,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,EACnD;AACF;AAEA,IAAM,uBAAuB;AAE7B,IAAM,cAAN,cAA0B,kBAAkB;AAAA,EAC1C,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,oBAAgB,MAAM,UAAU,UAAU;AAAA,EAC5C;AAAA,EACA,MAAM,OAAO;AACX,QAAI,CAAC,KAAK,MAAM;AAAS,WAAK,MAAM,KAAK;AACzC,SAAK,YAAY,KAAK;AACtB,SAAK,aAAa,IAAI,YAAY,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,EAC5D;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,YAAY,KAAK;AAChC,MAAE,MAAM,MAAM,WAAW,MAAM,MAAM;AACrC,uCAAmC,KAAK;AACxC,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,MAAM;AAAS;AACzB,SAAK,MAAM,UAAU;AACrB,SAAK,QAAQ;AACb,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,KAAK,cAAc;AACjB,iBAAa,SAAS,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA,EACjD;AACF;AAEA,IAAM,sBAAsB;AAE5B,IAAM,cAAN,cAA0B,kBAAkB;AAAA,EAC1C,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,oBAAgB,MAAM,UAAU,UAAU;AAAA,EAC5C;AAAA,EACA,MAAM,OAAO;AACX,QAAI,KAAK,OAAO,aAAa,MAAM,gBAAgB;AAAS;AAC5D,SAAK,MAAM,KAAK;AAChB,SAAK,cAAc,cAAc,KAAK,CAAC;AACvC,SAAK,QAAQ,KAAK;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,MAAM,OAAO;AACX,QAAI,KAAK,OAAO,aAAa,MAAM,gBAAgB;AAAS;AAC5D,UAAM,QAAQ,KAAK;AACnB,QAAI,CAAC,MAAM;AAAS;AACpB,UAAM,UAAU;AAChB,UAAM,SAAS,cAAc,KAAK;AAClC,UAAM,YAAY,MAAM,SAAS,EAAE,IAAI,QAAQ,MAAM,OAAO;AAC5D,SAAK,cAAc,MAAM;AACzB,SAAK,QAAQ,KAAK;AAClB,UAAM,QAAQ,MAAM;AACpB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,KAAK,cAAc;AACjB,iBAAa,WAAW,SAAS,KAAK,MAAM,KAAK,IAAI,CAAC;AACtD,iBAAa,WAAW,SAAS,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA,EACxD;AACF;AAEA,IAAM,sBAAsB,eAAe,eAAe,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG;AAAA,EAC5F,WAAW,CAAC,QAAQ,SAAS;AAC/B,CAAC;AAED,IAAM,YAAY,oBAAI,IAAI;AAC1B,IAAM,oBAAoB,oBAAI,IAAI;AAClC,SAAS,eAAe,QAAQ;AAC9B,YAAU,IAAI,OAAO,KAAK,OAAO,MAAM;AACvC,oBAAkB,IAAI,OAAO,KAAK,OAAO,QAAQ;AACnD;AACA,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;;;AC97CA,mBAAkB;;;ACClB,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AACT,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AACA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAC3D,MAAI,KAAK;AACT,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAC1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG;AAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,uBAAuB;AAAA,EAC3B,OAAO,OAAO;AACZ,QAAI,OAAO;AACT,aAAO,MAAM,aAAa,QAAQ,MAAM,UAAU;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,QAAQ,MAAM;AACpB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,QAAQ,YAAY,SAAS,QAAW;AACrD,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,IAAI,CAAC,GAAG;AACN,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,WAAO;AAAA,EACT;AACF;AAEA,IAAM,YAAY,CAAC,UAAU,gBAAgB,UAAU,WAAW,WAAW;AAC7E,SAAS,YAAY,SAAS,CAAC,GAAG,WAAW;AAC3C,QAAM,SAAS,CAAC;AAChB,aAAW,CAAC,KAAK,QAAQ,KAAK,OAAO,QAAQ,SAAS,GAAG;AACvD,YAAQ,OAAO,UAAU;AAAA,MACvB,KAAK;AACH,YAAI,MAAwC;AAC1C,gBAAM,IAAI,SAAS,KAAK,QAAQ,OAAO,GAAG,GAAG,KAAK,MAAM;AACxD,cAAI,CAAC,OAAO,MAAM,CAAC;AAAG,mBAAO,GAAG,IAAI;AAAA,QACtC,OAAO;AACL,iBAAO,GAAG,IAAI,SAAS,KAAK,QAAQ,OAAO,GAAG,GAAG,KAAK,MAAM;AAAA,QAC9D;AACA;AAAA,MACF,KAAK;AACH,eAAO,GAAG,IAAI,YAAY,OAAO,GAAG,GAAG,QAAQ;AAC/C;AAAA,MACF,KAAK;AACH,YAAI;AAAU,iBAAO,GAAG,IAAI,OAAO,GAAG;AACtC;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,MAAM,WAAW,YAAY,UAAU,CAAC,GAAG;AAClD,QAAM,OAAO,WACX;AAAA,IACE;AAAA,IACA;AAAA,IACA,QAAAC;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,MACJ,OAAO,yBAAyB,MAAM,SAAS;AACjD,UAAQ,SAAS,YAAY;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,QAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,oBAAoB;AACvB,MAAI,YAAY;AACd,UAAM,WAAW,kBAAkB,IAAI,UAAU;AACjD,YAAQ,UAAU,IAAI,YAAY,eAAe;AAAA,MAC/C,QAAQ,QAAQ;AAAA,IAClB,GAAG,IAAI,GAAG,QAAQ;AAAA,EACpB,OAAO;AACL,eAAW,OAAO,MAAM;AACtB,YAAM,WAAW,kBAAkB,IAAI,GAAG;AAC1C,UAAI,UAAU;AACZ,gBAAQ,GAAG,IAAI,YAAY,eAAe;AAAA,UACxC,QAAQ,QAAQ;AAAA,QAClB,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ;AAAA,MACzB,WAAW,MAAwC;AACjD,YAAI,CAAC,CAAC,QAAQ,SAAS,UAAU,SAAS,QAAQ,OAAO,EAAE,SAAS,GAAG,GAAG;AACxE,cAAI,QAAQ,aAAa;AACvB,kBAAM,MAAM,sEAAsE;AAAA,UACpF;AACA,kBAAQ,KAAK,wCAAwC,wEAAwE;AAAA,QAC/H;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,MAAM,YAAY;AAC5B,oBAAgB,MAAM,cAAc,oBAAI,IAAI,CAAC;AAC7C,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,IAAI,SAAS,QAAQ,QAAQ,SAAS,SAAS;AAC7C,UAAM,YAAY,KAAK;AACvB,UAAM,OAAO,eAAe,QAAQ,MAAM;AAC1C,UAAM,WAAW,KAAK,cAAc,KAAK,MAAM,OAAO,KAAK,WAAW,EAAE,eAAe,CAAC;AACxF,UAAM,eAAe,eAAe,eAAe,CAAC,GAAG,QAAQ,GAAG,OAAO;AACzE,YAAQ,iBAAiB,MAAM,SAAS,YAAY;AACpD,UAAM,SAAS,MAAM;AACnB,cAAQ,oBAAoB,MAAM,SAAS,YAAY;AACvD,gBAAU,OAAO,MAAM;AAAA,IACzB;AACA,cAAU,IAAI,MAAM;AACpB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,SAAK,WAAW,QAAQ,YAAU,OAAO,CAAC;AAC1C,SAAK,WAAW,MAAM;AAAA,EACxB;AACF;AAEA,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,oBAAgB,MAAM,aAAa,oBAAI,IAAI,CAAC;AAAA,EAC9C;AAAA,EACA,IAAI,KAAK,UAAU,KAAK,QAAQ,MAAM;AACpC,SAAK,OAAO,GAAG;AACf,SAAK,UAAU,IAAI,KAAK,OAAO,WAAW,UAAU,IAAI,GAAG,IAAI,CAAC;AAAA,EAClE;AAAA,EACA,OAAO,KAAK;AACV,UAAM,UAAU,KAAK,UAAU,IAAI,GAAG;AACtC,QAAI;AAAS,aAAO,aAAa,OAAO;AAAA,EAC1C;AAAA,EACA,QAAQ;AACN,SAAK,UAAU,QAAQ,aAAW,KAAK,OAAO,aAAa,OAAO,CAAC;AACnE,SAAK,UAAU,MAAM;AAAA,EACvB;AACF;AAEA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,UAAU;AACpB,oBAAgB,MAAM,YAAY,oBAAI,IAAI,CAAC;AAC3C,oBAAgB,MAAM,qBAAqB,IAAI,WAAW,IAAI,CAAC;AAC/D,oBAAgB,MAAM,sBAAsB,CAAC,CAAC;AAC9C,oBAAgB,MAAM,wBAAwB,CAAC,CAAC;AAChD,oBAAgB,MAAM,YAAY,CAAC,CAAC;AACpC,oBAAgB,MAAM,UAAU,CAAC,CAAC;AAClC,oBAAgB,MAAM,cAAc,oBAAI,IAAI,CAAC;AAC7C,oBAAgB,MAAM,YAAY,oBAAI,IAAI,CAAC;AAC3C,oBAAgB,MAAM,SAAS;AAAA,MAC7B,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AACD,oBAAgB,MAAM,QAAQ;AAAA,EAChC;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,QAAQ,KAAK,GAAG;AAClB,WAAK,WAAW,IAAI,IAAI,SAAS,KAAK,CAAC;AACvC,aAAO,KAAK;AAAA,IACd,WAAW,eAAe,OAAO;AAC/B,UAAI,MAAM,SAAS,eAAe,MAAM,SAAS;AAAiB,aAAK,WAAW,OAAO,MAAM,SAAS;AAAA,eAAW,MAAM,SAAS;AAAe,aAAK,WAAW,IAAI,MAAM,SAAS;AACpL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EACA,cAAc,UAAU,gBAAgB;AACtC,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,YAAY,QAAQ,YAAY;AAC9B,SAAK,SAAS,MAAM,QAAQ,YAAY,KAAK,MAAM;AAAA,EACrD;AAAA,EACA,QAAQ;AACN,SAAK,kBAAkB,MAAM;AAC7B,eAAW,OAAO,KAAK,UAAU;AAC/B,WAAK,mBAAmB,GAAG,EAAE,MAAM;AACnC,WAAK,qBAAqB,GAAG,EAAE,MAAM;AAAA,IACvC;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,KAAK,OAAO,OAAO;AAAQ,WAAK,KAAK;AACzC,WAAO,MAAM,KAAK,kBAAkB,MAAM;AAAA,EAC5C;AAAA,EACA,QAAQ,MAAM;AACZ,UAAM,eAAe,KAAK,OAAO;AACjC,UAAM,QAAQ,CAAC;AACf,QAAI;AACJ,QAAI,aAAa,QAAQ;AACvB,eAAS,aAAa,OAAO;AAC7B,UAAI,CAAC;AAAQ;AAAA,IACf;AACA,QAAI,aAAa,SAAS;AACxB,iBAAW,cAAc,KAAK,UAAU;AACtC,cAAM,gBAAgB,KAAK,OAAO,UAAU;AAC5C,cAAM,eAAe,YAAY,OAAO,cAAc,cAAc,CAAC,CAAC,MAAM;AAC5E,YAAI,cAAc,SAAS;AACzB,gBAAMC,UAAS,UAAU,IAAI,UAAU;AACvC,cAAIA,QAAO,MAAM,MAAM,UAAU,EAAE,KAAK,YAAY;AAAA,QACtD;AAAA,MACF;AACA,YAAM,qBAAqB,YAAY,OAAO,aAAa,cAAc,CAAC,CAAC,MAAM;AACjF,iBAAW,YAAY,KAAK,gBAAgB;AAC1C,2BAAmB,UAAU,IAAI,WAAS,KAAK,eAAe,QAAQ,EAAE,eAAe,eAAe,CAAC,GAAG,KAAK,MAAM,MAAM,GAAG,CAAC,GAAG;AAAA,UAChI;AAAA,UACA;AAAA,QACF,CAAC,CAAC,GAAG,QAAW,IAAI;AAAA,MACtB;AAAA,IACF;AACA,eAAW,eAAe,OAAO;AAC/B,YAAM,WAAW,IAAI,MAAM,GAAG,MAAM,WAAW,CAAC;AAAA,IAClD;AACA,QAAI,CAAC;AAAQ,aAAO;AACpB,eAAW,eAAe,OAAO;AAC/B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,UAAU,WAAW;AACzB,WAAK,kBAAkB,IAAI,QAAQ,QAAQ,IAAI,MAAM,WAAW,GAAG;AAAA,QACjE;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,aAAa,MAAM,YAAY;AACtC,OAAK,SAAS,IAAI,UAAU;AAC5B,OAAK,mBAAmB,UAAU,IAAI,IAAI,WAAW,MAAM,UAAU;AACrE,OAAK,qBAAqB,UAAU,IAAI,IAAI,aAAa;AAC3D;AACA,SAAS,gBAAgB,MAAM,kBAAkB;AAC/C,MAAI,iBAAiB;AAAM,iBAAa,MAAM,MAAM;AACpD,MAAI,iBAAiB;AAAO,iBAAa,MAAM,OAAO;AACtD,MAAI,iBAAiB;AAAQ,iBAAa,MAAM,QAAQ;AACxD,MAAI,iBAAiB;AAAM,iBAAa,MAAM,MAAM;AACpD,MAAI,iBAAiB;AAAO,iBAAa,MAAM,OAAO;AACtD,MAAI,iBAAiB;AAAO,iBAAa,MAAM,OAAO;AACxD;AACA,IAAM,cAAc,CAAC,OAAO,cAAc,sBAAsB,CAAC,QAAQ,QAAQ,SAAS,UAAU,CAAC,GAAG,WAAW,UAAU;AAC3H,MAAI,kBAAkB;AACtB,QAAM,WAAW,mBAAmB,QAAQ,aAAa,QAAQ,qBAAqB,SAAS,mBAAmB,aAAa;AAC/H,QAAM,WAAW,mBAAmB,QAAQ,aAAa,QAAQ,qBAAqB,SAAS,mBAAmB,aAAa;AAC/H,MAAI,cAAc,WAAW,SAAS,cAAc,QAAQ,QAAQ,OAAO;AAC3E,MAAI,qBAAqB;AAAS,mBAAe;AACjD,QAAM,WAAW,IAAI,MAAM,WAAW,KAAK,CAAC;AAC5C,QAAM,WAAW,EAAE,KAAK,OAAO;AACjC;AAEA,IAAM,gBAAgB;AACtB,SAAS,aAAa,WAAW;AAC/B,QAAM,SAAS,CAAC;AAChB,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,oBAAI,IAAI;AACxB,WAAS,OAAO,WAAW;AACzB,QAAI,cAAc,KAAK,GAAG,GAAG;AAC3B,cAAQ,IAAI,OAAO,SAAS;AAC5B,eAAS,GAAG,IAAI,UAAU,GAAG;AAAA,IAC/B,OAAO;AACL,aAAO,GAAG,IAAI,UAAU,GAAG;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,CAAC,UAAU,QAAQ,OAAO;AACnC;AACA,SAAS,gBAAgB,SAAS,UAAU,YAAY,KAAK,kBAAkB,QAAQ;AACrF,MAAI,CAAC,QAAQ,IAAI,UAAU;AAAG;AAC9B,MAAI,CAAC,UAAU,IAAI,GAAG,GAAG;AACvB,QAAI,MAAwC;AAC1C,cAAQ,KAAK,wEAAwE;AAAA;AAAA,eAAgE,yCAAyC;AAAA,IAChM;AACA;AAAA,EACF;AACA,QAAM,WAAW,aAAa;AAC9B,QAAM,SAAS,aAAa;AAC5B,QAAM,KAAK,WAAS;AAClB,QAAI,OAAO;AACX,QAAI,MAAM,SAAS,YAAY;AAAU,eAAS,QAAQ,EAAE,KAAK;AACjE,QAAI,cAAc;AAAU,aAAO,SAAS,UAAU,EAAE,KAAK;AAC7D,QAAI,MAAM,QAAQ,UAAU;AAAU,eAAS,MAAM,EAAE,KAAK;AAC5D,WAAO;AAAA,EACT;AACA,mBAAiB,GAAG,IAAI;AACxB,SAAO,GAAG,IAAI,OAAO,GAAG,KAAK,CAAC;AAChC;AACA,SAAS,oBAAoB,gBAAgB,cAAc;AACzD,QAAM,CAAC,UAAU,gBAAgB,OAAO,IAAI,aAAa,cAAc;AACvE,QAAM,mBAAmB,CAAC;AAC1B,kBAAgB,SAAS,UAAU,UAAU,QAAQ,kBAAkB,YAAY;AACnF,kBAAgB,SAAS,UAAU,WAAW,SAAS,kBAAkB,YAAY;AACrF,kBAAgB,SAAS,UAAU,YAAY,UAAU,kBAAkB,YAAY;AACvF,kBAAgB,SAAS,UAAU,WAAW,SAAS,kBAAkB,YAAY;AACrF,kBAAgB,SAAS,UAAU,UAAU,QAAQ,kBAAkB,YAAY;AACnF,kBAAgB,SAAS,UAAU,WAAW,SAAS,kBAAkB,YAAY;AACrF,SAAO;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,IACR;AAAA,EACF;AACF;;;ADjUA,SAAS,eAAe,UAAU,SAAS,CAAC,GAAG,YAAY,gBAAgB;AACzE,QAAM,OAAO,aAAAC,QAAM,QAAQ,MAAM,IAAI,WAAW,QAAQ,GAAG,CAAC,CAAC;AAC7D,OAAK,cAAc,UAAU,cAAc;AAC3C,OAAK,YAAY,QAAQ,UAAU;AACnC,eAAAA,QAAM,UAAU,KAAK,OAAO,KAAK,IAAI,CAAC;AACtC,eAAAA,QAAM,UAAU,MAAM;AACpB,WAAO,KAAK,MAAM,KAAK,IAAI;AAAA,EAC7B,GAAG,CAAC,CAAC;AACL,MAAI,OAAO,WAAW,QAAW;AAC/B,WAAO,KAAK,KAAK,KAAK,IAAI;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,SAAS,QAAQ;AAChC,iBAAe,UAAU;AACzB,SAAO,eAAe;AAAA,IACpB,MAAM;AAAA,EACR,GAAG,UAAU,CAAC,GAAG,MAAM;AACzB;AAqCA,SAAS,iBAAiB,SAAS;AACjC,UAAQ,QAAQ,cAAc;AAC9B,SAAO,SAASC,YAAW,WAAW,SAAS;AAC7C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,oBAAoB,WAAW,WAAW,CAAC,CAAC;AAChD,WAAO,eAAe,UAAU,QAAQ,QAAW,cAAc;AAAA,EACnE;AACF;AAEA,SAAS,WAAW,UAAU,QAAQ;AACpC,QAAM,OAAO,iBAAiB,CAAC,YAAY,aAAa,cAAc,aAAa,YAAY,WAAW,CAAC;AAC3G,SAAO,KAAK,UAAU,UAAU,CAAC,CAAC;AACpC;;;AE+FA,IAAM,wBAAwB,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;AACvD,QAAM,gBAAgB,IAAI;AAC1B,MAAI,YAAY,CAAC,UAAU,aAAa,YAAY;AAClD,QAAI,WAAW;AACf,QAAI,aAAa;AACf,YAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,eAAe,OAAO;AAC7E,UAAI,eAAe,SAAS,IAAI,SAAS,CAAC;AAC1C,iBAAW,CAAC,UAAU;AACpB,cAAM,YAAY,SAAS,KAAK;AAChC,YAAI,CAAC,WAAW,cAAc,SAAS,GAAG;AACxC,gBAAM,gBAAgB;AACtB,sBAAY,eAAe,WAAW,aAAa;AAAA,QACrD;AAAA,MACF;AACA,UAAI,WAAW,OAAO,SAAS,QAAQ,iBAAiB;AACtD,oBAAY,cAAc,YAAY;AAAA,MACxC;AAAA,IACF;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AACA,QAAM,eAAe,GAAG,KAAK,KAAK,GAAG;AACrC,SAAO;AACT;;;ACnMA,SAAS,QAAQ,MAAM,MAAM;AAC3B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG;AACvG,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;", "names": ["rubberband", "window", "Engine", "React", "useGesture"]}